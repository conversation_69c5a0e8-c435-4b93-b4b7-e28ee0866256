                        -H/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-<PERSON><PERSON><PERSON>OID_PLATFORM=android-21
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/25.1.8937393/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=/Users/<USER>/Downloads/projects/GASECUADOR/build/app/intermediates/cxx/Debug/5939462i/obj/x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Users/<USER>/Downloads/projects/GASECUADOR/build/app/intermediates/cxx/Debug/5939462i/obj/x86_64
-DCMAKE_BUILD_TYPE=Debug
-B/Users/<USER>/Downloads/projects/GASECUADOR/android/app/.cxx/Debug/5939462i/x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2