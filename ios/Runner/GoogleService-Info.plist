<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>819403982266-7hf6iifb9bcgu9di3r2h698qr6as2c62.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.819403982266-7hf6iifb9bcgu9di3r2h698qr6as2c62</string>
	<key>ANDROID_CLIENT_ID</key>
	<string>819403982266-jlgj59alkniobcpqpku508lvanaqk1lf.apps.googleusercontent.com</string>
	<key>API_KEY</key>
	<string>AIzaSyDXzQYmhqpI0v4tAyO9ChcPttF3W8yYfek</string>
	<key>GCM_SENDER_ID</key>
	<string>819403982266</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>com.xcodie.musicnow</string>
	<key>PROJECT_ID</key>
	<string>musicnow-6d640</string>
	<key>STORAGE_BUCKET</key>
	<string>musicnow-6d640.firebasestorage.app</string>
	<key>IS_ADS_ENABLED</key>
	<false></false>
	<key>IS_ANALYTICS_ENABLED</key>
	<false></false>
	<key>IS_APPINVITE_ENABLED</key>
	<true></true>
	<key>IS_GCM_ENABLED</key>
	<true></true>
	<key>IS_SIGNIN_ENABLED</key>
	<true></true>
	<key>GOOGLE_APP_ID</key>
	<string>1:819403982266:ios:bd97d25a8fb682635dc983</string>
	<key>DATABASE_URL</key>
	<string>https://musicnow-6d640-default-rtdb.firebaseio.com</string>
</dict>
</plist>