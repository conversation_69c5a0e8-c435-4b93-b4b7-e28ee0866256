PODS:
  - background_fetch (1.3.8):
    - Flutter
  - CocoaLumberjack (3.8.5):
    - CocoaLumberjack/Core (= 3.8.5)
  - CocoaLumberjack/Core (3.8.5)
  - Flutter (1.0.0)
  - flutter_background_geolocation (4.16.12):
    - CocoaLumberjack (~> 3.8.5)
    - Flutter
  - geocoding_ios (1.0.5):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS

DEPENDENCIES:
  - background_fetch (from `.symlinks/plugins/background_fetch/ios`)
  - Flutter (from `Flutter`)
  - flutter_background_geolocation (from `.symlinks/plugins/flutter_background_geolocation/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)

SPEC REPOS:
  trunk:
    - CocoaLumberjack

EXTERNAL SOURCES:
  background_fetch:
    :path: ".symlinks/plugins/background_fetch/ios"
  Flutter:
    :path: Flutter
  flutter_background_geolocation:
    :path: ".symlinks/plugins/flutter_background_geolocation/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"

SPEC CHECKSUMS:
  background_fetch: 1b98fa3f267413f98d997b82b94698e2574e1c1c
  CocoaLumberjack: 6a459bc897d6d80bd1b8c78482ec7ad05dffc3f0
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_background_geolocation: 67c5caa2e4ebc9ede72d9278e43e0a90d5903713
  geocoding_ios: d7460f56e80e118d57678efe5c2cdc888739ff18
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: 9878588469a2b0d0fc1e048d9f43605f92e6cec2
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78

PODFILE CHECKSUM: 819463e6a0290f5a72f145ba7cde16e8b6ef0796

COCOAPODS: 1.16.2
