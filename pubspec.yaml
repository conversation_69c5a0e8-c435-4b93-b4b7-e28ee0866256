name: music_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.0

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  get: ^4.7.2
  pin_code_fields: ^8.0.1
  animated_splash_screen: ^1.3.0
  flutter_rating_bar: ^4.0.1
  flutter_screenutil: ^5.9.3
  auto_size_text: ^3.0.0
  dotted_border: ^2.1.0
  toggle_switch: ^2.3.0
  dot_navigation_bar: ^1.0.2
  google_maps_flutter: ^2.12.1

  shared_preferences: ^2.5.1
  dio: ^5.8.0+1

  flutter_svg: ^2.0.17
  shimmer: ^3.0.0
  cached_network_image: ^3.4.1
  flutter_advanced_avatar: ^1.5.0
  geolocator: ^13.0.1
  google_places_flutter: ^2.0.9
  flutter_easyloading: ^3.0.3
  geocoding: ^2.0.3
  country_picker: ^2.0.27
  webview_flutter:
  intl: ^0.20.0
  photo_view: ^0.15.0
  change_app_package_name: ^1.5.0

  path_provider: ^2.1.2
  readmore:
  firebase_messaging:
  firebase_core:
  awesome_notifications: ^0.10.1
  google_sign_in: ^6.1.5
  firebase_auth: ^4.10.0
  file_picker: ^9.0.2
  socket_io_client: ^2.0.3+1
  dart_pusher_channels: ^1.2.3
  video_compress: ^3.1.2
  video_player: ^2.7.0
  chewie: ^1.7.0
  awesome_dialog: ^3.2.1
  location: ^8.0.0
  open_file: ^3.3.2
  http: ^1.1.0
  image_cropper: ^9.0.0
  flutter_image_compress: ^2.1.0
  image_picker: ^1.1.2
  url_launcher: ^6.1.14  # Use the latest version
  flutter_launcher_icons: ^0.14.3
dev_dependencies:
  flutter_test:
    sdk: flutter

flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assets/images/appstore.png"
  remove_alpha_ios: true
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  assets:
     - assets/images/onboarding/
     - assets/images/
     - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
     - family: PoppinsBlack

       fonts:
         - asset: assets/fonts/Poppins-Black.ttf


     - family: PoppinsRegular
       fonts:
         - asset: assets/fonts/Poppins-Regular.ttf

     - family: PoppinsBold
       fonts:
         - asset: assets/fonts/Poppins-SemiBold.ttf
           weight: 500

  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
