# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is **USAHS Guard**, a Flutter mobile application for security guard time tracking and attendance management. The app focuses on location-based clock-in/out functionality with background location tracking capabilities.

## Development Commands

### Core Flutter Commands
- **Run the app**: `flutter run`
- **Build for Android**: `flutter build apk` or `flutter build appbundle`
- **Build for iOS**: `flutter build ios`
- **Clean build artifacts**: `flutter clean`
- **Get dependencies**: `flutter pub get`
- **Upgrade dependencies**: `flutter pub upgrade`

### Code Quality
- **Analyze code**: `flutter analyze`
- **Run tests**: `flutter test`
- **Check linting**: Uses `flutter_lints` package with standard Flutter linting rules

### Platform-Specific
- **Android**: Build files in `android/` directory, uses Gradle
- **iOS**: Build files in `ios/` directory, uses CocoaPods (`cd ios && pod install`)

## Architecture Overview

### State Management
- **GetX**: Primary state management solution using controllers
- Controllers follow naming convention: `*Controller` (e.g., `AuthenticationController`, `HomeController`)
- Located in respective feature directories under `lib/view/*/controller/`

### Project Structure
```
lib/
├── controller/           # Global controllers (UserController)
├── controllers/          # Feature-specific controllers
├── core/                # Shared utilities and constants
│   ├── constants/       # API endpoints, colors, assets, padding
│   ├── models/          # Core data models
│   ├── routes/          # App navigation routes
│   ├── services/        # HTTP service layer
│   ├── utils/           # Utilities and extensions
│   └── widgets/         # Reusable UI components
├── model/               # Data models
├── services/            # Location and background services
└── view/                # UI screens organized by feature
    ├── authentication/
    ├── home/
    ├── navigation/
    ├── reports/
    └── starting/
```

### Key Features
1. **Authentication**: Login system with token-based auth
2. **Location Tracking**: Background location services for attendance
3. **Clock In/Out**: Time tracking with location verification
4. **Reports**: Historical attendance data
5. **Navigation**: Bottom navigation with multiple tabs

### Location Services Architecture
- **Background Location**: Uses `flutter_background_geolocation` for reliable continuous tracking
- **Location Repository**: `LocationRepository` in `lib/services/location_repository.dart` - handles location data management
- **Background Service**: `BackgroundLocationService` in `lib/services/background_location_service.dart` - core location tracking logic
- **Automatic Upload**: Built-in HTTP sync for location data to server
- **Battery Optimization**: Handles Android battery optimization and iOS background modes

### API Integration
- **HTTP Service**: Centralized API service using Dio in `lib/core/services/http_service.dart`
- **Base URL**: Configured in `lib/core/constants/api_endpoints.dart`
- **Authentication**: Bearer token-based with automatic logout on 401/402 errors
- **Error Handling**: Standardized error responses through `ServiceResponseModel`

### Dependencies
**Key packages:**
- `get: ^4.6.1` - State management and navigation
- `flutter_background_geolocation: ^4.16.2` - Reliable background location tracking
- `dio: ^5.4.3+1` - HTTP client
- `shared_preferences: ^2.3.5` - Local storage
- `permission_handler: ^12.0.0+1` - Runtime permissions
- `http: ^1.1.0` - HTTP requests for multipart uploads

### Platform Configuration
**Android** (Latest Gradle 8.11 with Kotlin DSL):
- `android/build.gradle.kts`: Project-level configuration with background geolocation repositories
- `android/app/build.gradle.kts`: App-level configuration with NDK 27.0.12077973
- `android/settings.gradle.kts`: Plugin management with updated Android Gradle Plugin 8.7.3
- Location permissions: `ACCESS_FINE_LOCATION`, `ACCESS_BACKGROUND_LOCATION`
- Background geolocation services and boot receiver configuration
- Battery optimization exclusion and notification permissions

**iOS** (`ios/Runner/Info.plist`):
- Location usage descriptions for background tracking
- Background modes: `location`, `background-fetch`, `background-processing`
- Camera and photo library permissions for guard verification

### Navigation
- **GetX Routing**: Defined in `lib/core/routes/app_routes.dart`
- **Main Routes**: Login, Home, Navigation, Clock Progress
- **Bottom Navigation**: Implemented in `lib/view/navigation/view/navigation_bar_view.dart`

### User Management
- **UserController**: Global state management for user data and authentication
- **Token Management**: Automatic token handling and refresh
- **Local Storage**: User data persisted via SharedPreferences

## Development Notes

### Location Services Setup
The app uses `flutter_background_geolocation` for reliable background location tracking. Key files:
- `lib/main.dart`: Initializes location repository and basic permissions
- `lib/services/background_location_service.dart`: Core background location tracking service
- `lib/services/location_repository.dart`: High-level location data management
- Location tracking automatically starts when users clock in and stops when they clock out

### Authentication Flow
1. User login through `LoginView`
2. Token storage via `UserController`
3. API requests include Bearer token automatically
4. Automatic logout on token expiration

### Testing Location Features
- Test on physical devices for accurate location tracking
- Ensure background location permissions are granted
- Verify foreground service notifications appear correctly

### Code Conventions
- Follow standard Flutter/Dart conventions
- Use GetX reactive programming patterns
- Maintain separation between UI, controllers, and services
- Implement proper error handling for API calls
- Use consistent naming for controllers and services