# In-App Purchase Integration Guide

This document provides a comprehensive guide for setting up and testing in-app purchases in the Bananas mobile app.

## Overview

The app now includes complete in-app purchase functionality for both Android and iOS platforms with three subscription tiers:
- Weekly Premium: $4.99/week
- Monthly Premium: $14.99/month  
- Yearly Premium: $99.99/year (Most Popular)

## Architecture

### Core Components

1. **PurchaseService** (`lib/core/services/purchase_service.dart`)
   - Handles all in-app purchase operations
   - Manages product loading and purchase flow
   - Handles purchase verification with backend

2. **PurchaseController** (`lib/controller/purchase_controller.dart`)
   - UI controller for subscription management
   - Manages subscription state and user interactions

3. **SubscriptionModel** (`lib/model/subscription_model.dart`)
   - Data models for subscription plans and status

4. **Updated SubscriptionView** (`lib/view/modules/profile/view/subscription_view.dart`)
   - Enhanced UI with real purchase integration

## Store Configuration

### Android (Google Play Console)

1. **Create Products in Google Play Console:**
   ```
   Product ID: banana_premium_weekly
   Product Type: Subscription
   Price: $4.99
   Billing Period: Weekly
   
   Product ID: banana_premium_monthly  
   Product Type: Subscription
   Price: $14.99
   Billing Period: Monthly
   
   Product ID: banana_premium_yearly
   Product Type: Subscription
   Price: $99.99
   Billing Period: Yearly
   ```

2. **Configure Subscription Groups:**
   - Create a subscription group called "Banana Premium"
   - Add all three products to this group
   - Set upgrade/downgrade paths between plans

3. **Testing Setup:**
   - Add test accounts in Google Play Console
   - Create test purchases for validation

### iOS (App Store Connect)

1. **Create Products in App Store Connect:**
   ```
   Product ID: banana_premium_weekly
   Type: Auto-Renewable Subscription
   Price: $4.99
   Duration: 1 Week
   
   Product ID: banana_premium_monthly
   Type: Auto-Renewable Subscription  
   Price: $14.99
   Duration: 1 Month
   
   Product ID: banana_premium_yearly
   Type: Auto-Renewable Subscription
   Price: $99.99
   Duration: 1 Year
   ```

2. **Configure Subscription Groups:**
   - Create subscription group "Banana Premium"
   - Add all products to the group
   - Configure subscription levels and upgrade paths

3. **Testing Setup:**
   - Create sandbox test accounts
   - Configure test environment

## Backend Integration

### Required API Endpoints

The following endpoints need to be implemented on your backend:

1. **POST /verify_purchase**
   ```json
   {
     "access_token": "user_access_token",
     "product_id": "banana_premium_yearly",
     "purchase_token": "purchase_verification_data",
     "platform": "ios" | "android"
   }
   ```

2. **POST /get_subscription_status**
   ```json
   {
     "access_token": "user_access_token"
   }
   ```

3. **POST /update_subscription**
   ```json
   {
     "access_token": "user_access_token",
     "subscription_data": {...}
   }
   ```

### Purchase Verification

- **Android**: Verify purchase tokens with Google Play Developer API
- **iOS**: Verify receipts with Apple's App Store Server API
- Store subscription status in your database
- Handle subscription renewals and cancellations

## Testing

### Development Testing

1. **Android Testing:**
   ```bash
   # Use test accounts configured in Google Play Console
   # Test with debug builds
   flutter run --debug
   ```

2. **iOS Testing:**
   ```bash
   # Use sandbox environment with test accounts
   # Test with debug builds on physical device
   flutter run --debug
   ```

### Test Scenarios

1. **Purchase Flow:**
   - Select different subscription plans
   - Complete purchase process
   - Verify subscription activation

2. **Restore Purchases:**
   - Test restore functionality
   - Verify previous purchases are restored

3. **Subscription Management:**
   - Test subscription status checking
   - Verify subscription expiry handling

4. **Error Handling:**
   - Test network failures
   - Test cancelled purchases
   - Test invalid products

## Usage

### Initializing Purchase Service

The purchase service is automatically initialized in `main.dart`:

```dart
// Initialize purchase service
Get.put(PurchaseService());
```

### Using in UI

```dart
// Get purchase controller
final purchaseController = Get.put(PurchaseController());

// Check if purchases are available
if (purchaseController.isPurchaseServiceAvailable) {
  // Show subscription options
}

// Purchase a plan
await purchaseController.purchaseSelectedPlan();

// Restore purchases
await purchaseController.restorePurchases();

// Check subscription status
await purchaseController.checkSubscriptionStatus();
```

## Security Considerations

1. **Receipt Validation:**
   - Always verify purchases on your backend
   - Never trust client-side purchase validation alone

2. **Subscription Status:**
   - Regularly sync subscription status with stores
   - Handle subscription state changes (renewals, cancellations)

3. **Error Handling:**
   - Implement proper error handling for all purchase flows
   - Provide clear user feedback for failures

## Troubleshooting

### Common Issues

1. **Products Not Loading:**
   - Verify product IDs match store configuration
   - Check app bundle ID matches store setup
   - Ensure products are active in store

2. **Purchase Failures:**
   - Check network connectivity
   - Verify store account setup
   - Check app permissions

3. **Verification Failures:**
   - Verify backend endpoints are working
   - Check purchase token/receipt format
   - Ensure proper API credentials

### Debug Information

Enable debug logging in development:

```dart
// In purchase_service.dart
log('Purchase debug info: $debugInfo');
```

## Production Deployment

### Pre-Launch Checklist

1. **Store Configuration:**
   - [ ] Products created and active
   - [ ] Subscription groups configured
   - [ ] Pricing and availability set

2. **App Configuration:**
   - [ ] Product IDs match store setup
   - [ ] Backend endpoints implemented
   - [ ] Purchase verification working

3. **Testing:**
   - [ ] All purchase flows tested
   - [ ] Restore purchases working
   - [ ] Error handling verified

4. **Security:**
   - [ ] Receipt validation implemented
   - [ ] Secure API endpoints
   - [ ] Proper error handling

### Launch Steps

1. Submit app for review with in-app purchases
2. Ensure all products are approved
3. Monitor purchase analytics
4. Handle customer support for purchase issues

## Support

For issues with in-app purchases:

1. Check store-specific documentation
2. Review purchase logs and analytics
3. Test with different devices and accounts
4. Contact platform support if needed

## References

- [Flutter In-App Purchase Plugin](https://pub.dev/packages/in_app_purchase)
- [Google Play Billing](https://developer.android.com/google/play/billing)
- [Apple In-App Purchase](https://developer.apple.com/in-app-purchase/)
