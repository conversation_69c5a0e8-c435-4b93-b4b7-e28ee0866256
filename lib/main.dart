import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app/data/services/storage_service.dart';
import 'app/data/services/localization_service.dart';
import 'app/core/theme/app_theme.dart';
import 'app/routes/app_pages.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize services
  await Get.putAsync(() => StorageService().init());
  Get.put(LocalizationService());

  runApp(const SunalyzeApp());
}

class SunalyzeApp extends StatelessWidget {
  const SunalyzeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      title: 'Sunalyze',
      theme: AppTheme.lightTheme,
      initialRoute: AppPages.INITIAL,
      getPages: AppPages.routes,
      debugShowCheckedModeBanner: false,
    );
  }
}
