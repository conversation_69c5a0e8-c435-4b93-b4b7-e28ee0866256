import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';

import 'package:guardtracker/view/starting/splash_view.dart';
import 'package:permission_handler/permission_handler.dart' as pm;

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  await _requestPermissions();

  runApp(const SplashView());
}

Future<void> _requestPermissions() async {
  // Request notification permission

  final notificationPermission =
      await FlutterForegroundTask.checkNotificationPermission();
  if (notificationPermission != NotificationPermission.granted) {
    await FlutterForegroundTask.requestNotificationPermission();
  }

  if (Platform.isAndroid) {
    // Request battery optimization exemption
    if (!await FlutterForegroundTask.isIgnoringBatteryOptimizations) {
      await FlutterForegroundTask.requestIgnoreBatteryOptimization();
    }
  }
  if (Platform.isAndroid) {
    pm.PermissionStatus bgStatus = await pm.Permission.locationAlways.status;
    if (bgStatus.isDenied) {
      bgStatus = await pm.Permission.locationAlways.request();
    }
  }
}
