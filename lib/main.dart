import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/routes/app_routes.dart';
import 'package:music_app/view/client/view/subscription/view/client_subscription_view.dart';
import 'package:music_app/view/musician/create_musician_profile/musician_profile_screen/view/musician_profile_screen.dart';


import 'package:music_app/view/starting/splash_view.dart';

import 'view/musician/subscription/controller/subscription_view_controller.dart';


Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  AwesomeNotifications().createNotificationFromJsonData(message.data);

  print('Handling a background message: ${message.messageId}');
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await SystemChrome.setPreferredOrientations(
    [DeviceOrientation.portraitUp],
  );
  await Firebase.initializeApp();
  AwesomeNotifications().initialize(
    // Set the icon and configure channels in this method
      null, // Use default icon or specify a custom one
      [
        NotificationChannel(
          channelKey: 'default_channel_id',
          channelName: 'Basic notifications',
          channelDescription: 'Notification channel for basic tests',
          defaultColor: ColorConstants.primaryColor,
          ledColor: Colors.white,
          importance: NotificationImportance.High,
        ),
      ],
      debug: false);

  AwesomeNotifications().isNotificationAllowed().then((isAllowed) {
    if (!isAllowed) {
      AwesomeNotifications().requestPermissionToSendNotifications();
    }
  });
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {


    return ScreenUtilInit(
      designSize: const Size(360, 690),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (_ , child) {
        return GetMaterialApp(    builder: EasyLoading.init(),
           getPages: AppRoutes.routes,
          defaultTransition: Transition.rightToLeft,
          debugShowCheckedModeBanner: false,
          title: 'First Method',
          theme: ThemeData(fontFamily: "PoppinsRegular",
            primarySwatch: Colors.blue,primaryColor: ColorConstants.primaryColor,scaffoldBackgroundColor: Colors.white,

          ),
               home: child,



        );
      },
       child: SplashView(),
      // child: OfferedServices(),
    );
  }
}