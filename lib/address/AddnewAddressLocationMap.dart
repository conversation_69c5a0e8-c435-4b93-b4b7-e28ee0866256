// ignore_for_file: depend_on_referenced_packages, prefer_typing_uninitialized_variables, use_build_context_synchronously

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:banana/api_services/ToastShow.dart';
import 'package:banana/constants/my_colors.dart';
import 'package:banana/constants/my_fonts.dart';
import 'package:banana/constants/my_images.dart';
import 'package:banana/constants/sized_box.dart';
import 'package:banana/core/widgets/custom_button.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/my_string.dart';
import '../core/constants/assets_constants.dart';
import '../core/widgets/text_widgets.dart';

class SelectLocationView extends StatefulWidget {
  final Function oncallback;

  // ignore: non_constant_identifier_names
  double? userLat;
  double? userLng;

  SelectLocationView({
    super.key,
    required this.oncallback,
    required this.userLat,
    required this.userLng,
  });
  @override
  State<SelectLocationView> createState() => _SelectLocationViewState();
}

class _SelectLocationViewState extends State<SelectLocationView> {
  Completer<GoogleMapController> controller = Completer();
  double? latback;
  double? longback;
  Set<Marker> markers = {};
  final double _zoom = 17.0;
  bool addresslistShowHide = false;
  List<dynamic> _placeList = [];
  String sessionToken = "";
  Position? _currentPosition;

  String? _currentAddress;


  double longitude = -97.4369639530778;

  late LocationPermission permission;
  bool servicestatus = false;
  bool haspermission = false;
  double latitude = 45.657893757769145;

  String governorate = "";
  String area = "";
  String block = "";
  String street = "";
  String address = "";
  var kInitialPosition;

  bool isSearch = false;

  bool cameraMove = false;
  TextEditingController addressControl = TextEditingController();
  FocusNode focusNode = FocusNode();
  bool isLoadingLocation = false;

  @override
  void initState() {
    super.initState();
    isSearch = true;
    setState(() {});
    debugPrint("user_lat${widget.userLat}");
    debugPrint("user_lng${widget.userLng}");
    kInitialPosition = LatLng(latitude, longitude);

    // Delay to ensure map is properly initialized before moving camera
    Future.delayed(Duration(milliseconds: 500), () {
      if (widget.userLat == null || widget.userLng == null ||
          widget.userLat == 0.0 || widget.userLng == 0.0) {
        animateToCurrentLocation(); // Use the more robust method
      } else {
        mapData(widget.userLat!, widget.userLng!, "");
      }
    });
  }


  ///Location
  Future<void> checkGps() async {
    servicestatus = await Geolocator.isLocationServiceEnabled();
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    if (servicestatus) {
      permission = await Geolocator.checkPermission();

      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          haspermission = false;
          sharedPreferences.setBool("haspermission", haspermission);
          debugPrint('Location permissions are denied');
          setState(() {});
        } else if (permission == LocationPermission.deniedForever) {
          haspermission = false;
          sharedPreferences.setBool("haspermission", haspermission);

          debugPrint("'Location permissions are permanently denied");
          setState(() {});
        } else {
          haspermission = true;
          sharedPreferences.setBool("haspermission", haspermission);
          setState(() {});
        }
      } else {
        haspermission = true;
        sharedPreferences.setBool("haspermission", haspermission);
        setState(() {});
      }

      if (haspermission) {
        getCurrentLocation();
        setState(() {});
      }
    } else {
      debugPrint("GPS Service is not enabled, turn on GPS location");
    }

    setState(() {
      //refresh the UI
    });
  }

  getCurrentLocation() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    _currentPosition = await Geolocator.getCurrentPosition();
_controller?.animateCamera(CameraUpdate.newCameraPosition(
  CameraPosition(
    bearing: 0,
    target: LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
    zoom: 18.0,
  ),
));
    debugPrint("longitude>>Know${_currentPosition!.longitude}");
    debugPrint("latitude>>>KnoiwGGOO${_currentPosition!.latitude}");

    longitude = _currentPosition!.longitude;
    latitude = _currentPosition!.latitude;

    try {
      String host = 'https://maps.google.com/maps/api/geocode/json';
      final url =
          '$host?key=${"AIzaSyBCu6CV7TvuXAOELvB-bZ8SFkIlLeliuxw"}&language=en&latlng=$latitude,$longitude';
      debugPrint("urlAddress>>>$url");
      if (longitude != null) {
        var response = await http.get(Uri.parse(url));
        if (response.statusCode == 200) {
          Map data = jsonDecode(response.body);
          debugPrint("response ====getAddressFromLatLn ${data["results"][0]}");
          String formattedAddress = data["results"][0]["formatted_address"];
          debugPrint("response ====getAddressFromLatLng $formattedAddress");

          setState(() {
            _currentAddress = data["results"][0]["formatted_address"];
            sharedPreferences.setString(
                "address_Name", _currentAddress.toString());
            sharedPreferences.setDouble("lat_current", latitude);
            sharedPreferences.setDouble("lng_current", longitude);
            kInitialPosition = LatLng(latitude, longitude);
            widget.userLat = latitude;
            widget.userLng = longitude;
            addressControl.text = _currentAddress.toString();
            debugPrint("_currentAddress>> $_currentAddress");
            setState(() {});
          });
          return formattedAddress;
        } else {
          return null;
        }
      } else {
        return null;
      }
    } catch (e) {
      debugPrint("Error fetching address: $e");
    }
  }

  void mapData(double lattitude1, double longitude1, String address1) async {
    isSearch = true;
    setState(() {});

    /// long = longitude1;
    address = address1;
    addressControl.text = address1;

    kInitialPosition = LatLng(lattitude1, longitude1);

    // Move camera to the specified location
    if (_controller != null) {
      await _controller!.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            target: LatLng(lattitude1, longitude1),
            zoom: 18.0,
            bearing: 0,
          ),
        ),
      );

      // Update markers
      markers.clear();
      markers.add(
        Marker(
          markerId: const MarkerId('selectedLocation'),
          position: LatLng(lattitude1, longitude1),
          infoWindow: InfoWindow(title: address1.isNotEmpty ? address1 : "Selected Location"),
        ),
      );
    }

    setState(() {});

    Future.delayed(const Duration(seconds: 2), () {
      isSearch = false;
      setState(() {});
    });

    debugPrint("issearc>>$isSearch");
  }

  String permissionStatus = "";

  void _currentLocation() async {
    _controller!.animateCamera(CameraUpdate.newCameraPosition(
      CameraPosition(
        bearing: 0,
        target: LatLng(latitude, longitude),
        zoom: 18.0,
      ),
    ));
  }

  GoogleMapController? _controller;
  void _onMapCreated(GoogleMapController controller) {
    _controller = controller;
  }

  Future<void> animateToCurrentLocation() async {
    setState(() {
      isLoadingLocation = true;
    });

    try {
      // Check if location service is enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ToastShowAll.showToastMethod("Please enable location services");
        return;
      }

      // Check and request location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ToastShowAll.showToastMethod("Location permission denied");
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ToastShowAll.showToastMethod("Location permissions are permanently denied");
        return;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      setState(() {
        latitude = position.latitude;
        longitude = position.longitude;
        _currentPosition = position;
        kInitialPosition = LatLng(latitude, longitude);
      });

      // Animate camera to current position
      if (_controller != null) {
        await _controller!.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: LatLng(position.latitude, position.longitude),
              zoom: 18.0,
            ),
          ),
        );
      }

      // Get address from coordinates
      String host = 'https://maps.google.com/maps/api/geocode/json';
      final url = '$host?key=${"AIzaSyBCu6CV7TvuXAOELvB-bZ8SFkIlLeliuxw"}&language=en&latlng=${position.latitude},${position.longitude}';

      var response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        Map data = jsonDecode(response.body);
        if (data["results"].isNotEmpty) {
          setState(() {
            _currentAddress = data["results"][0]["formatted_address"];
            addressControl.text = _currentAddress ?? "";
          });

          // Save to SharedPreferences
          SharedPreferences prefs = await SharedPreferences.getInstance();
          await prefs.setString("address_Name", _currentAddress ?? "");
          await prefs.setDouble("lat_current", position.latitude);
          await prefs.setDouble("lng_current", position.longitude);

          // Update markers
          markers.clear();
          markers.add(
            Marker(
              markerId: const MarkerId('currentLocation'),
              position: LatLng(latitude, longitude),
              infoWindow: InfoWindow(title: _currentAddress ?? "Current Location"),
            ),
          );

          // Update callback values
          latback = position.latitude;
          longback = position.longitude;
        }
      }
    } catch (e) {
      debugPrint("Error moving to current location: $e");
      ToastShowAll.showToastMethod("Failed to get current location");
    } finally {
      setState(() {
        isLoadingLocation = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final mediaQueryData = MediaQuery.of(context);
    return MediaQuery(
      data: mediaQueryData.copyWith(textScaler: const TextScaler.linear(1.0)),
      child: Scaffold(

        extendBody: true,
        extendBodyBehindAppBar: true,
        body: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Stack(
                children: [
                  SizedBox(
                    height: MediaQuery.of(context).size.height,

                    //   margin: EdgeInsets.only(top: MediaQuery.of(context).size.height /2

                    child: GoogleMap(
                      mapType: MapType.normal,
                        mapToolbarEnabled: true,
                        indoorViewEnabled: true,
                        scrollGesturesEnabled: true,
                        markers: Set.from(markers),
                        gestureRecognizers: <Factory<
                            OneSequenceGestureRecognizer>>{
                          Factory<OneSequenceGestureRecognizer>(
                            () => EagerGestureRecognizer(),
                          ),
                        },
                        initialCameraPosition:_currentPosition == null
                            ? CameraPosition(
                                target: kInitialPosition,
                                zoom: 18.0,
                              )
                            : CameraPosition(
                                target: LatLng(
                                    _currentPosition!.latitude,
                                    _currentPosition!.longitude),
                                zoom: 18.0,
                              ),
                        onCameraMove: (position) {
                          cameraMove = true;
                          setState(() {});
                          debugPrint(
                              "latlong move======${position.target.latitude}");
                          debugPrint(
                              "latlong move444======${position.target.longitude}");

                          setState(() => _currentPosition = Position(
                              longitude: latitude,
                              latitude: longitude,
                              timestamp: DateTime.now(),
                              accuracy: 0.0,
                              altitude: 0.0,
                              heading: 0.0,
                              speed: 0.0,
                              speedAccuracy: 0.0,
                              altitudeAccuracy: 0.0,
                              headingAccuracy: 0.0));
                          if (isSearch == false) {
                            // GetAddressFromLatLong(lat, lon);
                            debugPrint("issd");

                            setState(() {});
                          } else {
                            debugPrint("else");
                          }
                        },
                        onCameraIdle: () async {
                          isSearch = false;
                          cameraMove = false;
                          setState(() {});
                          LatLngBounds bounds =
                              await _controller!.getVisibleRegion();
                          final lon = (bounds.northeast.longitude +
                                  bounds.southwest.longitude) /
                              2;
                          final lat = (bounds.northeast.latitude +
                                  bounds.southwest.latitude) /
                              2;

                          debugPrint("jdjfk>>$lon  6666666$lat");
                          latback = lat;
                          longback = lon;
                          getAddressFromLatLng(context, lat, lon);
                          setState(() {});
                        },
                        onMapCreated: _onMapCreated),
                  ),

                  // Loading indicator
                  if (isLoadingLocation)
                    Container(
                      height: MediaQuery.of(context).size.height,
                      width: MediaQuery.of(context).size.width,
                      color: Colors.black.withOpacity(0.3),
                      child: Center(
                        child: Container(
                          padding: EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CircularProgressIndicator(
                                color: MyColors.colorFF0004,
                              ),
                              SizedBox(height: 16),
                              Texts.textMedium(
                                "Getting your location...",
                                color: Colors.black87,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                  // Location pin in center
                  Positioned.fill(
                    top: 50,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Container(
                            margin: cameraMove
                                ? const EdgeInsets.fromLTRB(0, 0, 0, 20)
                                : const EdgeInsets.fromLTRB(0, 0, 0, 0),
                            child: Icon(
                              Icons.location_on,
                              size: 50,
                              color: MyColors.colorFF0004,
                            )),
                        Container(
                          margin: cameraMove
                              ? const EdgeInsets.fromLTRB(0, 0, 0, 0)
                              : const EdgeInsets.fromLTRB(0, 0, 0, 0),
                          decoration: BoxDecoration(
                            color: cameraMove
                                ? Colors.transparent
                                : MyColors.colorFF0004,
                            border: Border.all(
                              color: cameraMove
                                  ? Colors.transparent
                                  : MyColors.colorFF0004,
                            ),
                            borderRadius:
                                const BorderRadius.all(Radius.circular(3)),
                            boxShadow: cameraMove
                                ? [
                                    const BoxShadow(
                                      color: MyColors.colorFF0004,
                                      blurRadius: 5.0, // soften the shadow
                                      spreadRadius: 3.0,
                                      offset: Offset(
                                        0.5,
                                        0.5,
                                      ),
                                    )
                                  ]
                                : [
                                    const BoxShadow(
                                      color: Colors.transparent,
                                      blurRadius: 5.0, // soften the shadow
                                      spreadRadius: 3.0, //extend the shadow
                                      offset: Offset(
                                        0.5,
                                        0.5,
                                      ),
                                    )
                                  ],
                          ),
                          width: cameraMove ? 9 : 6,
                          height: cameraMove ? 0.6 : 6,
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    child: Container(
                      child: Container(
                        alignment: Alignment.centerLeft,
                        margin: EdgeInsets.only(top: 50,left: 15,right: 15),
                        padding: EdgeInsets.only(left: 15,right: 15),

                        decoration: BoxDecoration(
                          color: Colors.white,
                          border: Border.all(
                            color: Colors.grey.shade300,
                          ),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(8)),

                        ),
                        child: Row(crossAxisAlignment: CrossAxisAlignment.center,
                          children: [                                    InkWell(

                          onTap: () {
                            Get.back();
                          },
                          child: Icon(Icons.arrow_back,size: 25,)),   SizedBox(width: 10,),

                            Expanded(
                              child: TextField(
                                textAlign: TextAlign.start,
                                onChanged: (content) {
                                  debugPrint("on change>>>$content");

                                  getSuggestion(content);
                                },

                                onTap: () {
                                  addresslistShowHide = false;
                                  addressControl.text = "";
                                  setState(() {});
                                },
                                controller: addressControl,
                                decoration: const InputDecoration(
                                  //enabled: false,
                                  border: InputBorder.none,

                                  contentPadding:
                                      EdgeInsets.only(left: 10, top: 5),
                                ),
                                style: const TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontFamily: MyFonts.robotoRegular,
                                    fontStyle: FontStyle.normal,
                                    fontSize: 13.0),
                                keyboardType: TextInputType.text,
                                cursorColor: MyColors.colorBlack,
                              ),
                            ),

                          ],
                        ),
                      ),
                    ),
                  ),
                  addresslistShowHide
                      ? Container(
                          color: Colors.transparent,
                          height: 2,
                        )
                      : Container(
                          width: double.infinity,
                          margin: const EdgeInsets.fromLTRB(21, 110, 21, 0),
                          color: Colors.white,
                          child: ListView.builder(
                            //  physics: NeverScrollableScrollPhysics(),
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            itemCount: _placeList.length,
                            itemBuilder: (context, index) {
                              debugPrint(
                                  "_placeList.length${_placeList.length}");
                              return ListTile(dense: true,
                                selectedTileColor: Colors.brown,
                                hoverColor: Colors.grey,
                                title: Texts.textMedium(_placeList[index]["description"],size: 12),
                                onTap: () async {
                                  focusNode.unfocus();
                                  debugPrint("tab===${_placeList[index]}");
                                  addressControl.text = _placeList[index]
                                          ["description"]
                                      .toString();
                                  _currentAddress = _placeList[index]
                                          ["description"]
                                      .toString();
                                  addresslistShowHide = true;
                                  getLatLongFromAutocompletePrediction(
                                      _placeList[index]['place_id']);

                                  setState(() {});
                                },
                              );
                            },
                          )),

                  Positioned(bottom: 0,left: 0,right: 0,child: Container(
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(15),
                        topRight: Radius.circular(15),
                      ),
                    ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(left: 15, top: 15),
              child: Row(
                children: [
                  Texts.textSemiBold(
                    MyString.locations,
                  ),

                ],
              ),
            ),
            Container(
                margin: EdgeInsets.only(left: 15, top: 5),
                child: Texts.textMedium(
                  addressControl.text,
                  size: 12,
                )),
            Padding(
              padding: const EdgeInsets.all(15.0),
              child: CustomButton(
                padding: 10,
                fontSize: 12,
                label:MyString.saveLocation,onTap: (){


                Get.back();
                widget.oncallback(latback, longback, _currentAddress);
                setState(() {});
              },),
            ),
          ],
        ),
      ))
                ],
              ),


            ],
          ),
        ),
      ),
    );
  }

  getAddressFromLatLng(context, double lat, double lng) async {
    String host = 'https://maps.google.com/maps/api/geocode/json';
    final url =
        '$host?key=${"AIzaSyBCu6CV7TvuXAOELvB-bZ8SFkIlLeliuxw"}&language=en&latlng=$lat,$lng';

    if (lng != null) {
      var response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        Map data = jsonDecode(response.body);

        debugPrint("response ====getAddressFromLatLn ${data["results"][0]}");
        String formattedAddress = data["results"][0]["formatted_address"];
        debugPrint("response ====getAddressFromLatLng $formattedAddress");

        setState(() {
          _currentAddress = data["results"][0]["formatted_address"];
          addressControl.text = _currentAddress!;
        });

        return formattedAddress;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  static final String androidKey = "AIzaSyBCu6CV7TvuXAOELvB-bZ8SFkIlLeliuxw";
  static final String iosKey = "AIzaSyBCu6CV7TvuXAOELvB-bZ8SFkIlLeliuxw";
  final apiKey = Platform.isAndroid ? androidKey : iosKey;

  //Map Api Call
  void getSuggestion(String input) async {
    String kplacesApiKey = "AIzaSyBCu6CV7TvuXAOELvB-bZ8SFkIlLeliuxw";
    String baseURL =
        'https://maps.googleapis.com/maps/api/place/autocomplete/json';
    String request =
        '$baseURL?input=$input&key=$kplacesApiKey&sessiontoken=$sessionToken';

    //String request = '$baseURL?input=$input&key=$kPLACES_API_KEY&components=country:${"IN"}&language=en';
    var response = await http.get(Uri.parse(request));
    if (response.statusCode == 200) {
      setState(() {
        _placeList = json.decode(response.body)['predictions'];
      });
    } else {
      throw Exception('Failed to load predictions');
    }
  }

  getCenter() async {
    LatLngBounds bounds = await _controller!.getVisibleRegion();
    LatLng center = LatLng(
      (bounds.northeast.latitude + bounds.southwest.latitude) / 2,
      (bounds.northeast.longitude + bounds.southwest.longitude) / 2,
    );

    return center;
  }

  void getLatLongFromAutocompletePrediction(String placeId) async {
    String baseURL =
        'https://maps.googleapis.com/maps/api/place/details/json?place_id=$placeId&key=$apiKey';
    var response = await http.get(Uri.parse(baseURL));
    if (response.statusCode == 200) {
      double lat =
          json.decode(response.body)['result']['geometry']['location']['lat'];
      double long =
          json.decode(response.body)['result']['geometry']['location']['lng'];
      latback = lat;
      longback = long;
      latitude = lat;
      longitude = long;
      setState(() => _currentPosition = Position(
          longitude: long,
          latitude: lat,
          timestamp: DateTime.now(),
          accuracy: 0.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0));
      setState(() => _currentPosition = Position(
          longitude: json.decode(response.body)['result']['geometry']
              ['location']['lng'],
          latitude: json.decode(response.body)['result']['geometry']['location']
              ['lat'],
          timestamp: DateTime.now(),
          accuracy: 0.0,
          altitude: 0.0,
          heading: 0.0,
          speed: 0.0,
          speedAccuracy: 0.0,
          altitudeAccuracy: 0.0,
          headingAccuracy: 0.0));
      setState(() {
        kInitialPosition = LatLng(latitude, longitude);
        _currentLocation();
      });
    } else {
      throw Exception('Failed to load predictions');
    }
  }
}
