import 'package:banana/core/widgets/custom_dropdown.dart';
import 'package:banana/view/authentication/controller/authentication_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../constants/my_string.dart';
import '../../../../core/constants/assets_constants.dart';
import '../../../../core/widgets/entry_field.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../../../../utils/my_navigations.dart';

class CreateEventPartThreeView extends StatelessWidget {
  AuthenticationController authenticationController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textBold("Kick things off with your first event?",
            size: 27, maxline: 2, align: TextAlign.start, color: Colors.black),
        Widgets.heightSpaceH2,
        Texts.textMedium(
            "Start strong! Add your first event now or skip to set it up later from your dashboard.",
            align: TextAlign.start,
            color: Colors.black54,
            size: 13),
        Widgets.heightSpaceH3,
        Center(child:Image.asset(
          Assets.organizerEvent, // Replace with your asset
          height: .38.sh,
        ),
        )

      ],
    );
  }
}
