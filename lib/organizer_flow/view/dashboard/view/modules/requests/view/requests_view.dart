// ignore_for_file: depend_on_referenced_packages, deprecated_member_use

import 'package:banana/constants/my_colors.dart';
import 'package:banana/constants/my_fonts.dart';
import 'package:banana/constants/my_images.dart';
import 'package:banana/constants/my_string.dart';
import 'package:banana/constants/sized_box.dart';
import 'package:banana/core/constants/color_constants.dart';
import 'package:banana/response/GetHomeDataResponse.dart';
import 'package:banana/utils/Utility.dart';
import 'package:banana/view/modules/home/<USER>/home_controller.dart';
import 'package:banana/view/modules/requests/model/request_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:readmore/readmore.dart';

import '../../../../../../../core/constants/assets_constants.dart';
import '../../../../../../../core/widgets/custom_button.dart';
import '../../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../../core/widgets/widgets.dart';
import '../../../../../../../view/modules/home/<USER>/events_detail_view.dart';
import '../../../../../../../view/modules/people/controller/people_controller.dart';
import '../../../../../../../view/modules/people/model/people_model.dart';
import '../../../../../../../view/modules/people/view/people_detail_view.dart';
import '../../../../../../../view/modules/profile/view/private_tryst_request_view.dart';

import '../controller/request_controller.dart';

class OrganizerRequestView extends StatefulWidget {
  const OrganizerRequestView({super.key});

  @override
  State<OrganizerRequestView> createState() => _OrganizerRequestViewState();
}

class _OrganizerRequestViewState extends State<OrganizerRequestView>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late OrganizerRequestController requestController;
  @override
  void initState() {
    super.initState();
    requestController = Get.find();
    requestController.fetchRequestsBackground();
    _tabController = TabController(length: 2, vsync: this);
  }

  List<DeclineRequest> askOutDeclineReasons = <DeclineRequest>[];
  getAskOutReasons() {
    askOutDeclineReasons.clear();
    askOutDeclineReasons
        .add(DeclineRequest("Timing doesn’t work for me.", false));
    askOutDeclineReasons
        .add(DeclineRequest("Not interested, but thanks!", false));

  }

  List<DeclineRequest> eventDeclineReasons = <DeclineRequest>[];
  getEventReasons() {
    eventDeclineReasons.clear();
    eventDeclineReasons.add(DeclineRequest("Sorry, the event is full", false));
    eventDeclineReasons.add(
        DeclineRequest("Plans changed, won’t be hosting this event.", false));
    eventDeclineReasons
        .add(DeclineRequest("This event is invite-only.", false));

  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          centerTitle: true,
          automaticallyImplyLeading: false,
          title: Texts.textBold(
            "Requests",
            size: 23,
          ),
        ),
      body:  buildEventRequests()
    );
  }

  buildEventRequests() {
    return Obx(() {
      if (requestController.isRequestsLoading.value == true) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0),
          child: ShimmerListSkeleton(),
        );
      }

      return RefreshIndicator(backgroundColor: Colors.black,color: ColorConstants.primaryColor,
        onRefresh: () async {
          await requestController.fetchRequests();
        },
        child: requestController.requests.isEmpty
            ? SingleChildScrollView(
                physics: AlwaysScrollableScrollPhysics(),
                child: Container(
                  height: MediaQuery.of(context).size.height - 200,
                  padding: EdgeInsets.only(top: 110, left: 15, right: 15),
                  child: Column(
                    children: [
                      Widgets.heightSpaceH2,
                      Image.asset(Assets.eventNo),
                      Widgets.heightSpaceH1,
                      Texts.textBold("No Requests Found!", size: 20),
                      Widgets.heightSpaceH1,
                    ],
                  ),
                ),
              )
            : ListView.separated(
                physics: const AlwaysScrollableScrollPhysics(),
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),

                itemBuilder: (context, index) {
                  AttendesRequest requestCard = requestController.requests[index];
                  if (requestCard.event?.eventType == "Event") {
                    return InkWell(
                      onTap: () {},
                      child: requestEventCard(requestCard: requestCard),
                    );
                  }
                  return SizedBox.shrink();
                },
                separatorBuilder: (context, index) {
                  return Widgets.heightSpaceH2;
                },
                itemCount: requestController.requests.length),
      );
    });
  }


  Widget requestEventCard({
    required AttendesRequest requestCard
  }) {
    return Container(
      padding: EdgeInsets.all(15.0),
      margin: EdgeInsets.only(bottom: 13),
      decoration: Widgets.blockDecoration,
      child:    Row(
        children: [
          GestureDetector(
            onTap: () {},
            child: AdvancedAvatar(
              animated: true,
              decoration: BoxDecoration(
                color: ColorConstants.textColor,
                shape: BoxShape.circle,
              ),
              size: 45,
              foregroundDecoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color: ColorConstants.primaryColor,
                  width: 0.0,
                ),
              ),
              child: Widgets.networkImage(
                requestCard.user?.profileThumbnail??"",
                width: 60,
                height: 60,

              ),
            ),
          ),
          SizedBox(width: 5),
          // Name and Age
          Expanded(
            child:  Texts.textBlock(fontWeight: FontWeight.w600,
                color: ColorConstants.blackColor,
                "${requestCard.user?.name??""}, ${requestCard.user?.age??""}",
                size: 14,
                maxline: 2),
          ),
          requestCard.status == "Reject" ? SizedBox() : SizedBox(width: 8),
          if (requestCard.status == "Pending review") ...[

            Row(children: [

              InkWell(
                onTap: () async {

                  requestController.acceptRequestForAttendsApi(context,requestCard.attendeeId.toString());

                },
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 15,vertical: 5),

                  decoration: BoxDecoration(color:ColorConstants.primaryColor,borderRadius: BorderRadius.circular(25) ),
                  child: Texts.textNormal("Accept",size: 12),
                ),
              ), SizedBox(
                width: 5,
              ),
              InkWell(
                  onTap: () async {
                    var result=await  askOutDeclineRequest(context);
                    if(result!=null){


                      requestController.cancelRequestForAttendsApi(context, result,requestCard.attendeeId.toString());
                    }
                  },
                  child: CircleAvatar(
                    radius: 15,
                    child: Icon(
                      Icons.clear,
                      color: Colors.white,
                      size: 15,
                    ),
                    backgroundColor: ColorConstants.redColor,
                  )),


            ]),
          ]
        ],
      ),
    
    );
  }

  Future<void> showDetailDialog(BuildContext context, bool isEvent,AttendesRequest requestCard) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            backgroundColor: Colors.white,
            content: SingleChildScrollView(
              child: Column(
                children: [
                  Align(
                      alignment: Alignment.topRight,
                      child: InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Icon(
                            CupertinoIcons.clear_circled_solid,
                            color: Colors.black,
                          ))),
                  Widgets.heightSpaceH1,
                  isEvent
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                              Texts.textBlock("Music ",
                                  size: 10, color: Colors.black),
                              SizedBox(
                                height: 5,
                              ),
                              Texts.textBlock(
                                  fontWeight: FontWeight.w600,
                                  color: ColorConstants.blackColor,
                                  "Neon Party Night",
                                  size: 14,
                                  maxline: 2),
                              Divider(
                                  color: ColorConstants.lightGrey,
                                  thickness: .5),
                              Widgets.heightSpaceH1,
                            ])
                      : SizedBox(),
                  Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                            onTap: () {},
                            child: Icon(
                              Icons.calendar_month,
                              color: Colors.black,
                              size: 19,
                            ),
                          ),
                          SizedBox(width: 8),
                          Flexible(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Texts.textBlock(
                                    fontWeight: FontWeight.w600,
                                    color: ColorConstants.blackColor,
                                    requestCard.event?.startDate ?? "",
                                    size: 14,
                                    maxline: 2),
                                SizedBox(
                                  height: 2,
                                ),
                                Texts.textBlock(requestCard.event?.datetimeStr ?? "",
                                    size: 13, color: Colors.black87),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Widgets.heightSpaceH1,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {},
                            child: Icon(
                              CupertinoIcons.location_solid,
                              color: Colors.black,
                              size: 18,
                            ),
                          ),
                          SizedBox(width: 8),
                          Flexible(
                            child: Texts.textBlock(
                                fontWeight: FontWeight.w400,
                                color: Colors.black87,
                                requestCard.event?.locationName ?? "",
                                size: 14,
                                maxline: 2),
                          ),
                        ],
                      ),
                      Widgets.heightSpaceH2,
                      Texts.textMedium(
                         requestCard.event?.description ?? "",
                          size: 14,
                          color: Colors.black54,
                          align: TextAlign.start),
                    ],
                  ),
                ],
              ),
            ),
          );
        });
  }


  askOutDeclineRequest(BuildContext context) async {
    getAskOutReasons(); // Initialize the decline request list
    int? selectedReason;

    return showDialog<String?>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          insetPadding: const EdgeInsets.symmetric(horizontal: 15),
          backgroundColor: Colors.white,
          content: SizedBox(
            width: double.maxFinite,
            child: SingleChildScrollView(
              child: StatefulBuilder(
                builder: (BuildContext context, StateSetter setState) {
                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Widgets.heightSpaceH2,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Texts.textBold(
                              "Let them know why you're declining.",
                              size: 20,
                              maxline: 2,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          SizedBox(width: 10),
                          InkWell(
                              onTap: () => Navigator.pop(context),
                              child: Icon(
                                CupertinoIcons.clear_circled_solid,
                                color: Colors.black,
                              ))
                        ],
                      ),
                      Widgets.heightSpaceH1,
                      SizedBox(
                        height: .34.sh,
                        child: ListView.builder(
                          itemCount: askOutDeclineReasons.length,
                          itemBuilder: (context, index) {
                            return Container(
                              margin: EdgeInsets.only(top: 10),
                              padding: EdgeInsets.only(left: 15, right: 15),
                              height: 40,
                              decoration: BoxDecoration(
                                  color: askOutDeclineReasons[index].reasonSe ==
                                          true
                                      ? ColorConstants.lightPrimaryColor
                                      : ColorConstants.lightGrey,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(12.0)),
                                  border: Border.all(
                                      color: askOutDeclineReasons[index]
                                                  .reasonSe ==
                                              true
                                          ? ColorConstants.blackColor
                                          : Colors.white,
                                      width: 1)),
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    // Reset all selections
                                    for (var request in askOutDeclineReasons) {
                                      request.reasonSe = false;
                                    }
                                    // Set selected item
                                    askOutDeclineReasons[index].reasonSe = true;
                                    selectedReason =index+1;
                                  });

                                  debugPrint(
                                      "selectLookingForList>>>  authenticationControllerselectLookingForList");
                                  setState(() {});
                                },
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Texts.textNormal(
                                      askOutDeclineReasons[index]
                                          .reasonName
                                          .toString(),
                                      color: Colors.black,
                                      size: 12,
                                    ),
                                    SvgPicture.asset(
                                        askOutDeclineReasons[index].reasonSe ==
                                                true
                                            ? Assets.selectedItem
                                            : Assets.unselectedItem,
                                        width: 16,
                                        height: 16)
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      CustomButton(
                          label: "Submit",
                          onTap: () {
                            Navigator.pop(context, selectedReason.toString());
                          }),
                      Widgets.heightSpaceH1,
                    ],
                  );
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
