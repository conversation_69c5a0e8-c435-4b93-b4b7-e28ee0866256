import 'dart:io';

import 'package:banana/controller/user_controller.dart';
import 'package:banana/core/utils/extensions.dart';
import 'package:banana/core/widgets/custom_button.dart';
import 'package:banana/core/widgets/custom_dropdown.dart';
import 'package:banana/view/authentication/controller/authentication_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../../../../address/AddnewAddressLocationMap.dart';
import '../../../../../../../constants/my_string.dart';
import '../../../../../../../core/constants/color_constants.dart';
import '../../../../../../../core/widgets/entry_field.dart';
import '../../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../../core/widgets/widgets.dart';
import '../../../../../../../utils/my_navigations.dart';


class OrganizerEditProfileView extends StatefulWidget {
  const OrganizerEditProfileView({super.key});

  @override
  State<OrganizerEditProfileView> createState() => _OrganizerEditProfileViewState();
}

class _OrganizerEditProfileViewState extends State<OrganizerEditProfileView> {
  AuthenticationController authenticationController = Get.find();
  final picker = ImagePicker();
  File? image;
  String img = "";
  Future<void> pickImageOrganizer(ImageSource source) async {

    final pickedFile = await picker.pickImage(source: source);
    if (pickedFile != null) {
      // Show cropping option
      File originalFile = File(pickedFile.path);
      final croppedFile = await ImageCropper().cropImage(
        sourcePath: originalFile.path,
        aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1), // Square aspect ratio for profile
        compressQuality: 70,
        uiSettings: [
          AndroidUiSettings(
            toolbarTitle: 'Crop Image',
            toolbarColor: ColorConstants.primaryColor,
            toolbarWidgetColor: Colors.white,
            initAspectRatio: CropAspectRatioPreset.square,
            lockAspectRatio: true,
          ),
          IOSUiSettings(
            title: 'Crop Image',
            aspectRatioLockEnabled: true,
            resetAspectRatioEnabled: false,
          ),
        ],
      );
      if(croppedFile!=null){
        File? compressedFile = await authenticationController.compressImage(File(pickedFile.path));
        if (compressedFile != null) {
        authenticationController.editOrganizerProfilePicture(compressedFile.path);
        }
      }

    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    authenticationController.nameController.text=Get.find<UserController>().userModel?.name??"";
    authenticationController.whoeWeAre.text=Get.find<UserController>().userModel?.aboutMe??"";

}
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
       context.hideKeyboard();
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
            centerTitle: true,
            automaticallyImplyLeading: true,
            elevation: 0,
            backgroundColor: Colors.white,
            title: Texts.textBold("Edit Profile", size: 18, color: Colors.black)),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [


              Texts.textBlock("Upload Logo or Profile Image:",
                  size: 12, align: TextAlign.center, color: Colors.black),
              SizedBox(
                height: 4,
              ),
              GestureDetector(onTap: (){
                pickImageOrganizer(ImageSource.gallery);
              },
                child: GetBuilder<UserController>(
                      builder: (UserController controller) {return ClipRRect(borderRadius: BorderRadius.circular(15),
                        child: Widgets.networkImage(
                                          controller.userModel?.profileThumbnail ?? "",
                                          width: 100,
                                          height: 100),
                      );  },
                ),
              ),
              Widgets.heightSpaceH2,
              EntryField(
                controller: authenticationController.nameController,
                label: "Organizer Name",
                hint: MyString.enterName,
                textCapitalization: TextCapitalization.words,
              ),

             EntryBigField(
                controller: authenticationController.whoeWeAre,
                label: "Tell the people what to expect?",
                hint: MyString.enterName,minLines: 10,maxLines: null,
                textCapitalization: TextCapitalization.words,
              ),
              CustomButton(label:"Save Changes",onTap:(){
                authenticationController.updateOrganizerProfileApiFromProfile();
              },)
            ],
          ),
        ),
      ),
    );
  }
}
