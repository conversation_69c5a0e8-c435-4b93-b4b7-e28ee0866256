import 'dart:developer';

import 'package:avatar_stack/avatar_stack.dart';
import 'package:avatar_stack/positions.dart';
import 'package:banana/core/widgets/recieved_bubble_chat.dart';
import 'package:banana/view/modules/people/controller/people_controller.dart';
import 'package:banana/view/modules/people/view/people_detail_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:image_picker/image_picker.dart';

import 'package:intl/intl.dart';


import '../../../../../../../core/constants/assets_constants.dart';
import '../../../../../../../core/constants/color_constants.dart';
import '../../../../../../../core/services/socket_service.dart';
import '../../../../../../../core/utils/utils.dart';
import '../../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../../core/widgets/widgets.dart';
import '../../../../../../../view/modules/inbox/model/message_model.dart';
import '../../../../../../../view/modules/people/model/people_model.dart';
import '../controller/chat_controller.dart';

class OrganizerChatView extends StatefulWidget {
  const OrganizerChatView({
    Key? key,
  }) : super(key: key);

  @override
  _OrganizerChatViewState createState() => _OrganizerChatViewState();
}

class _OrganizerChatViewState extends State<OrganizerChatView> {
  final TextEditingController message = TextEditingController();
  late OrganizerChatController chatController;

  @override
  void initState() {
    super.initState();
    chatController = Get.find();
    chatController.initializeSocketListeners(
        chatController.selectedChat.value.roomId ?? "");
    chatController.fetchMessages();


  }





  @override
  void dispose() {
    SocketService.disconnect();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: false,
        elevation: 0,
        backgroundColor: Colors.white,
        scrolledUnderElevation: 0,
        automaticallyImplyLeading: false,
        title: Column(
          children: [
            Row(children: [
              InkWell(
                onTap: () {
                  Get.back();
                },
                child: const Icon(Icons.arrow_back_outlined, size: 20),
              ),
              Widgets.widthSpaceW2,
              InkWell(
                onTap: () {},
                child: Row(
                  children: [
                    chatController.selectedChat.value.isGroup==true? SizedBox(height: 33,width: 50,
                      child: AvatarStack(
                        settings: RestrictedPositions(
                          maxCoverage: 0.7,
                          minCoverage: 0.2,
                          align: StackAlign.left,
                          infoIndent: 15,
                        ),
                        height: 33,
                        avatars: [
                          for (var image in chatController.selectedChat.value.userInfo!.thumbnails!)
                            if (image.isNotEmpty)
                              NetworkImage(image),
                        ],
                      ),
                    ):  InkWell(

                        onTap: (){

                          Get.find<PeopleController>().peopleCard.value=People(userId:chatController.selectedChat.value.userInfo?.userId);
                          Get.find<PeopleController>().fetchProfileDetails();
                          Get.to(()=>PeopleDetailView());
                        },
                        child: AdvancedAvatar(
                      animated: true,
                      size: 35,
                      foregroundDecoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.grey,
                          width: 0.0,
                        ),
                      ),
                      child: Widgets.networkImage(
                          chatController.selectedChat.value.userInfo
                                  ?.profileThumbnail ??
                              "",
                          width: 100,
                          height: 100),
                    )),
                    Widgets.widthSpaceW2,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Texts.textSemiBold(
                            chatController.selectedChat.value.roomName??
                                "",
                            size: 15,
                            color: Colors.black),
                        SizedBox(
                          height: 4,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Icon(Icons.alarm,
                                size: 11, color: ColorConstants.greenColor),
                            SizedBox(
                              width: 3,
                            ),
                            Texts.textNormal(
                                DateTime.parse(chatController.selectedChat.value.expireTime ?? DateTime.now().toString()).isBefore(DateTime.now())
                                    ? "Expired"
                                    : "Expire in ${Utils.timeAgo(chatController.selectedChat.value.expireTime ?? "")}",
                                color: DateTime.parse(chatController.selectedChat.value.expireTime ?? DateTime.now().toString()).isBefore(DateTime.now())
                                    ? Colors.red
                                    : Colors.black87,
                                size: 9)
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ]),
            Divider(color: ColorConstants.lightGrey, thickness: .5),
          ],
        ),
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(child: buildMessages()),
            buildMessageInput(),
          ],
        ),
      ),
    );
  }

  Widget buildMessages() {
    return Obx(
      () {
        return Scrollbar(
          child: ListView(
            shrinkWrap: true,
            physics: const BouncingScrollPhysics(),
            controller: chatController.scrollMessageController,
            reverse: true,
            padding: const EdgeInsets.all(10.0),
            children: [
              if (chatController.isMessagesLoadingMore.value)
                CircularProgressIndicator(),
              chatController.isMessagesLoading.value
                  ? Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: const ShimmerListSkeleton(),
                    )
                  : chatController.messages.isNotEmpty
                      ? ListView.builder(
                          physics: const NeverScrollableScrollPhysics(),
                          reverse: true,
                          shrinkWrap: true,
                          itemCount: chatController.messages.length,
                          itemBuilder: (context, index) {
                            Message message = chatController.messages[index];
                            Message? previousMessage =
                                index < chatController.messages.length - 1
                                    ? chatController.messages[index + 1]
                                    : null;

                            Widget messageWidget =message.messageType=="system"?Text(

                                  message.message??"",
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 12,
                                fontFamily: "LatoRegular",
                              ),textAlign: TextAlign.center,
                            ): chatController
                                        .userController.userModel?.userId
                                        .toString() ==
                                    message.senderId
                                ? SentMessage(message: message)
                                : chatController.selectedChat.value.isGroup==true?ReceivedBubbleChatGroup(
                              message: message,
                              onAvatarTap: () {

                                Get.find<PeopleController>().peopleCard.value=People(userId:int.parse(message.senderId??"0") );
                                Get.find<PeopleController>().fetchProfileDetails();
                                Get.to(()=>PeopleDetailView());
                              },
                            ):ReceivedBubbleChat(
                                    message: message,
                                    onAvatarTap: () {},
                                  );

                            if (shouldShowTimeDivider(
                                message, previousMessage)) {
                              return Column(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 16),
                                    child: Center(
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 12, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: ColorConstants.greyColor
                                              .withOpacity(0.3),
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Text(
                                          getMessageDateDivider(
                                              message.timestamp ?? ""),
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                            fontFamily: "LatoRegular",
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  messageWidget,
                                ],
                              );
                            }

                            return messageWidget;
                          },
                        )
                      : Padding(
                          padding: EdgeInsets.only(bottom: .4.sh),
                          child:
                              Widgets.noRecordsFound(title: "No messages yet")),
            ],
          ),
        );
      },
    );
  }

  shouldShowTimeDivider(Message currentMessage, Message? previousMessage) {
    if (previousMessage == null) return true;

    DateTime currentTime = parseDateString(currentMessage.timestamp!);
    DateTime previousTime = parseDateString(previousMessage.timestamp!);

    return !Utils.isSameDay(currentTime, previousTime);
  }

  DateTime parseDateString(String dateStr) {
    try {
      // First try parsing with DateFormat
      return DateFormat("dd/MM/yyyy hh:mm a").parse(dateStr);
    } catch (e) {
      try {
        // If that fails, try standard DateTime.parse
        return DateTime.parse(dateStr);
      } catch (e) {
        // If all parsing fails, return current time
        print('Error parsing date: $dateStr');
        return DateTime.now();
      }
    }
  }

  String getMessageDateDivider(String dateTime) {
    DateTime messageDate = parseDateString(dateTime);
    DateTime now = DateTime.now();

    if (Utils.isSameDay(messageDate, now)) {
      return "Today";
    } else if (Utils.isSameDay(
        messageDate, now.subtract(const Duration(days: 1)))) {
      return "Yesterday";
    } else {
      return DateFormat("MMMM dd, yyyy").format(messageDate);
    }
  }

  Widget buildMessageInput() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        color: ColorConstants.lightGrey,
      ),
      padding: const EdgeInsets.only(right: 8.0, left: 8.0),
      margin: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: message,
              maxLines: null,
              keyboardType: TextInputType.multiline,
              style: const TextStyle(
                color: Colors.black87,
                fontFamily: "sansRegular",
              ),
              decoration: InputDecoration(
                hintText: "Message..",
                fillColor: ColorConstants.lightGrey,
                filled: true,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
              ),
            ),
          ),
          InkWell(
            onTap: () {
              if (message.text.trim().isNotEmpty) {
                // Send message via socket
                SocketService.sendMessage({
                  'access_token':
                      chatController.userController.accessToken.value,
                  'action': 'sendMessage',
                  'message': message.text.trim(),
                  'roomId': chatController.selectedChat.value.roomId ?? ""
                });

                // Add temporary message to UI
                final tempMessage = Message(
                  id: DateTime.now().millisecondsSinceEpoch.toString(),
                  message: message.text.trim(),
                  senderId: chatController.userController.userModel?.userId
                      .toString(),
                  timestamp:
                      DateFormat("dd/MM/yyyy hh:mm a").format(DateTime.now()),
                  roomId: chatController.selectedChat.value.roomId,
                  isLoading: true, // Mark as temporary
                );

                chatController.messages.insert(0, tempMessage);

                // Clear the input field
                message.clear();

                // Scroll to bottom
                chatController.scrollMessageController.animateTo(
                  0,
                  duration: const Duration(milliseconds: 300),
                  curve: Curves.easeOut,
                );
              }
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: ColorConstants.primaryColor,
                shape: BoxShape.circle,
              ),
              child: Image.asset(Assets.sendIcon,
                  width: 20, height: 20, color: Colors.black),
            ),
          ),
        ],
      ),
    );
  }
}
