import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../../../core/constants/color_constants.dart';
import '../../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../../core/widgets/widgets.dart';
import '../../../../../../../view/modules/inbox/model/chat_model.dart';
import '../../../../../../../view/modules/inbox/view/chat_view.dart';


import '../controller/chat_controller.dart';

import 'organizer_chat_view.dart';

class OrganizerInboxView extends StatefulWidget {
  OrganizerInboxView({super.key});

  @override
  State<OrganizerInboxView> createState() => _OrganizerInboxViewState();
}

class _OrganizerInboxViewState extends State<OrganizerInboxView> {
  late OrganizerChatController chatController;
  @override
  void initState() {
    super.initState();
    chatController = Get.put(OrganizerChatController());
    chatController.fetchChatsBackground();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        centerTitle: true,
        automaticallyImplyLeading: false,
        title: Texts.textBold(
          "Inbox",
          size: 23,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 5),
        child: buildChats(),
      ),
    );
  }

  Widget buildChats() {
    return RefreshIndicator(
      color: ColorConstants.primaryColor,
      backgroundColor: ColorConstants.secondaryColor,
      onRefresh: () async {
        await chatController.fetchChats();
      },
      child: Obx(
            () {
          if (chatController.isLoading.value) {
            return ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                ShimmerListSkeleton(),
              ],
            );
          } else if (chatController.chats.isEmpty) {
            return ListView(
              physics: const AlwaysScrollableScrollPhysics(),
              children: [
                Container(
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: Center(
                    child: Widgets.noRecordsFound(title: "No chats found"),
                  ),
                ),
              ],
            );
          } else {
            return ListView.separated(
              physics: const AlwaysScrollableScrollPhysics(),
              padding: EdgeInsets.zero,
              itemBuilder: (context, index) {
                Chat chat = chatController.chats[index];
                return InkWell(
                  onTap: () {if( !DateTime.parse(chat.expireTime ?? DateTime.now().toString()).isBefore(DateTime.now()))

                    {
                      chatController.selectedChat.value = chat;
                      Get.to(() => OrganizerChatView());
                    }

                  },
                  child: Widgets.chatCard(chat),
                );
              },
              separatorBuilder: (context, index) {
                return Divider(color: ColorConstants.lightGrey, thickness: .5);
              },
              itemCount: chatController.chats.length,
            );
          }
        },
      ),
    );
  }
}
