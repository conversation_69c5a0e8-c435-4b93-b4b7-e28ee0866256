import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:banana/organizer_flow/view/dashboard/view/modules/home/<USER>/dashboard_controller.dart';
import 'package:banana/view/modules/people/controller/people_controller.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';


import '../../../../controller/user_controller.dart';
import '../../../../core/constants/assets_constants.dart';
import '../../../../core/constants/color_constants.dart';

import '../controller/NavigationController.dart';

import 'modules/requests/controller/request_controller.dart';


class OrganizerNavigationView extends StatefulWidget {
  OrganizerNavigationView({super.key, required this.currentIndex}) ; int currentIndex;
  @override
  State<OrganizerNavigationView> createState() => _OrganizerNavigationViewState();
}

class _OrganizerNavigationViewState extends State<OrganizerNavigationView> {
  final OrganizerNavigationController controller = Get.put(OrganizerNavigationController());
  late DashboardController homeController;
  late OrganizerRequestController requestController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {

      AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: DateTime.now().millisecondsSinceEpoch ~/
              1000,
          channelKey: 'default_channel_id',
          title: message.notification?.title ?? 'Default Title',
          body: message.notification?.body ?? 'Default Body',
          notificationLayout: NotificationLayout.Default,

        ),
      );
    });
    homeController=Get.put(DashboardController());

    requestController=Get.put(OrganizerRequestController());

    initMethod();

  }


  initMethod() async {

    await Get.find<UserController>().updateFcmToken();
    await Get.find<UserController>().fetchProfileDetailsBackground();


    homeController.fetchStats();
    homeController.fetchPostedEvents();
    requestController.fetchRequests();


  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (controller.selectedIndex.value != 0) {
          controller.changeIndex(0);
          return false;
        }
        return true;
      },
      child: Scaffold(

        body: Obx(() => controller.pages[controller.selectedIndex.value]),
        bottomNavigationBar: Obx(
              () => BottomNavigationBar(
            backgroundColor: Colors.white,
            elevation: 10,
            selectedLabelStyle: TextStyle(
              fontSize: 11,
              fontFamily: "sansRegular",
              color: ColorConstants.blackColor,
              fontWeight: FontWeight.w500,
            ),
            unselectedFontSize: 11,
            unselectedLabelStyle: const TextStyle(
              fontSize: 11,
              fontFamily: "sansRegular",
              color: Colors.black45,
            ),
            type: BottomNavigationBarType.fixed,
            selectedItemColor: Colors.black,
            unselectedItemColor: Colors.black45,
            currentIndex: controller.selectedIndex.value,
            onTap: controller.changeIndex,
            items: [
              _navBarItem(Assets.homeIcon, 'Home', 0),

              _navBarItem(Assets.requestsIcon, 'Requests', 1),
              _navBarItem(Assets.chatIcon, 'Inbox', 2),
              _navBarItem(Assets.settingsIcon, 'Settings', 3),
            ],
          ),
        ),
      ),
    );
  }


  BottomNavigationBarItem _navBarItem(String icon, String label, int index) {
    return BottomNavigationBarItem(
      icon: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(2),
            child: Obx(
                  () => ColorFiltered(
                colorFilter: ColorFilter.mode(
                  controller.selectedIndex.value == index
                      ? ColorConstants.blackColor
                      : Colors.black54,
                  BlendMode.srcIn,
                ),
                child: Image.asset(
                  icon,
                  height: 25,
                  width: 25,
                ),
              ),
            ),
          ),
          // if (index == 1) // Inbox tab
          //   Positioned(
          //     right: 0,
          //     top: 0,
          //     child: Obx(() {
          //       final count = 0;
          //       return count > 0
          //           ? CircleAvatar(radius: 7,
          //         backgroundColor: Colors.red,
          //         child: Text(
          //           count > 9 ? '9+' : count.toString(),
          //           style: const TextStyle(
          //             color: Colors.white,
          //             fontSize: 7,
          //             fontWeight: FontWeight.bold,
          //           ),
          //           textAlign: TextAlign.center,
          //         ),
          //       )
          //           : const SizedBox.shrink();
          //     }),
          //   ),
        ],
      ),
      label: label,
    );
  }



}
