class Endpoints{


  static const String baseUrl="https://madw81f995.execute-api.us-east-1.amazonaws.com/prod/";
  static const String socketBaseUrl="wss://bg33ruq2gf.execute-api.us-east-1.amazonaws.com/demo";
  static const String updateOrganizerProfileApi="update_org_profile";
  static const String signupApi="signup";
  static const String verifyApi="verify";
  static const String updateProfileApi="update_profile";
  static const String loginApi="send_login_code";
  static const String getOnboardingContentApi="${baseUrl}get_onboarding_content";
  static const String getDropDownDataCreateProfileApi="get_dd_options";
  static const String updateProfilePicture="upload_image";
  static const String getMyProfileApi="get_my_profile";
  static const String logoutApi="logout";  static const String deleteAccountApi="delete_account";

  static const String createEventApi="create_event";
  static const String getPostedEventsApi="get_posted_events"; //HomeUpcomingEventsApi
  static const String editEventApi="edit_event"; //HomeUpcomingEventsApi
  static const String getEventTrystDetailsApi="get_event_details";
  static const String requestAttendApi="attend";
  static const String editEventTrystApi="edit_event";//Event/Tryst
  static const String getHomeEventsApi="get_events";//Event/Tryst
  static const String listAttendanceReqApi="list_attendance";//Event/Tryst
  static const String getMyNotifications="get_my_notifications";//Event/Tryst
  static const String cancelEvent="cancel_event";//Event/Tryst
  static const String searchPeopleApi="search_users";
  static const String gerUserThought="get_user_thought";
  static const String getThoughts="get_thoughts";

  static const String askOutApi="askout";
  static const String getAttendance="list_attendance";
  static const String updateThought="update_thought";
  static const String getSuggested="get_suggested";
  static const String updateFCM="add_notification_endpoint";
  static const String makeFollowApi="follow";
  static const String deleteProfilePicture="delete_image";
  static const String chatMessages="chat-get-messages";
  static const String activateAccount="activate";
  static const String deActivateAccount="deactivate";
  static const String respondAttendanceApi="respond_attendance";
  static const String rateEvent="rate";
  static const String getStats="get_organizer_stats";
  static const String cancelAttendanceApi="cancel_attendance";
  static const String eventImages="get_event_images";
  static const String profileImages="get_profile_images";
  static const String chatRooms="chat-get-rooms";
  static const String getAttendes="get_attendees";
  static const String appFeedback="app_feedback";
  static const String updateOrganizerSettings="organizer_setting";
  static const String searchApi="search_auto_complete";
  // In-app purchase endpoints
  static const String verifyPurchase="verify_purchase";
  static const String getSubscriptionStatus="get_subscription_status";
  static const String updateSubscription="update_subscription";

  // Report endpoints
  static const String reportUser="report";
}