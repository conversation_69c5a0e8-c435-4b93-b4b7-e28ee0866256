import 'assets_constants.dart';

class Data
{
  static List groupMemberTabs=['Nearby','Joined','Favorites'];
  static List chatsTabs=['Groups','Friends'];

  static List groupLeaderTabs=['Nearby','Joined','My Groups','Favorites'];
  static List datesItems = ["Today", "Tomorrow", "This Week", "This Month"];

  static iconFromIndex(int index) {
    switch (index) {
      case 0:
        return '🎨';  // Design
      case 1:
        return '🎶';  // Music
      case 2:
        return '🎨';  // Art (could be represented with same as Design, or you can change)
      case 3:
        return '🏈';  // Sports (Football)
      case 4:
        return '🍔';  // Food
      case 5:
        return '⚪';  // Others (Dot)
      case 6:
        return '⚽';  // Soccer
      case 7:
        return '🏀';  // Basketball
      case 8:
        return '🎾';  // Tennis
      case 9:
        return '🏐';  // Volleyball
      case 10:
        return '⚾';  // Baseball
      case 11:
        return '🎱';  // Pool
      case 12:
        return '🍸';  // Clubs
      case 13:
        return '🏞️';  // Hiking
      case 14:
        return '🚴';  // Biking
      case 15:
        return '🚗';  // Cars
      case 16:
        return '✈️';  // Traveling
      case 17:
        return '🔫';  // Firearms
      case 18:
        return '🏠';  // Real Estate
      case 19:
        return '💼';  // Business
      case 20:
        return '🎮';  // Video Games
      case 21:
        return '🔧';  // Engineering
      case 22:
        return '💘';  // Dating
      case 23:
        return '🥋';  // Martial Arts
      case 24:
        return '🔬';  // Science
      case 25:
        return '♈';  // Astrology (Zodiac Sign)
      case 26:
        return '🕊️';  // Spirituality
      case 27:
        return '💃';  // Dancing
      case 28:
        return '⛵';  // Boating
      case 29:
        return '🧠';  // Mental Health
      case 30:
        return '🍻';  // Socializing
      case 31:
        return '🏓';  // Pickleball
      case 32:
        return '🎞️';  // Horror Movies
      case 33:
        return '🎧';  // Music Production
      case 34:
        return '🐶';  // Animals
      case 35:
        return '🌃';  // Nightlife
      case 36:
        return '🎶';  // Concerts/Festivals
      case 37:
        return '🧘‍♂️';  // Yoga
      case 38:
        return '🍻';
      case 39:
        return '🥷'; // Bar Crawls
      default:
        return '⚪';  // Default to a dot
    }
  }
  static labelFromIndex(int index) {
    switch (index) {
      case 0:
        return 'Design';
      case 1:
        return 'Music';
      case 2:
        return 'Art';
      case 3:
        return 'Sports';
      case 4:
        return 'Food';
      case 5:
        return 'Others';
      case 6:
        return 'Soccer';
      case 7:
        return 'Basketball';
      case 8:
        return 'Tennis';
      case 9:
        return 'Volleyball';
      case 10:
        return 'Baseball';
      case 11:
        return 'Pool';
      case 12:
        return 'Clubs';
      case 13:
        return 'Hiking';
      case 14:
        return 'Biking';
      case 15:
        return 'Cars';
      case 16:
        return 'Traveling';
      case 17:
        return 'Firearms';
      case 18:
        return 'Real Estate';
      case 19:
        return 'Business';
      case 20:
        return 'Video Games';
      case 21:
        return 'Engineering';
      case 22:
        return 'Dating';
      case 23:
        return 'Martial Arts';
      case 24:
        return 'Science';
      case 25:
        return 'Astrology';
      case 26:
        return 'Spirituality';
      case 27:
        return 'Dancing';
      case 28:
        return 'Boating';
      case 29:
        return 'Mental Health';
      case 30:
        return 'Socializing';
      case 31:
        return 'Pickleball';
      case 32:
        return 'Movies';
      case 33:
        return 'Music Production';
      case 34:
        return 'Animals';
      case 35:
        return 'Nightlife';
      case 36:
        return 'Concerts';
      case 37:
        return 'Yoga';
      case 38:
        return 'Bar Crawls';
      case 39:
        return 'Anime';
      default:
        return 'Unknown';
    }
  }

}