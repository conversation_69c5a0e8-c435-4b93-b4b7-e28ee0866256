import 'dart:io';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../widgets/widgets.dart';

class UrlLauncherService {
  /// Launch a phone call
  static Future<void> launchPhoneCall(String phoneNumber) async {
    // Remove any non-digit characters from the phone number
    final cleanedNumber = phoneNumber.replaceAll(RegExp(r'\D'), '');
    
    if (cleanedNumber.isEmpty) {
      Widgets.showSnackBar('Error', 'Invalid phone number');
      return;
    }
    
    final Uri uri = Uri.parse('tel:$cleanedNumber');
    await _launchUrl(uri);
  }

  /// Launch an email client
  static Future<void> launchEmail(String email, {String subject = '', String body = ''}) async {
    if (email.isEmpty || !email.contains('@')) {
      Widgets.showSnackBar('Error', 'Invalid email address');
      return;
    }
    
    final Uri uri = Uri(
      scheme: 'mailto',
      path: email,
      queryParameters: {
        if (subject.isNotEmpty) 'subject': subject,
        if (body.isNotEmpty) 'body': body,
      },
    );
    
    await _launchUrl(uri);
  }

  /// Launch a website URL
  static Future<void> launchWebsite(String url) async {
    if (url.isEmpty) {
      Widgets.showSnackBar('Error', 'Invalid URL');
      return;
    }
    
    // Add https:// if the URL doesn't start with http:// or https://
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }
    
    try {
      final Uri uri = Uri.parse(url);

      // Check if the URL can be launched before attempting
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
          webViewConfiguration: const WebViewConfiguration(
            enableJavaScript: true,
            enableDomStorage: true,
          ),
        );
      } else {
        // Try with universal_links: false as a fallback
        if (await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
          webOnlyWindowName: '_blank'
        )) {
          return;
        }

        // If still can't launch, show error
        Widgets.showSnackBar('Error', 'Could not launch $url');
        debugPrint('Could not launch URL: $url');
      }
    } catch (e) {
      // Handle malformed URLs or other exceptions
      Widgets.showSnackBar('Error', 'Invalid URL format');
      debugPrint('Error launching URL: $e');
    }
  }

  /// Launch maps directions
  static Future<void> launchMapsDirections(double lat, double long) async {
    final url = Platform.isIOS
        ? 'https://maps.apple.com/?daddr=$lat,$long&dirflg=d'
        : 'https://www.google.com/maps/dir/?api=1&destination=$lat,$long&travelmode=driving';
    
    final Uri uri = Uri.parse(url);
    await _launchUrl(uri, mode: LaunchMode.externalApplication);
  }

  /// Launch social media profile
  static Future<void> launchSocialMedia(String platform, String username) async {
    String url;
    
    switch (platform.toLowerCase()) {
      case 'instagram':
        url = 'https://instagram.com/$username';
        break;
      case 'facebook':
        url = 'https://facebook.com/$username';
        break;
      case 'twitter':
        url = 'https://twitter.com/$username';
        break;
      case 'tiktok':
        url = 'https://tiktok.com/@$username';
        break;
      case 'youtube':
        url = 'https://youtube.com/@$username';
        break;
      case 'linkedin':
        url = 'https://linkedin.com/in/$username';
        break;
      case 'soundcloud':
        url = 'https://soundcloud.com/$username';
        break;
      case 'spotify':
        url = 'https://open.spotify.com/artist/$username';
        break;
      default:
        url = username; // If it's already a URL
    }
    
    final Uri uri = Uri.parse(url);
    await _launchUrl(uri, mode: LaunchMode.externalApplication);
  }

  /// Private method to launch URLs with error handling
  static Future<void> _launchUrl(Uri uri, {LaunchMode mode = LaunchMode.platformDefault}) async {
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: mode);
      } else {
        Widgets.showSnackBar('Error', 'Could not launch ${uri.toString()}');
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      Widgets.showSnackBar('Error', 'Failed to launch. Please try again.');
    }
  }
}