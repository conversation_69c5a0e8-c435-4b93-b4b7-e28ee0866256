import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/billing_client_wrappers.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';

import '../../controller/user_controller.dart';
import '../constants/api_endpoints.dart';
import 'http_service.dart';

class PurchaseService extends GetxService {
  static PurchaseService get instance => Get.find<PurchaseService>();
  
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  // Product IDs - these should match your store configurations
  static const String weeklyProductId = 'banana_premium_weekly';

  static const List<String> _productIds = [
    weeklyProductId,
  ];
  
  // Observable states
  RxBool isAvailable = false.obs;
  RxBool isPurchasing = false.obs;
  RxList<ProductDetails> products = <ProductDetails>[].obs;
  RxString currentSubscription = ''.obs;
  RxBool hasActiveSubscription = false.obs;
  
  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializePurchases();
  }
  
  Future<void> _initializePurchases() async {
    try {
      // Check if in-app purchases are available
      isAvailable.value = await _inAppPurchase.isAvailable();
      
      if (!isAvailable.value) {
        log('In-app purchases not available');
        return;
      }
      
      // Initialize platform-specific configurations
      if (Platform.isIOS) {
        final InAppPurchaseStoreKitPlatformAddition iosAddition =
            _inAppPurchase.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
        await iosAddition.setDelegate(ExamplePaymentQueueDelegate());
      }
      
      // Listen to purchase updates
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => _subscription.cancel(),
        onError: (error) => log('Purchase stream error: $error'),
      );
      
      // Load products
      await loadProducts();
      
      // Check for existing subscriptions
      await checkSubscriptionStatus();
      
    } catch (e) {
      log('Error initializing purchases: $e');
    }
  }
  
  Future<void> loadProducts() async {
    try {
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(_productIds.toSet());
      
      if (response.notFoundIDs.isNotEmpty) {
        log('Products not found: ${response.notFoundIDs}');
      }
      
      products.value = response.productDetails;
      log('Loaded ${products.length} products');
      
    } catch (e) {
      log('Error loading products: $e');
    }
  }
  
  Future<void> purchaseProduct(String productId) async {
    if (isPurchasing.value) return;
    
    try {
      isPurchasing.value = true;
      
      final ProductDetails? productDetails = products.firstWhereOrNull(
        (product) => product.id == productId,
      );
      
      if (productDetails == null) {
        throw Exception('Product not found: $productId');
      }
      
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );
      
      await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
      
    } catch (e) {
      isPurchasing.value = false;
      log('Error purchasing product: $e');
      rethrow;
    }
  }
  
  Future<void> _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) async {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      await _handlePurchase(purchaseDetails);
    }
  }
  
  Future<void> _handlePurchase(PurchaseDetails purchaseDetails) async {
    try {
      if (purchaseDetails.status == PurchaseStatus.pending) {
        log('Purchase pending: ${purchaseDetails.productID}');
        return;
      }
      
      if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        
        // Verify purchase with backend
        final bool isValid = await _verifyPurchase(purchaseDetails);
        
        if (isValid) {
          currentSubscription.value = purchaseDetails.productID;
          hasActiveSubscription.value = true;
          log('Purchase successful: ${purchaseDetails.productID}');
        } else {
          log('Purchase verification failed: ${purchaseDetails.productID}');
        }
      }
      
      if (purchaseDetails.status == PurchaseStatus.error) {
        log('Purchase error: ${purchaseDetails.error}');
      }
      
      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
      }
      
    } catch (e) {
      log('Error handling purchase: $e');
    } finally {
      isPurchasing.value = false;
    }
  }
  
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      final userController = Get.find<UserController>();
      
      final Map<String, dynamic> verificationData = {
        'access_token': userController.accessToken.value,
        'product_id': purchaseDetails.productID,
        'purchase_token': purchaseDetails.verificationData.serverVerificationData,
        'platform': Platform.isIOS ? 'ios' : 'android',
      };
      
      final response = await ApiService.postData(
        'verify_purchase', // Add this endpoint to your API
        verificationData,
      );
      
      return response.status == true;
      
    } catch (e) {
      log('Error verifying purchase: $e');
      return false;
    }
  }
  
  Future<void> restorePurchases() async {
    try {
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      log('Error restoring purchases: $e');
      rethrow;
    }
  }
  
  Future<void> checkSubscriptionStatus() async {
    try {
      // This would typically check with your backend
      final userController = Get.find<UserController>();
      
      final response = await ApiService.postData(
        'get_subscription_status', // Add this endpoint to your API
        {'access_token': userController.accessToken.value},
      );
      
      if (response.status == true && response.data['body'] != null) {
        final subscriptionData = response.data['body'];
        hasActiveSubscription.value = subscriptionData['has_active_subscription'] ?? false;
        currentSubscription.value = subscriptionData['current_plan'] ?? '';
      }
      
    } catch (e) {
      log('Error checking subscription status: $e');
    }
  }
  
  ProductDetails? getProductDetails(String productId) {
    return products.firstWhereOrNull((product) => product.id == productId);
  }
  
  String getProductPrice(String productId) {
    final product = getProductDetails(productId);
    return product?.price ?? '';
  }
  
  @override
  void onClose() {
    _subscription.cancel();
    super.onClose();
  }
}

// iOS Payment Queue Delegate
class ExamplePaymentQueueDelegate implements SKPaymentQueueDelegateWrapper {
  @override
  bool shouldContinueTransaction(
      SKPaymentTransactionWrapper transaction, SKStorefrontWrapper storefront) {
    return true;
  }

  @override
  bool shouldShowPriceConsent() {
    return false;
  }
}
