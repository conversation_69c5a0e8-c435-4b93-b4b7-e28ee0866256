import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';

class WebCenteredWrapper extends StatelessWidget {
  final Widget child;
  final double maxWidth;

  const WebCenteredWrapper({
    super.key,
    required this.child,
    this.maxWidth = 430, // Adjust to match your mobile width
  });

  @override
  Widget build(BuildContext context) {
    return kIsWeb
        ? Center(
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxWidth),
        child: child,
      ),
    )
        : child; // On mobile, don't constrain
  }
}
