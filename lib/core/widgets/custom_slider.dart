import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class SeveritySliderController extends GetxController {
  var currentValue = 1.0.obs;

  void setValue(double value) {
    currentValue.value = value;
  }
}

class SeveritySlider extends StatelessWidget {
  final List<String> labels;
  final SeveritySliderController controller;

  SeveritySlider({
    super.key,
    required this.labels,
    SeveritySliderController? controller,
  }) : controller = controller ?? Get.put(SeveritySliderController());

  // Map value to corresponding label
  String getLabelForValue(int value) {
    // Now we have a direct 1:1 mapping with 5 values
    if (value >= 1 && value <= 5) {
      return labels[value - 1]; // value 1 → labels[0], value 2 → labels[1], etc.
    }
    return labels[0]; // fallback
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Obx(() {
          //   final value = controller.currentValue.value.toInt();
          //   final label = getLabelForValue(value);
          //   return Row(
          //     mainAxisAlignment: MainAxisAlignment.end,
          //     children: [
          //       Texts.textNormal(
          //           "$value/10",
          //           size: 12,
          //           textAlign: TextAlign.start),
          //
          //     ],
          //   );
          // }),
          Obx(() {
            return SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.pink.shade100,
                inactiveTrackColor: Colors.pink.shade100,
                trackHeight: 4.0,
                trackShape: const FullWidthTrackShape(),
                thumbColor: Colors.white,
                thumbShape: CustomThumbShape(
                  thumbRadius: 8.0,
                  borderColor: Colors.pink,
                ),
                overlayColor: Colors.pink.withAlpha(32),
                overlayShape: const RoundSliderOverlayShape(overlayRadius: 20.0),
              ),
              child: Slider(
                min: 1,
                max: 10,
                divisions: 9, // 9 divisions for 10 points (many divisions)
                value: controller.currentValue.value,
                onChanged: (value) => controller.setValue(value),
              ),

            );
          }),

          // Labels positioned exactly at the center of each division
          Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: SizedBox(
              height: 20, // Fixed height for the label container
              child: LayoutBuilder(
                builder: (context, constraints) {
                  // Calculate the exact positions for each division point
                  final double sliderPadding = 24.0; // Flutter's internal slider padding
                  final double trackWidth = constraints.maxWidth - (sliderPadding * 2);
                  final int totalDivisions = 4; // 4 divisions for 5 points (1,2,3,4,5)

                  return Stack(
                    children: List.generate(labels.length, (index) {
                      // Calculate exact position for each point on the slider track
                      // Point positions: 0%, 25%, 50%, 75%, 100% of track width
                      final double normalizedPosition = index / totalDivisions; // 0, 0.25, 0.5, 0.75, 1.0
                      final double pointX = sliderPadding + (normalizedPosition * trackWidth);

                      return Positioned(
                        left: pointX - 35, // Center the text (70px width / 2)
                        top: 0,
                        child: Container(
                          width: 70,
                          alignment: Alignment.center,
                          child: Text(
                            labels[index],
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.black,
                              fontFamily: "Montserrat",
                            ),
                          ),
                        ),
                      );
                    }),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}



class CustomThumbShape extends SliderComponentShape {
  final double thumbRadius;
  final Color borderColor;

  CustomThumbShape({this.thumbRadius = 10.0, required this.borderColor});

  @override
  Size getPreferredSize(bool isEnabled, bool isDiscrete) {
    return Size.fromRadius(thumbRadius);
  }

  @override
  void paint(
      PaintingContext context,
      Offset center, {
        required Animation<double> activationAnimation,
        required Animation<double> enableAnimation,
        required bool isDiscrete,
        required TextPainter? labelPainter,
        required RenderBox parentBox,
        required SliderThemeData sliderTheme,
        required TextDirection textDirection,
        required double value,
        required double textScaleFactor,
        required Size sizeWithOverflow,
      }) {
    final Canvas canvas = context.canvas;

    // Outer pink border
    final Paint borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // Inner white fill
    final Paint fillPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(center, thumbRadius, fillPaint);
    canvas.drawCircle(center, thumbRadius, borderPaint);
  }
}



















//
//
// import 'package:MenoPal/core/widgets/text_widgets.dart';
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
//
//
// class SeveritySliderController extends GetxController {
//   var currentValue = 0.0.obs;
//
//   void setValue(double value) {
//     currentValue.value = value;
//   }
// }
//
// class SeveritySlider extends StatelessWidget {
//   final List<String> labels;
//   final SeveritySliderController controller;
//
//   SeveritySlider({
//     super.key,
//     required this.labels,
//     SeveritySliderController? controller,
//   })  : controller = controller ?? Get.put(SeveritySliderController());
//
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 4.0),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.end,
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Obx(()=>Texts.textNormal("${controller.currentValue.value.toStringAsFixed(0)}/10",size: 12,textAlign: TextAlign.start),),
//
//           Obx(() {
//             return SliderTheme(
//               data: SliderTheme.of(context).copyWith(
//                 trackHeight: 4.0,
//                 activeTrackColor: Colors.pink.shade100,
//                 inactiveTrackColor: Colors.pink.shade100,
//                 thumbColor: Colors.white,
//                 trackShape: const FullWidthTrackShape(), // ✅ this is correct
//                 thumbShape: CustomThumbShape( // ✅ your custom thumb
//                   thumbRadius: 8.0,
//                   borderColor: Colors.pink,
//                 ),
//                 overlayColor: Colors.pink.withAlpha(32),
//                 overlayShape: const RoundSliderOverlayShape(overlayRadius: 20.0),
//               ),
//               child: Slider(
//                 value: controller.currentValue.value,
//                 min: 0,
//                 max: (labels.length - 1).toDouble(),
//                 divisions: labels.length - 1,
//                 onChanged: (value) => controller.setValue(value),
//               ),
//             );
//
//           }),
//
//           const SizedBox(height: 2),
//
//           // 👇 Label Row matches slider width
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: labels
//                 .map((label) => Text(
//               label,
//               style: const TextStyle(
//                 fontSize: 10,
//                 color: Colors.black,
//                 fontFamily: "Montserrat",
//               ),
//             ))
//                 .toList(),
//           ),
//         ],
//       ),
//     );
//
//
//
//   }
// }
//
// class CustomThumbShape extends SliderComponentShape {
//   final double thumbRadius;
//   final Color borderColor;
//
//   CustomThumbShape({this.thumbRadius = 10.0, required this.borderColor});
//
//   @override
//   Size getPreferredSize(bool isEnabled, bool isDiscrete) {
//     return Size.fromRadius(thumbRadius);
//   }
//
//   @override
//   void paint(
//       PaintingContext context,
//       Offset center, {
//         required Animation<double> activationAnimation,
//         required Animation<double> enableAnimation,
//         required bool isDiscrete,
//         required TextPainter? labelPainter,
//         required RenderBox parentBox,
//         required SliderThemeData sliderTheme,
//         required TextDirection textDirection,
//         required double value,
//         required double textScaleFactor,
//         required Size sizeWithOverflow,
//       }) {
//     final Canvas canvas = context.canvas;
//
//     // Outer pink border
//     final Paint borderPaint = Paint()
//       ..color = borderColor
//       ..style = PaintingStyle.stroke
//       ..strokeWidth = 2;
//
//     // Inner white fill
//     final Paint fillPaint = Paint()
//       ..color = Colors.white
//       ..style = PaintingStyle.fill;
//
//     canvas.drawCircle(center, thumbRadius, fillPaint);
//     canvas.drawCircle(center, thumbRadius, borderPaint);
//   }
// }
class FullWidthTrackShape extends SliderTrackShape {
  const FullWidthTrackShape();

  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight ?? 4.0;
    final double trackTop = offset.dy + (parentBox.size.height - trackHeight) / 2;
    return Rect.fromLTWH(
      offset.dx,
      trackTop,
      parentBox.size.width,
      trackHeight,
    );
  }

  @override
  void paint(
      PaintingContext context,
      Offset offset, {
        required RenderBox parentBox,
        required SliderThemeData sliderTheme,
        required Animation<double> enableAnimation,
        required TextDirection textDirection,
        required Offset thumbCenter,
        Offset? secondaryOffset, // ✅ Add this parameter
        bool isEnabled = false,
        bool isDiscrete = false,
      }) {
    final Paint activePaint = Paint()
      ..color = sliderTheme.activeTrackColor!
      ..style = PaintingStyle.fill;

    final Paint inactivePaint = Paint()
      ..color = sliderTheme.inactiveTrackColor!
      ..style = PaintingStyle.fill;

    final Rect trackRect = getPreferredRect(
      parentBox: parentBox,
      offset: offset,
      sliderTheme: sliderTheme,
      isEnabled: isEnabled,
      isDiscrete: isDiscrete,
    );

    final bool isLTR = textDirection == TextDirection.ltr;

    final Rect leftTrackSegment = Rect.fromLTRB(
      trackRect.left,
      trackRect.top,
      thumbCenter.dx,
      trackRect.bottom,
    );

    final Rect rightTrackSegment = Rect.fromLTRB(
      thumbCenter.dx,
      trackRect.top,
      trackRect.right,
      trackRect.bottom,
    );

    final Canvas canvas = context.canvas;
    canvas.drawRect(isLTR ? leftTrackSegment : rightTrackSegment, activePaint);
    canvas.drawRect(isLTR ? rightTrackSegment : leftTrackSegment, inactivePaint);
  }
}


