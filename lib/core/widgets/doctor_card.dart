import 'package:MenoPal/core/widgets/custom_button.dart';
import 'package:MenoPal/core/widgets/doctor_dialog_box.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';


import '../constants/assets_constants.dart';
import '../constants/color_constants.dart';

class Doctor<PERSON>ard extends StatelessWidget {
  final Map doc;
  final String icon;

   DoctorCard({super.key, required this.doc, required this.icon});

  List<String> symptomsList = [
    "Hot Flushes",
    "Harmone Therapy",
    "Sleep Issues",
    "Mood Changes"
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 5),
      decoration: BoxDecoration(
        border: Border.all(color: ColorConstants.greyBgColor),
        color: ColorConstants.whiteColor,
        borderRadius: BorderRadius.circular(15),
      ),
      child: Padding(
        padding: const EdgeInsets.all(14),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Flexible(child: Texts.textBold(doc["name"], size: 18, maxlines: 2,textAlign: TextAlign.start),
                ),
                Widgets.widthSpaceW2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Baseline(
                        baseline: 18,
                        baselineType: TextBaseline.alphabetic,

                        child: Icon(Icons.star, color: Colors.orange, size: 18)),
                  Widgets.widthSpaceW05,
                  Texts.textNormal(doc["rating"], size: 14),
                ],),

              ],
            ),
            Widgets.heightSpaceH05,
            Texts.textNormal(doc["specialty"],
                size: 12, textAlign: TextAlign.start),
            const SizedBox(height: 6),
            Widgets.divider(),
            Widgets.heightSpaceH2,
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Flexible(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Image.asset(Assets.doctorLocationIcon,
                          height: 15, width: 15),
                      Widgets.widthSpaceW1,
                      Flexible(child:Texts.textNormal(doc["location"], size: 12,textAlign: TextAlign.start),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12),
                  height: 20,
                  width: 1,
                  color: ColorConstants.grayColor,
                ),
                Flexible(
                  child: Row(
                    children: [
                      Image.asset(Assets.calendarIcon, height: 15, width: 15),
                      Widgets.widthSpaceW05,
                      Flexible(
                        child: Texts.textNormal(
                          doc["experience"],
                          size: 12,
                          textAlign: TextAlign.start,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Widgets.heightSpaceH2,
            Widgets.divider(),
            Widgets.heightSpaceH2,
            Wrap(
              spacing: 8,
              runSpacing: 8,

              children: (doc["available"] as List<String>).map((option) {
                String iconPath;

                if (option.toLowerCase() == "corporate") {
                  iconPath = Assets.corporateIcon;
                } else if (option.toLowerCase() == "in person") {
                  iconPath = Assets.inpersonIcon;
                } else if (option.toLowerCase() == "online") {
                  iconPath = Assets.onlineIcon;
                } else {
                  iconPath = Assets.onlineIcon; // fallback if needed
                }

                return SizedBox(
                  height: 35,
                  child: Chip(
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    side: BorderSide.none,
                    label: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Image.asset(iconPath, height: 15, width: 15),
                        Widgets.widthSpaceW1,
                        Flexible(child:Texts.textNormal(option,size: 12,color: ColorConstants.redTextColor,maxLines: 3,textAlign: TextAlign.start),
                        ),
                      ],
                    ),
                    backgroundColor: ColorConstants.primaryColor
                        .withAlpha((0.2 * 255).toInt()),
                    // labelStyle: TextStyle(color: ColorConstants.redTextColor,fontSize: 12),
                  ),
                );
              }).toList(),
            ),
            Widgets.heightSpaceH2,
            CustomButton(
              backgroundColor: ColorConstants.darkPrimaryColor,
              label: "Book consultation",
              onTap: () {
                // Handle tap
              },
            ),
            Widgets.heightSpaceH2,
            CustomButton(
              borderColor: ColorConstants.blackColor,
              label: "View details",
              textColor: ColorConstants.blackColor,
              onTap: () {
                Get.dialog(
                  DoctorDialogBox(symptomsList: symptomsList,name: doc["name"],speaciality:doc["specialty"],),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}


