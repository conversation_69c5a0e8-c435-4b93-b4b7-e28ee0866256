import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/track/view/symptoms/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../view/bottom_nav_bar/modules/track/model.dart';


class ExpandableListTile extends StatelessWidget {
  const ExpandableListTile({
    super.key,
    required this.item,
    required this.children,
  });

  final TrackItem item;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    final controller = Get.put<SymptomsController>(SymptomsController());

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 6),
      child: Obx(() {
        final isExpanded = controller.expandedMap[item.title] ?? false;

        return InkWell(
          onTap: () => controller.toggleExpanded(item.title),
          child: Container(

            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: ColorConstants.primaryColor.withAlpha(
                    (0.3 * 255).toInt()),
              ),
            ),
            child: Column(
             crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.pink.shade50,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      alignment: Alignment.center,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 15.0,
                            horizontal: 15),
                        child: Image.asset(
                          item.icon,
                          height: 20,
                          width: 20,
                        ),
                      ),
                    ),
                    Widgets.widthSpaceW2,
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Texts.textBold(item.title, size: 14),
                          Widgets.heightSpaceH05,
                          Texts.textNormal(item.subtitle, size: 12,
                              textAlign: TextAlign.start),
                          Widgets.heightSpaceH05,
                          if (item.subtitle2 != null)
                            Texts.textNormal(item.subtitle2!, size: 12,
                                textAlign: TextAlign.start),
                        ],
                      ),
                    ),
                  ],
                ),
                if (isExpanded) ...children,
              ],
            ),
          ),
        );
      }),
    );
  }}