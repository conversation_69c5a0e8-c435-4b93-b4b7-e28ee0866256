import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:flutter/material.dart';


class DynamicButton extends StatelessWidget {
   DynamicButton({
    super.key,
    required this.title,
     this.icon,
    this.borderColor,
    this.textColor,
     this.bgColor,
     this.onTap,this.padding,this.size,this.iconHeight,this.iconWidth
  });
  final String title;
   String? icon;
  final Color? borderColor;
  final Color? textColor;
   final Color? bgColor;
    GestureTapCallback? onTap;
    var padding;
    var size;
   var iconWidth;
   var iconHeight;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:onTap,

      child: Container(
        decoration: BoxDecoration(
          color: bgColor??ColorConstants.transparentColor,
            borderRadius: BorderRadius.circular(7),
            border: Border.all(color: borderColor??ColorConstants.darkPrimaryColor)
        ),
        child: Padding(
          padding: padding??const EdgeInsets.all(8.0),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
             icon!=null ?Image.asset(
                icon!,
              width:  iconWidth??15,
               height: iconHeight??15,
              ):Text(""),
              Widgets.widthSpaceW1,
              Texts.textNormal(title,
                  color: textColor??ColorConstants.blackColor, size:size?? 12,maxLines: 2,textAlign: TextAlign.center,),
            ],
          ),
        ),
      ),
    );
  }
}