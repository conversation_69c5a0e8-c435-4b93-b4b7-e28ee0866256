import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/widgets/symptom_chart.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:flutter/material.dart';

import '../constants/assets_constants.dart';


class SymptomsTrendWidget extends StatelessWidget {
  const SymptomsTrendWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: ColorConstants.greyBorderColor),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(
                      color: ColorConstants.primaryColor
                          .withAlpha((0.5 * 255).toInt()),
                      borderRadius: BorderRadius.circular(10)),
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Image.asset(
                      Assets.sleepingIcon,
                      height: 20,
                      width: 20,
                    ),
                  ),
                ),
                Widgets.widthSpaceW2,
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Texts.textBold("Symptom Trends", size: 16,maxlines: 2,textAlign: TextAlign.start),
                      Widgets.heightSpaceH05,
                      Texts.textNormal(
                          "Monitor your sleep quality and duration and patterns",
                          textAlign: TextAlign.start,
                          size: 12,maxLines: 2),
                    ],
                  ),
                ),
              ],
            ),
            Widgets.heightSpaceH2,

            Widgets.divider(),
            Widgets.heightSpaceH4,
            SymptomSeverityChart(),
          ],
        ),
      ),
    );
  }
}
