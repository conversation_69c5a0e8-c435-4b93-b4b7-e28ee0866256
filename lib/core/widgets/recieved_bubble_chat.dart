
import 'package:banana/core/widgets/widgets.dart';
import 'package:banana/view/modules/inbox/model/message_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';

import '../constants/color_constants.dart';
import '../utils/triangle.dart';
import 'dart:math' as math;

import '../utils/utils.dart';
import 'image_preview.dart'; // import this
class ReceivedBubbleChatGroup extends StatelessWidget {
  final Message message;
  Callback? onAvatarTap;
  ReceivedBubbleChatGroup({required this.message, required this.onAvatarTap});

  @override
  Widget build(BuildContext context) {
    final messageTextGroup = Flexible(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Transform(
              alignment: Alignment.center,
              transform: Matrix4.rotationY(math.pi),
              child: CustomPaint(
                painter: Triangle(Colors.black),
              ),
            ),
            Flexible(
              child: Container(
                padding:
                const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 20),
                decoration: const BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(18),
                    bottomLeft: Radius.circular(18),
                    bottomRight: Radius.circular(18),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [

                    Text(
                      message.message ?? "",
                      style: const TextStyle(color: Colors.white, fontSize: 14),
                    ),

                  ],
                ),
              ),
            ),
          ],
        ));

    return Container(
      margin: const EdgeInsets.only(right: 20.0, left: 5, top: 0, bottom: 5),
      padding: const EdgeInsets.only(right: 20.0, left: 5, top: 5, bottom: 5),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start,
        children: [ Padding(
          padding: const EdgeInsets.only(left: 23.0,bottom: 3),
          child: Text(
            message.senderName ?? "",
            style: TextStyle(
                color:ColorConstants.secondaryColor ,
                fontSize: 7,
                fontWeight: FontWeight.bold),
          ),
        ),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              InkWell(
                onTap: onAvatarTap,
                child: AdvancedAvatar(
                  size: 22,
                  foregroundDecoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color:Colors.white,
                      width: .5,
                    ),
                  ),
                  child:message.senderImage==""? Text(message.senderName?.substring(0, 1)??""):Widgets.networkImage(message.senderImage ?? "",
                      width: 100, height: 100),
                ),
              ),
              const SizedBox(
                width: 5,
              ),
              messageTextGroup,
            ],
          ),
          const SizedBox(
            height: 2,
          ),
        ],
      ),
    );
  }
}
class ReceivedBubbleChat extends StatelessWidget {
   Message message;
  Callback? onAvatarTap;
   ReceivedBubbleChat({required this.message, required this.onAvatarTap});

  @override
  Widget build(BuildContext context) {
    final messageTextGroup = Flexible(
        child: Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Transform(
          alignment: Alignment.center,
          transform: Matrix4.rotationY(math.pi),
          child: CustomPaint(
            painter: Triangle(Colors.black),
          ),
        ),
        Flexible(
          child: Container(
            padding:
                const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 20),
            decoration: const BoxDecoration(
              color: Colors.black,
              borderRadius: BorderRadius.only(
                topRight: Radius.circular(18),
                bottomLeft: Radius.circular(18),
                bottomRight: Radius.circular(18),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [

                Text(
                  message.message ?? "",
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),

              ],
            ),
          ),
        ),
      ],
    ));

    return Container(
      margin: const EdgeInsets.only(right: 20.0, left: 5, top: 0, bottom: 5),
      padding: const EdgeInsets.only(right: 20.0, left: 5, top: 5, bottom: 5),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[

          messageTextGroup,
        ],
      ),
    );
  }
}

class SentMessage extends StatelessWidget {
 Message message;
   SentMessage({
    required this.message,
  });

  @override
  Widget build(BuildContext context) {
    final messageTextGroup = Flexible(
        child: Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Flexible(
          child: Container(
            padding:
                const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 20),
            decoration:  BoxDecoration(
              color: ColorConstants.purpleColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(18),
                bottomLeft: Radius.circular(18),
                bottomRight: Radius.circular(18),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  message.message ?? "",
                  style: const TextStyle(color: Colors.white, fontSize: 14),
                ),

              ],
            ),
          ),
        ),
        Transform(
          alignment: Alignment.center,
          transform: Matrix4.rotationY(math.pi),
          child: CustomPaint(
            painter: Triangle(ColorConstants.purpleColor),
          ),
        ),
      ],
    ));

    return Padding(
      padding: EdgeInsets.only(right: 18.0, left: 50, top: 5, bottom: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          SizedBox(height: 30),
          messageTextGroup,
        ],
      ),
    );
  }
}
