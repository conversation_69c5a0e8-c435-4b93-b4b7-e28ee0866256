import 'package:banana/core/widgets/text_widgets.dart';
import 'package:banana/core/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';

import '../../constants/my_fonts.dart';
import '../constants/color_constants.dart';

class EntryField extends StatelessWidget {
  final String? hint;
  final String? prefixIcon;
  final Color? color;  final Color? fontColor;
  final TextEditingController? controller;
  final String? initialValue;
  final bool? readOnly;
  final TextAlign? textAlign;
  final IconData? suffixIcon;
  final TextInputType? textInputType;
  final String? label;  final String? subtitle;
  final int? maxLines; final int? minLines;
  final EdgeInsetsGeometry? padding;
  final String? Function(String? value)? validator;
  final Function(String? value)? onChange;final Function(String? value)? onSubmit;
  final List<TextInputFormatter>? inputFormatter;
  final int? maxLength;
  final bool? maxLengthEnforced;
  final bool? obscureText;
  final String? trailingTitle;
  final TextCapitalization? textCapitalization;
  final BorderRadius? borderRadius;
  final Function()? onTrailingTap;
  final Function()? onTap;
   EntryField({
    super.key,
    this.hint,this.fontColor,
    this.prefixIcon,this.onTap,
    this.color,
    this.controller,
    this.initialValue,this.minLines,
    this.readOnly,
    this.textAlign,
    this.suffixIcon,
    this.textInputType,this.subtitle,
    this.label,
    this.maxLines,
    this.obscureText,
    this.trailingTitle,
    this.padding,
    this.validator,
    this.inputFormatter,this.onSubmit,
    this.onTrailingTap,
    this.maxLength,
    this.maxLengthEnforced,
    this.onChange,
    this.textCapitalization,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        label != null
            ? Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Texts.textBlock( label??"",
                      size: 12,

                      align: TextAlign.center,
                      color:Colors.black),
                  if (trailingTitle != null)
                    GestureDetector(
                      onTap: onTrailingTap,
                      child: Text(
                        trailingTitle ?? '',
                        style: Theme.of(context)
                            .textTheme
                            .titleSmall!
                            .copyWith(fontSize: 14, color: Colors.red),
                      ),
                    )
                ],
              )
            : const SizedBox.shrink(),
        subtitle!= null? Column(crossAxisAlignment: CrossAxisAlignment.start,
          children: [
             subtitle!= null
                ? Texts.textMedium( subtitle??"",
                align: TextAlign.center, color: Colors.black54, size: 13)
                : const SizedBox.shrink(),const SizedBox(
                height: 8
            ),
          ],
        ):SizedBox(height: 5,),
        Container(
alignment: Alignment.center,
          height: 50,
          child: TextFormField(onTap:onTap ,
            obscureText: obscureText ?? false,onFieldSubmitted: onSubmit,
            controller: controller,
            validator: validator,
            onChanged: onChange,
            initialValue: initialValue,
            textCapitalization: textCapitalization ?? TextCapitalization.none,
            readOnly: readOnly ?? false,
            maxLines: maxLines ?? 1,
            inputFormatters: inputFormatter,
            maxLength: maxLength,minLines: minLines??1,
            maxLengthEnforcement: maxLengthEnforced ?? false
                ? MaxLengthEnforcement.enforced
                : MaxLengthEnforcement.none,
            textAlign: textAlign ?? TextAlign.left,
            style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w700,fontFamily: "sansRegular",
                color: fontColor??Colors.black),
            keyboardType: textInputType,
            decoration: InputDecoration(
              filled: true,
              contentPadding: const EdgeInsets.symmetric(horizontal: 10),
              prefixIconConstraints: BoxConstraints(
                minWidth: 15, // Set minimum width for the icon
                minHeight: 15, // Set minimum height for the icon
              ),
              prefixIcon: prefixIcon != null
                  ? Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SizedBox(width: 17,height: 17,
                    child: Image.asset(prefixIcon??"",fit: BoxFit.contain,color: fontColor??Colors.black54)),
                  )
                  : null,
              suffixIcon: suffixIcon==null?null:InkWell(
                  onTap: onTrailingTap,

                  child: Icon(suffixIcon,size: 17,color: Colors.black54,)),
              hintText: hint,
              hintStyle: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w400,
                  color: Colors.black38),
              counterStyle: const TextStyle(
                height: double.minPositive,
              ),
              counterText: "",fillColor:color??ColorConstants.lightGrey ,
              border: OutlineInputBorder(
                borderSide:  BorderSide(color: ColorConstants.secondaryColor),
                borderRadius:
                    borderRadius ?? BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.secondaryColor),
                borderRadius:
                    borderRadius ?? BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.secondaryColor),
                borderRadius:
                    borderRadius ?? BorderRadius.all(Radius.circular(10)),
              ),
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
      ],
    );
  }
}
class EntrySearchField extends StatelessWidget {
  final String? hint;
  final String? prefixIcon;
  final Color? color;
  final TextEditingController? controller;
  final String? initialValue;
  final bool? readOnly;
  final TextAlign? textAlign;
  final String? suffixIcon;
  final TextInputType? textInputType;
  final String? label;
  final int? maxLines;
  final EdgeInsetsGeometry? padding;
  final String? Function(String? value)? validator;
  final Function(String? value)? onChange;
  final List<TextInputFormatter>? inputFormatter;
  final int? maxLength;
  final bool? maxLengthEnforced;
  final bool? obscureText;
  final String? trailingTitle;
  final TextCapitalization? textCapitalization;
  final BorderRadius? borderRadius;
  final Function()? onTrailingTap;
  final Function()? onTap;
  const EntrySearchField({
    super.key,
    this.hint,
    this.prefixIcon,this.onTap,
    this.color,
    this.controller,
    this.initialValue,
    this.readOnly,
    this.textAlign,
    this.suffixIcon,
    this.textInputType,
    this.label,
    this.maxLines,
    this.obscureText,
    this.trailingTitle,
    this.padding,
    this.validator,
    this.inputFormatter,
    this.onTrailingTap,
    this.maxLength,
    this.maxLengthEnforced,
    this.onChange,
    this.textCapitalization,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        label != null
            ? Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              '${label!}',
              style: Theme.of(context).textTheme.titleSmall!.copyWith(
                  color: Colors.black87,
                  fontSize: 13,
                  fontFamily: "LatoRegular"),
            ),
            if (trailingTitle != null)
              GestureDetector(
                onTap: onTrailingTap,
                child: Text(
                  trailingTitle ?? '',
                  style: Theme.of(context)
                      .textTheme
                      .titleSmall!
                      .copyWith(fontSize: 14, color: Colors.red),
                ),
              )
          ],
        )
            : const SizedBox.shrink(),
        Widgets.heightSpaceH1,
        Container(
          alignment: Alignment.center,
          height: 45,
          child: TextFormField(onTap:onTap ,
            obscureText: obscureText ?? false,
            controller: controller,
            validator: validator,
            onChanged: onChange,
            initialValue: initialValue,
            textCapitalization: textCapitalization ?? TextCapitalization.none,
            readOnly: readOnly ?? false,
            maxLines: maxLines ?? 1,
            inputFormatters: inputFormatter,
            maxLength: maxLength,
            maxLengthEnforcement: maxLengthEnforced ?? false
                ? MaxLengthEnforcement.enforced
                : MaxLengthEnforcement.none,
            textAlign: textAlign ?? TextAlign.left,
            style: const TextStyle(
                fontFamily: "sansRegular",
                fontWeight: FontWeight.w400,
                fontSize: 12,
                color: Colors.black),
            keyboardType: textInputType,
            decoration: InputDecoration(
              filled: true,fillColor: ColorConstants.lightGrey,
              contentPadding: const EdgeInsets.symmetric(horizontal: 10),
              prefixIconConstraints: BoxConstraints(
                minWidth: 15, // Set minimum width for the icon
                minHeight: 15, // Set minimum height for the icon
              ),
              prefixIcon: prefixIcon != null
                  ? Padding(
                padding: const EdgeInsets.all(8.0),
                child: SizedBox(width: 17,height: 17,
                    child: Image.asset(prefixIcon??"",fit: BoxFit.contain,color: Colors.black54)),
              )
                  : null,
              suffixIcon: suffixIcon!=null?InkWell(
                  onTap: onTrailingTap,

                  child:  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: SizedBox(width: 15,height: 15,
                        child: Image.asset(suffixIcon??"",fit: BoxFit.contain,color: Colors.black54)),
                  )):SizedBox(),
              hintText: hint,
              hintStyle: const TextStyle(
                  fontFamily: "sansRegular",
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                  color: Colors.black45),
              counterStyle: const TextStyle(
                height: double.minPositive,
              ),
              counterText: "",
              border: OutlineInputBorder(
                borderSide:  BorderSide(color: ColorConstants.lightGrey),
                borderRadius:
                borderRadius ?? BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color:ColorConstants.lightGrey),
                borderRadius:
                borderRadius ?? BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color:  ColorConstants.lightGrey),
                borderRadius:
                borderRadius ?? BorderRadius.all(Radius.circular(10)),
              ),
            ),
          ),
        ),
        Widgets.heightSpaceH2,
      ],
    );
  }
}

class EntryBigField extends StatelessWidget {
  final String? hint;
  final IconData? prefixIcon;
  final Color? color; final Color? fontColor;
  final TextEditingController? controller;
  final String? initialValue;
  final bool? readOnly;
  final TextAlign? textAlign;
  final IconData? suffixIcon;
  final TextInputType? textInputType;
  final String? label;
  final int? maxLines;
  final int? minLines;
  final EdgeInsetsGeometry? padding;
  final String? Function(String? value)? validator;
  final Function(String? value)? onChange;
  final List<TextInputFormatter>? inputFormatter;
  final int? maxLength;
  final bool? maxLengthEnforced;
  final bool? obscureText;
  final String? trailingTitle;
  final Iterable<String>? autoFillType;
  final TextCapitalization? textCapitalization;
  final Function()? onTrailingTap;

  const EntryBigField({
    super.key,
    this.hint,this.fontColor,
    this.prefixIcon,
    this.color,
    this.controller,
    this.initialValue,
    this.readOnly,
    this.textAlign,
    this.autoFillType,
    this.suffixIcon,
    this.textInputType,
    this.label,
    this.maxLines,
    this.obscureText,
    this.trailingTitle,
    this.padding,
    this.validator,
    this.inputFormatter,
    this.onTrailingTap,
    this.maxLength,
    this.maxLengthEnforced,
    this.onChange,
    this.minLines,
    this.textCapitalization,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        label != null
            ? Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    '${label!}',
                    style: Theme.of(context).textTheme.titleSmall!.copyWith(
                        color: Colors.black,
                        fontSize: 12,fontFamily: "sansRegular"
                       ),
                  ),
                  if (trailingTitle != null)
                    GestureDetector(
                      onTap: onTrailingTap,
                      child: Text(
                        trailingTitle ?? '',
                        style: Theme.of(context)
                            .textTheme
                            .titleSmall!
                            .copyWith(fontSize: 14, color: Colors.red),
                      ),
                    )
                ],
              )
            : const SizedBox.shrink(),
        const SizedBox(
          height: 5,
        ),
        TextFormField(
          obscureText: obscureText ?? false,
          autofillHints: autoFillType,
          controller: controller,
          validator: validator,
          onChanged: onChange,
          minLines: minLines,
          initialValue: initialValue,
          textCapitalization: textCapitalization ?? TextCapitalization.none,
          readOnly: readOnly ?? false,
          maxLines: null,
          inputFormatters: inputFormatter,
          maxLength: maxLength,
          maxLengthEnforcement: maxLengthEnforced ?? false
              ? MaxLengthEnforcement.enforced
              : MaxLengthEnforcement.none,
          textAlign: textAlign ?? TextAlign.left,
          style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w700,fontFamily: "sansRegular",
              color: fontColor??Colors.black),
          keyboardType: textInputType,
          decoration: InputDecoration(
            filled: true,


            // suffixIcon: onTrailingTap==null?SizedBox.shrink():InkWell(
            //     onTap: onTrailingTap,
            //
            //     child: Icon(suffixIcon,size: 17,color: Colors.black54,)),
            hintText: hint,
            hintStyle: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: Colors.black38),
            counterStyle: const TextStyle(
              height: double.minPositive,
            ),
            counterText: "",fillColor:color??ColorConstants.lightGrey ,
            border: OutlineInputBorder(
              borderSide:  BorderSide(color: ColorConstants.secondaryColor),
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ColorConstants.secondaryColor),
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
            enabledBorder: OutlineInputBorder(
              borderSide: BorderSide(color: ColorConstants.secondaryColor),
              borderRadius:BorderRadius.all(Radius.circular(10)),
            ),
          ),
        ),
        const SizedBox(
          height: 15,
        ),
      ],
    );
  }
}
