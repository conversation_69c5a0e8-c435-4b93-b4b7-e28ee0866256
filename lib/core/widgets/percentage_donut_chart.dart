import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class PercentageDonutChart extends StatelessWidget {
  final num percentage;
  final Color primaryColor;
  final Color backgroundColor;
  final double radius;
  final double centerSpaceRadius;
  final double fontSize;

  const PercentageDonutChart({
    Key? key,
    required this.percentage,
    this.primaryColor = Colors.green,
    this.backgroundColor = const Color(0xFFE0E0E0),
    this.radius = 12,
    this.centerSpaceRadius = 18,
    this.fontSize = 12,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 70,
      height: 70,
      alignment: Alignment.center,
      child: Stack(
        alignment: Alignment.center,
        children: [
          <PERSON><PERSON><PERSON>(
            PieChartData(
              startDegreeOffset: -90,
              sectionsSpace: 0,
              centerSpaceRadius: centerSpaceRadius,
              sections: [
                PieChartSectionData(
                  value: percentage.toDouble(),
                  color: Colors.green,
                  radius: radius,
                  title: '',
                ),
                PieChartSectionData(
                  value: 100 - percentage.toDouble(),
                  color: backgroundColor,
                  radius: radius,
                  title: '',
                ),
              ],
            ),
          ),
          Text(
            '${percentage.toInt()}%',
            style: TextStyle(
              fontSize: fontSize,
              fontWeight: FontWeight.bold,
              color: Colors.black,
            ),
          ),
        ],
      ),
    );
  }
}