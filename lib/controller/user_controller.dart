import 'dart:convert';
import 'dart:developer';

import 'package:banana/organizer_flow/view/dashboard/view/modules/home/<USER>/dashboard_view.dart';
import 'package:banana/view/authentication/view/role_selection_view.dart';
import 'package:banana/view/navigation/controller/navigation_controller.dart';
import 'package:banana/view/navigation/view/navigation_view.dart';
import 'package:banana/view/profile_process/view/welcome_bananas_view.dart';
import 'package:banana/view/starting/view/on_boarding_view.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../api_services/pref_data_string.dart';
import '../core/constants/api_endpoints.dart';
import '../core/services/http_service.dart';
import '../core/services/report_service.dart';
import '../core/widgets/custom_button.dart';
import '../core/widgets/text_widgets.dart';
import '../core/widgets/widgets.dart';
import '../model/user_model.dart';
import '../organizer_flow/view/dashboard/view/navigation_view.dart';
import '../utils/my_navigations.dart';
import '../view/authentication/view/login_view.dart';
import '../view/modules/event/controller/event_controller.dart';
import '../view/modules/home/<USER>/home_controller.dart';
import '../view/modules/inbox/controller/chat_controller.dart';
import '../view/modules/profile/controller/profile_controller.dart';

class UserController extends GetxController {
  UserModel? userModel;
  RxString accessToken = "".obs;  RxString userId = "".obs;
  String? password;
  bool isLoggedIn = false;
  TextEditingController passwordController = TextEditingController();
  // var member = MemberDetail().obs; // Observable variable for member details
@override
  void onInit() {
    // TODO: implement onInit
    super.onInit();
    fetchUser();
  }
  Future<void> saveUser(UserModel userModel) async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      final userString = jsonEncode(userModel);
      sharedUser.setString('user', userString);
      sharedUser.setString('token', accessToken.value);
      sharedUser.setBool('login', true);
    } catch (e) {
      print('Error saving user: $e');
    }
  }

  Future<void> eraseUser() async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      sharedUser.remove('user');
      sharedUser.remove('token');

      sharedUser.clear();
      accessToken.value="";
      userId.value="";
      update();
    } catch (e) {
      print('Error erasing user: $e');
    }
  }

  Future<void> fetchUser() async {
    try {
      final SharedPreferences sharedUser =
          await SharedPreferences.getInstance();
      userModel = UserModel.fromJson(jsonDecode(sharedUser.getString('user')!));
      accessToken.value = sharedUser.getString('token')??"";

      update();
    } catch (e) {
      print('Error fetching user: $e');
    }
  }

  void navigateToNextScreen() async {
fetchUser();
    Future.delayed(Duration(seconds: 2), () {
      Get.offAll(
            () => accessToken.value==""?OnboardingView():userModel?.userType=="Organizer"?OrganizerNavigationView(currentIndex: 0):NavigationView(currentIndex: 0),
      );
    });

  }


  logout() async {
     Get.delete<HomeController>();
     Get.delete<EventController>();
     Get.delete<NavigationController>();
    Get.delete<ChatController>();
    Get.delete<ProfileController>();

    await eraseUser();
    Get.offAll(
      () => LoginView(),
    );
  }

  fetchProfileDetails({required String? userId}) async {
    try {
      Widgets.showLoader("Fetching profile");

      var request = {};
      request['access_token']=accessToken.value;
      request['user_id']=userId;
      var response = await ApiService.postData(
          Endpoints.getMyProfileApi,
        request,
      );
      if (response.status == true) {
        UserModel user=UserModel.fromJson(response.data['body']);
       await saveUser(user);
       await fetchUser();
      } else {
      }}
    catch (exception) {
    }finally{   Widgets.hideLoader();}
  }

  fetchProfileDetailsBackground() async {
    try {

      var request = {};
      request['access_token']=accessToken.value;
      request['user_id']=userModel?.userId??0;
      var response = await ApiService.postData(
        Endpoints.getMyProfileApi,
        request,
      );
      if (response.status == true) {
        UserModel user=UserModel.fromJson(response.data['body']);
        await saveUser(user);
        await fetchUser();
      } else {
      }}
    catch (exception) {
    }finally{  ;}
  }







//
  // void requestDeletionAccount() async {
  //   try {
  //     Widgets.showLoader("Loading.. ");
  //
  //     var response = await ApiService.postData(Endpoints.deleteAccount, {});
  //     Get.back();
  //     Widgets.hideLoader();
  //
  //     if (response.status == true) {
  //       Widgets.showSnackBar("Success", "Account Deleted");
  //       logout();
  //     } else {}
  //   } catch (e) {
  //     Widgets.hideLoader();
  //   }
  // }
  //
  void requestLogoutAccount() async {
    try {
      Get.back();
      Widgets.showLoader("Loading.. ");

      var response = await ApiService.postData(Endpoints.logoutApi, {"access_token":accessToken.value??""});
      Get.back();
      Widgets.hideLoader();

      if (response.status == true) {
        logout();
      } else {}
    } catch (e) {
      Widgets.hideLoader();
    }finally {
      Widgets.hideLoader();   logout();
    }
  }
  //
  updateFcmToken() async {
    try {
      String? token = await getToken();
      var response = await ApiService.postData(
          Endpoints.updateFCM, {"token": token,"access_token":accessToken.value??""});
    } catch (e) {}
  }
  //
  // void updateInterests(String interests) async {
  //   try {
  //     Widgets.showLoader("Setting up account...");
  //     var response = await ApiService.postData(
  //         Endpoints.interests, {"interests": interests});
  //     Widgets.hideLoader();
  //     if (response.status == true) {
  //       // Get.offAll(() => const AppNavigationView());
  //     } else {}
  //   } catch (e) {
  //   } finally {
  //     Widgets.hideLoader();
  //   }
  // }
  //
  getToken() async {
    final FirebaseMessaging Fcm = FirebaseMessaging.instance;

    String? token = await Fcm.getToken();
    log("Firebase Messaging Token: $token");
    return token;
  }
  //
  // fetchMemberDetail(String memberId) async {
  //   try {
  //     var response = await ApiService.postData(
  //         "${Endpoints.memberDetail}?user_id=$memberId", {});
  //
  //     if (response.status == true) {
  //       // member.value = MemberDetail.fromJson(response.data['member']);
  //       update();
  //     }
  //   } finally {
  //     ;
  //     update();
  //   }
  // }
  //
  // getInfo() async {
  //   // SharedPreferences pref = await SharedPreferences.getInstance();
  //   // bool? checkInstall = pref.getBool(PrefDataString.isInstall);
  //   // bool? isSignUp = pref.getBool(PrefDataString.isSignUp);
  //   // bool? isLogin = pref.getBool(PrefDataString.isLogin);
  //   // bool? isCompleteProfile = pref.getBool("isCompleteProfile");
  //   //
  //   //
  //   // debugPrint("checkInstall>>>$checkInstall");
  //   // debugPrint("isSignUp>>>$isSignUp");
  //   // debugPrint("isLogin>>$isLogin");
  //   // debugPrint("isCompleteProfile>>$isCompleteProfile");
  //   //
  //   // if (checkInstall == null || checkInstall == false) {
  //   //   Future.delayed(Duration(seconds: 3), () {
  //   //     // ignore: use_build_context_synchronously
  //   //     CustomNavigator.pushAndRemoveUntill(context, WelcomeScreen());
  //   //   });
  //   // } else if (isLogin == true) {
  //   //   Future.delayed(Duration(seconds: 3), () {
  //   //     // ignore: use_build_context_synchronously
  //   //     CustomNavigator.pushAndRemoveUntill(
  //   //         context, DashboardScreen(currentIndex: 0));
  //   //   });
  //   // } else {
  //   //   Future.delayed(Duration(seconds: 3), () {
  //   //     // ignore: use_build_context_synchronously
  //   //     CustomNavigator.pushAndRemoveUntill(context, LoginScreen());
  //   //   });
  //   // }
  // }





  void submitReport(BuildContext context, String reason, String description,String userId) async {
    try {
      Widgets.showLoader("Submitting report...");

      final success = await reportUserFromProfile(
        flaggedUserId: userId.toString(),
        reason: reason,
        reportDescription: description,
      );

      Widgets.hideLoader();

      if (success) {
        Widgets.showSnackBar(
            "Success",
            "Thank you for your report. We'll review it shortly."
        );
      } else {
        Widgets.showSnackBar(
            "Error",
            "Failed to submit report. Please try again later."
        );
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("Error", "An unexpected error occurred");
      print("Error submitting report: $e");
    }
  }

  // Report reasons list
   List<String> reportReasons = [
    'Hate Speech or Discrimination',
    'Illegal Activity (sex work, underage users)',
    'Scan or Fraud',
    'Inappropriate Contents (Obscene photos, nudity)',
  ];

  /// Report a user from their profile
  Future<bool> reportUserFromProfile({
    required String flaggedUserId,
    required String reason,
    required String reportDescription,
  }) async {
    try {
      final requestBody = {
        'access_token': accessToken.value,
        'reason': reason,
        'flagged_user_id': flaggedUserId,
        'report_desc': reportDescription,
        'from': 'user_profile',
        'roomId': '',
      };

      log('Reporting user from profile: $requestBody');

      final response = await ApiService.postData(
        Endpoints.reportUser,
        requestBody,
      );

      if (response.status == true) {
        log('User reported successfully from profile');
        return true;
      } else {
        log('Failed to report user from profile: ${response.message}');
        return false;
      }
    } catch (e) {
      log('Error reporting user from profile: $e');
      return false;
    }
  }

  /// Report a user from chat room
  Future<bool> reportUserFromChat({
    required String flaggedUserId,
    required String reason,
    required String reportDescription,
    required String roomId,
  }) async {
    try {
      final requestBody = {
        'access_token': accessToken.value,
        'reason': reason,
        'flagged_user_id': flaggedUserId,
        'report_desc': reportDescription,
        'from': 'chat',
        'roomId': roomId,
      };

      log('Reporting user from chat: $requestBody');

      final response = await ApiService.postData(
        Endpoints.reportUser,
        requestBody,
      );

      if (response.status == true) {
        log('User reported successfully from chat');
        return true;
      } else {
        log('Failed to report user from chat: ${response.message}');
        return false;
      }
    } catch (e) {
      log('Error reporting user from chat: $e');
      return false;
    }
  }

  /// Generic report method that determines the context
  Future<bool> reportUser({
    required String flaggedUserId,
    required String reason,
    required String reportDescription,
    String? roomId,
    bool isFromChat = false,
  }) async {
    if (isFromChat && roomId != null && roomId.isNotEmpty) {
      return await reportUserFromChat(
        flaggedUserId: flaggedUserId,
        reason: reason,
        reportDescription: reportDescription,
        roomId: roomId,
      );
    } else {
      return await reportUserFromProfile(
        flaggedUserId: flaggedUserId,
        reason: reason,
        reportDescription: reportDescription,
      );
    }
  }

  /// Validate report reason
  bool isValidReason(String reason) {
    return reportReasons.contains(reason);
  }

  /// Get formatted report reasons for UI
  List<String> getReportReasons() {
    return List.from(reportReasons);
  }
}
