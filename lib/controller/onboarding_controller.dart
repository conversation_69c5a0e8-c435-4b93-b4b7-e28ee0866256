import 'package:banana/view/authentication/view/login_view.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../core/constants/assets_constants.dart';
import '../view/starting/view/welcome_view.dart';

class OnboardingController extends GetxController {
  var currentPage = 0.obs;
  PageController pageController = PageController();

  void nextPage() {
    if (currentPage.value < 2) {
      pageController.nextPage(duration: Duration(milliseconds: 300), curve: Curves.ease);
    } else {
      Get.offAll(()=>WelcomeView());
    }
  }
  final List<Map<String, String>> onboardingData = [
    {
      "image": Assets.onboardingImg1,
      "title": "Find Your Next Adventure!",
      "description": "Whether you're looking to attend an exciting event or create your own, Go Bananas helps you explore and share amazing experiences with like-minded people."
    },
    {
      "image": Assets.onboardingImg2,
      "title": "Make Plans, Meet New People!",
      "description": "Send or receive invites to join events and activities. Whether it's a concert, a game night, or a casual meetup, Go Bananas makes it easy to find company for any occasion."
    }, {
      "image": Assets.onboardingImg3,
      "title": "Show Who You Really Are!",
      "description": "Build your profile with your interests and hobbies. Connect with people who share your passions and turn every event into a memorable experience."
    },
  ];

  void skip() {
    Get.offAll(()=>LoginView());
  }
}
