import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../core/services/purchase_service.dart';
import '../model/subscription_model.dart';
import '../core/widgets/widgets.dart';

class PurchaseController extends GetxController {
  PurchaseService? _purchaseService;

  PurchaseService? get purchaseService {
    try {
      _purchaseService ??= Get.find<PurchaseService>();
      return _purchaseService;
    } catch (e) {
      // Service not available (e.g., during testing)
      return null;
    }
  }
  
  // Observable states
  var selectedPlan = SubscriptionPlans.weeklyId.obs;
  var isLoading = false.obs;
  var isPurchasing = false.obs;
  var availablePlans = <SubscriptionPlan>[].obs;
  var subscriptionStatus = Rx<SubscriptionStatus?>(null);
  
  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }
  
  Future<void> _initializeController() async {
    try {
      isLoading.value = true;
      
      // Load default plans
      availablePlans.value = SubscriptionPlans.getDefaultPlans();
      
      // Wait for purchase service to initialize
      await _waitForPurchaseService();
      
      // Update plans with real prices from store
      await _updatePlansWithStorePrices();
      
      // Check current subscription status
      await checkSubscriptionStatus();
      
    } catch (e) {
      log('Error initializing purchase controller: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  Future<void> _waitForPurchaseService() async {
    if (purchaseService == null) return;

    int attempts = 0;
    while (!purchaseService!.isAvailable.value && attempts < 10) {
      await Future.delayed(const Duration(milliseconds: 500));
      attempts++;
    }
  }
  
  Future<void> _updatePlansWithStorePrices() async {
    if (purchaseService == null) return;

    try {
      final updatedPlans = <SubscriptionPlan>[];

      for (final plan in availablePlans) {
        final storePrice = purchaseService!.getProductPrice(plan.id);
        if (storePrice.isNotEmpty) {
          final updatedPlan = SubscriptionPlan(
            id: plan.id,
            name: plan.name,
            description: plan.description,
            price: storePrice,
            period: plan.period,
            label: plan.label,
            isPopular: plan.isPopular,
            features: plan.features,
            originalPrice: plan.originalPrice,
            discount: plan.discount,
          );
          updatedPlans.add(updatedPlan);
        } else {
          updatedPlans.add(plan);
        }
      }

      availablePlans.value = updatedPlans;

    } catch (e) {
      log('Error updating plans with store prices: $e');
    }
  }
  
  void selectPlan(String planId) {
    selectedPlan.value = planId;
  }
  
  SubscriptionPlan? getSelectedPlan() {
    return availablePlans.firstWhereOrNull((plan) => plan.id == selectedPlan.value);
  }
  
  Future<void> purchaseSelectedPlan() async {
    if (isPurchasing.value) return;
    
    try {
      isPurchasing.value = true;
      Widgets.showLoader("Processing purchase...");
      
      final selectedPlanData = getSelectedPlan();
      if (selectedPlanData == null) {
        throw Exception('No plan selected');
      }
      
      // Check if purchase service is available
      if (purchaseService == null || !purchaseService!.isAvailable.value) {
        throw Exception('In-app purchases are not available');
      }

      // Initiate purchase
      await purchaseService!.purchaseProduct(selectedPlan.value);

      // Listen for purchase completion
      await _waitForPurchaseCompletion();

      Widgets.hideLoader();

      if (purchaseService!.hasActiveSubscription.value) {
        _showSuccessMessage();
        await checkSubscriptionStatus();
        Get.back(); // Close subscription screen
      } else {
        throw Exception('Purchase was not completed successfully');
      }
      
    } catch (e) {
      Widgets.hideLoader();
      _showErrorMessage(e.toString());
      log('Error purchasing plan: $e');
    } finally {
      isPurchasing.value = false;
    }
  }
  
  Future<void> _waitForPurchaseCompletion() async {
    if (purchaseService == null) return;

    int attempts = 0;
    while (purchaseService!.isPurchasing.value && attempts < 30) {
      await Future.delayed(const Duration(seconds: 1));
      attempts++;
    }
  }
  
  Future<void> restorePurchases() async {
    if (purchaseService == null) {
      _showErrorMessage("Purchase service not available");
      return;
    }

    try {
      Widgets.showLoader("Restoring purchases...");

      await purchaseService!.restorePurchases();
      await checkSubscriptionStatus();

      Widgets.hideLoader();

      if (purchaseService!.hasActiveSubscription.value) {
        Fluttertoast.showToast(
          msg: "Purchases restored successfully!",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.green,
          textColor: Colors.white,
        );
      } else {
        Fluttertoast.showToast(
          msg: "No previous purchases found",
          toastLength: Toast.LENGTH_SHORT,
          gravity: ToastGravity.BOTTOM,
          backgroundColor: Colors.orange,
          textColor: Colors.white,
        );
      }

    } catch (e) {
      Widgets.hideLoader();
      _showErrorMessage("Failed to restore purchases: ${e.toString()}");
      log('Error restoring purchases: $e');
    }
  }
  
  Future<void> checkSubscriptionStatus() async {
    if (purchaseService == null) return;

    try {
      await purchaseService!.checkSubscriptionStatus();

      // Update local subscription status
      if (purchaseService!.hasActiveSubscription.value) {
        subscriptionStatus.value = SubscriptionStatus(
          isActive: true,
          currentPlan: purchaseService!.currentSubscription.value,
          status: 'active',
        );
      } else {
        subscriptionStatus.value = SubscriptionStatus(
          isActive: false,
          status: 'inactive',
        );
      }

    } catch (e) {
      log('Error checking subscription status: $e');
    }
  }
  
  bool get hasActiveSubscription => purchaseService?.hasActiveSubscription.value ?? false;

  String get currentSubscriptionPlan => purchaseService?.currentSubscription.value ?? '';

  bool get isPurchaseServiceAvailable => purchaseService?.isAvailable.value ?? false;
  
  void _showSuccessMessage() {
    Fluttertoast.showToast(
      msg: "Subscription activated successfully!",
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.green,
      textColor: Colors.white,
    );
  }
  
  void _showErrorMessage(String message) {
    Fluttertoast.showToast(
      msg: message.contains('Exception:') 
          ? message.replaceAll('Exception: ', '') 
          : message,
      toastLength: Toast.LENGTH_LONG,
      gravity: ToastGravity.BOTTOM,
      backgroundColor: Colors.red,
      textColor: Colors.white,
    );
  }
  
  // Helper methods for UI
  String getPlanDisplayPrice(String planId) {
    final plan = availablePlans.firstWhereOrNull((p) => p.id == planId);
    return plan?.price ?? '';
  }
  
  String getPlanDisplayPeriod(String planId) {
    final plan = availablePlans.firstWhereOrNull((p) => p.id == planId);
    return plan?.period ?? '';
  }
  
  String getPlanDisplayLabel(String planId) {
    final plan = availablePlans.firstWhereOrNull((p) => p.id == planId);
    return plan?.label ?? '';
  }
  
  bool isPlanPopular(String planId) {
    final plan = availablePlans.firstWhereOrNull((p) => p.id == planId);
    return plan?.isPopular ?? false;
  }
  
  List<String> getPlanFeatures(String planId) {
    final plan = availablePlans.firstWhereOrNull((p) => p.id == planId);
    return plan?.features ?? [];
  }
}
