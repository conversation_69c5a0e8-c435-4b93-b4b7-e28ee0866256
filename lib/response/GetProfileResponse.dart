
class GetProfileResponse {
  int? statusCode;
  Headers? headers;
  Body? body;

  GetProfileResponse({this.statusCode, this.headers, this.body});

  GetProfileResponse.fromJson(Map<String, dynamic> json) {
    statusCode = json['statusCode'];
    headers =
    json['headers'] != null ? Headers.fromJson(json['headers']) : null;
    body = json['body'] != null ? Body.fromJson(json['body']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['statusCode'] = statusCode;
    if (headers != null) {
      data['headers'] = headers!.toJson();
    }
    if (body != null) {
      data['body'] = body!.toJson();
    }
    return data;
  }
}

class Headers {
  String? contentType;

  Headers({this.contentType});

  Headers.fromJson(Map<String, dynamic> json) {
    contentType = json['Content-Type'];
  }

  Map<String, dynamic> toJ<PERSON>() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Content-Type'] = contentType;
    return data;
  }
}

class Body {
  int? userId;
  String? name;
  String? profileThumbnail;
  int? age;
  String? intention;
  String? interestedIn;
  String? height;
  String? spokenLanguages;

  Body(
      {this.userId,
        this.name,
        this.profileThumbnail,
        this.age,
        this.intention,
        this.interestedIn,
        this.height,
        this.spokenLanguages});

  Body.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    name = json['name'];
    profileThumbnail = json['profile_thumbnail'];
    age = json['age'];
    intention = json['intention'];
    interestedIn = json['interested_in'];
    height = json['height'];
    spokenLanguages = json['spoken_languages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['user_id'] = userId;
    data['name'] = name;
    data['profile_thumbnail'] = profileThumbnail;
    data['age'] = age;
    data['intention'] = intention;
    data['interested_in'] = interestedIn;
    data['height'] = height;
    data['spoken_languages'] = spokenLanguages;
    return data;
  }
}
