import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/planning_options_controller.dart';

class PlanningOptionsView extends GetView<PlanningOptionsController> {
  const PlanningOptionsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Planning & Options'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.settings,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'Planning & Options',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This screen will contain:\n'
              '• Offer modules (Battery, Wallbox, etc.)\n'
              '• Financing options\n'
              '• Scaffolding arrangements\n'
              '• Building plans upload\n'
              '• Additional notes',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: controller.continueToNext,
          child: const Text('Continue to Photo Upload'),
        ),
      ),
    );
  }
}
