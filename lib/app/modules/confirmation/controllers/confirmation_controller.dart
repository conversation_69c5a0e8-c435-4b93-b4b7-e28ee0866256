import 'package:get/get.dart';
import '../../../data/models/project_model.dart';
import '../../../routes/app_pages.dart';

class ConfirmationController extends GetxController {
  Project? currentProject;

  @override
  void onInit() {
    super.onInit();
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      currentProject = arguments['project'];
    }
  }

  void submitProject() {
    // Show success message and navigate back to project overview
    Get.snackbar(
      'Success',
      'Project submitted successfully!',
      snackPosition: SnackPosition.BOTTOM,
    );
    
    Get.offAllNamed(Routes.PROJECT_OVERVIEW);
  }
}
