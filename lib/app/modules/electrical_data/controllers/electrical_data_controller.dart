import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../../data/models/project_model.dart';
import '../../../data/services/storage_service.dart';
import '../../../data/services/localization_service.dart';
import '../../../routes/app_pages.dart';

class ElectricalDataController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();
  final LocalizationService _strings = Get.find<LocalizationService>();

  final formKey = GlobalKey<FormState>();

  // Form Controllers
  final meterBoxNumberController = TextEditingController();
  final annualConsumptionController = TextEditingController();
  final otherConsumersController = TextEditingController();

  // Observable variables
  final hasEmptyConduit = false.obs;
  final hasSufficientSpace = false.obs;
  final hasEVChargingStation = false.obs;
  final hasAirConditioning = false.obs;
  final hasHeatPump = false.obs;
  final powerMeasurementRequested = false.obs;
  final isLoading = false.obs;
  final meterBoxPhotoPath = Rxn<String>();

  // Project data
  Project? currentProject;
  final ImagePicker _picker = ImagePicker();

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  @override
  void onClose() {
    _disposeControllers();
    super.onClose();
  }

  void _initializeData() {
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      currentProject = arguments['project'];
      if (currentProject?.electricalData != null) {
        _loadElectricalData();
      }
    }
  }

  void _loadElectricalData() {
    final electricalData = currentProject!.electricalData!;

    meterBoxNumberController.text = electricalData.meterBoxNumber;
    annualConsumptionController.text = electricalData.annualConsumption.toString();
    otherConsumersController.text = electricalData.otherConsumers ?? '';

    hasEmptyConduit.value = electricalData.hasEmptyConduit;
    hasSufficientSpace.value = electricalData.hasSufficientSpace;
    hasEVChargingStation.value = electricalData.hasEVChargingStation;
    hasAirConditioning.value = electricalData.hasAirConditioning;
    hasHeatPump.value = electricalData.hasHeatPump;
    powerMeasurementRequested.value = electricalData.powerMeasurementRequested;
    meterBoxPhotoPath.value = electricalData.meterBoxPhotoPath;
  }

  void _disposeControllers() {
    meterBoxNumberController.dispose();
    annualConsumptionController.dispose();
    otherConsumersController.dispose();
  }

  Future<void> takeMeterBoxPhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photo != null) {
        meterBoxPhotoPath.value = photo.path;
        Get.snackbar(
          _strings.getString('success'),
          'Meter box photo added successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to take photo: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> selectMeterBoxPhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photo != null) {
        meterBoxPhotoPath.value = photo.path;
        Get.snackbar(
          _strings.getString('success'),
          'Meter box photo selected successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to select photo: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void removeMeterBoxPhoto() {
    meterBoxPhotoPath.value = null;
  }

  Future<void> saveAndContinue() async {
    if (!formKey.currentState!.validate()) return;

    isLoading.value = true;

    try {
      final electricalData = ElectricalData(
        meterBoxNumber: meterBoxNumberController.text.trim(),
        hasEmptyConduit: hasEmptyConduit.value,
        hasSufficientSpace: hasSufficientSpace.value,
        meterBoxPhotoPath: meterBoxPhotoPath.value,
        annualConsumption: double.parse(annualConsumptionController.text.trim()),
        hasEVChargingStation: hasEVChargingStation.value,
        hasAirConditioning: hasAirConditioning.value,
        hasHeatPump: hasHeatPump.value,
        otherConsumers: otherConsumersController.text.trim().isEmpty
            ? null
            : otherConsumersController.text.trim(),
        powerMeasurementRequested: powerMeasurementRequested.value,
      );

      currentProject!.electricalData = electricalData;
      currentProject!.lastChange = DateTime.now();

      await _storageService.saveProject(currentProject!);

      Get.toNamed(Routes.PLANNING_OPTIONS, arguments: {
        'project': currentProject,
      });

    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to save electrical data: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> saveDraft() async {
    final draftData = {
      'meterBoxNumber': meterBoxNumberController.text,
      'hasEmptyConduit': hasEmptyConduit.value,
      'hasSufficientSpace': hasSufficientSpace.value,
      'meterBoxPhotoPath': meterBoxPhotoPath.value,
      'annualConsumption': annualConsumptionController.text,
      'hasEVChargingStation': hasEVChargingStation.value,
      'hasAirConditioning': hasAirConditioning.value,
      'hasHeatPump': hasHeatPump.value,
      'otherConsumers': otherConsumersController.text,
      'powerMeasurementRequested': powerMeasurementRequested.value,
    };

    await _storageService.saveDraft('electrical_data', draftData);

    Get.snackbar(
      _strings.getString('success'),
      _strings.getString('progressSaved'),
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Validation methods
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  String? validateConsumption(String? value) {
    if (value == null || value.trim().isEmpty) {
      return _strings.getString('annualConsumptionRequired');
    }

    final consumption = double.tryParse(value.trim());
    if (consumption == null || consumption < 0) {
      return _strings.getString('validConsumptionRequired');
    }

    return null;
  }
}
