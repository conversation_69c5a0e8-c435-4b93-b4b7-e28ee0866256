import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../../../data/services/localization_service.dart';
import '../controllers/electrical_data_controller.dart';

class ElectricalDataView extends GetView<ElectricalDataController> {
  const ElectricalDataView({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = Get.find<LocalizationService>();

    return Scaffold(
      appBar: AppBar(
        title: Text(strings.getString('meterElectricalConnection')),
        actions: [
          TextButton(
            onPressed: controller.saveDraft,
            child: Text(
              strings.getString('saveDraft'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: controller.formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Meter Box Number
              TextFormField(
                controller: controller.meterBoxNumberController,
                decoration: InputDecoration(
                  labelText: '${strings.getString('meterBoxNumber')} *',
                  hintText: strings.getString('enterMeterBoxNumber'),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) => controller.validateRequired(value, strings.getString('meterBoxNumber')),
              ),
              const SizedBox(height: 16),

              // Empty Conduit and Space Availability
              Obx(() => CheckboxListTile(
                title: Text(strings.getString('emptyConduitPresent')),
                value: controller.hasEmptyConduit.value,
                onChanged: (value) {
                  controller.hasEmptyConduit.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),

              Obx(() => CheckboxListTile(
                title: Text(strings.getString('sufficientSpaceAvailable')),
                value: controller.hasSufficientSpace.value,
                onChanged: (value) {
                  controller.hasSufficientSpace.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),
              const SizedBox(height: 16),

              // Meter Box Photo Section
              Text(
                strings.getString('meterBoxPhoto'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              // Photo buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: controller.takeMeterBoxPhoto,
                      icon: const Icon(Icons.camera_alt),
                      label: Text(strings.getString('takePhoto')),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: controller.selectMeterBoxPhoto,
                      icon: const Icon(Icons.photo_library),
                      label: Text(strings.getString('selectFromGallery')),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Photo Display
              Obx(() => controller.meterBoxPhotoPath.value == null
                  ? Container(
                      height: 120,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.photo_camera,
                              size: 48,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'No meter box photo added',
                              style: TextStyle(color: Colors.grey.shade600),
                            ),
                          ],
                        ),
                      ),
                    )
                  : Stack(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.file(
                            File(controller.meterBoxPhotoPath.value!),
                            height: 200,
                            width: double.infinity,
                            fit: BoxFit.cover,
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: GestureDetector(
                            onTap: controller.removeMeterBoxPhoto,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
              const SizedBox(height: 24),

              // Consumption Data Section
              Text(
                strings.getString('consumptionData'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              TextFormField(
                controller: controller.annualConsumptionController,
                decoration: InputDecoration(
                  labelText: '${strings.getString('annualElectricityConsumption')} *',
                  hintText: strings.getString('enterAnnualConsumption'),
                  border: const OutlineInputBorder(),
                  suffixText: 'kWh',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: controller.validateConsumption,
              ),
              const SizedBox(height: 24),

              // Special Consumers Section
              Text(
                strings.getString('specialConsumers'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              Obx(() => CheckboxListTile(
                title: Text(strings.getString('evChargingStation')),
                value: controller.hasEVChargingStation.value,
                onChanged: (value) {
                  controller.hasEVChargingStation.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),

              Obx(() => CheckboxListTile(
                title: Text(strings.getString('airConditioning')),
                value: controller.hasAirConditioning.value,
                onChanged: (value) {
                  controller.hasAirConditioning.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),

              Obx(() => CheckboxListTile(
                title: Text(strings.getString('heatPump')),
                value: controller.hasHeatPump.value,
                onChanged: (value) {
                  controller.hasHeatPump.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),

              const SizedBox(height: 16),

              // Other Consumers
              TextFormField(
                controller: controller.otherConsumersController,
                decoration: InputDecoration(
                  labelText: strings.getString('other'),
                  hintText: strings.getString('enterOtherConsumers'),
                  border: const OutlineInputBorder(),
                ),
                maxLines: 2,
              ),
              const SizedBox(height: 24),

              // Power Measurement Request
              Obx(() => CheckboxListTile(
                title: Text(strings.getString('powerMeasurementRequested')),
                value: controller.powerMeasurementRequested.value,
                onChanged: (value) {
                  controller.powerMeasurementRequested.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),
              const SizedBox(height: 32),

              // Continue Button
              SizedBox(
                width: double.infinity,
                child: Obx(() => ElevatedButton(
                  onPressed: controller.isLoading.value
                      ? null
                      : controller.saveAndContinue,
                  child: controller.isLoading.value
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(strings.getString('continueToPlanningOptions')),
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
