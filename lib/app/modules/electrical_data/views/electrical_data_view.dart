import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/electrical_data_controller.dart';

class ElectricalDataView extends GetView<ElectricalDataController> {
  const ElectricalDataView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Meter & Electrical Connection'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.electrical_services,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'Meter & Electrical Connection',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This screen will contain:\n'
              '• Meter box number\n'
              '• Empty conduit availability\n'
              '• Space availability\n'
              '• Meter box photo\n'
              '• Annual consumption data\n'
              '• Special consumers (EV, AC, Heat pump)\n'
              '• Power measurement request',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: controller.continueToNext,
          child: const Text('Continue to Planning & Options'),
        ),
      ),
    );
  }
}
