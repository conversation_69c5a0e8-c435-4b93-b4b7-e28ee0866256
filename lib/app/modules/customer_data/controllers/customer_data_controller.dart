import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../data/models/project_model.dart';
import '../../../data/services/storage_service.dart';
import '../../../routes/app_pages.dart';

class CustomerDataController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();
  
  final formKey = GlobalKey<FormState>();
  
  // Form Controllers
  final projectNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final firstNameController = TextEditingController();
  final streetController = TextEditingController();
  final houseNumberController = TextEditingController();
  final zipCodeController = TextEditingController();
  final cityController = TextEditingController();
  final phoneController = TextEditingController();
  final mobileController = TextEditingController();
  final emailController = TextEditingController();
  final preferredContactHoursController = TextEditingController();
  final companyController = TextEditingController();
  final commentsController = TextEditingController();
  
  // Observable variables
  final selectedBuildingType = BuildingType.singleFamily.obs;
  final selectedYear = DateTime.now().year.obs;
  final isBuildingOwner = true.obs;
  final interestedInTenantElectricity = false.obs;
  final siteVisitDate = Rxn<DateTime>();
  final isLoading = false.obs;
  
  // Project data
  Project? currentProject;
  bool isNewProject = true;

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  @override
  void onClose() {
    _disposeControllers();
    super.onClose();
  }

  void _initializeData() {
    final arguments = Get.arguments as Map<String, dynamic>?;
    
    if (arguments != null) {
      isNewProject = arguments['isNewProject'] ?? true;
      currentProject = arguments['project'];
      
      if (currentProject != null && !isNewProject) {
        _loadProjectData();
      }
    }
  }

  void _loadProjectData() {
    if (currentProject?.customerData != null) {
      final customerData = currentProject!.customerData!;
      
      projectNameController.text = currentProject!.projectName;
      lastNameController.text = customerData.lastName;
      firstNameController.text = customerData.firstName;
      streetController.text = customerData.street;
      houseNumberController.text = customerData.houseNumber;
      zipCodeController.text = customerData.zipCode;
      cityController.text = customerData.city;
      phoneController.text = customerData.phone;
      mobileController.text = customerData.mobile;
      emailController.text = customerData.email;
      preferredContactHoursController.text = customerData.preferredContactHours;
      companyController.text = customerData.company ?? '';
      commentsController.text = customerData.comments ?? '';
      
      selectedBuildingType.value = customerData.buildingType;
      selectedYear.value = customerData.yearOfConstruction;
      isBuildingOwner.value = customerData.isBuildingOwner;
      interestedInTenantElectricity.value = customerData.interestedInTenantElectricity;
      siteVisitDate.value = customerData.siteVisitDate;
    }
  }

  void _disposeControllers() {
    projectNameController.dispose();
    lastNameController.dispose();
    firstNameController.dispose();
    streetController.dispose();
    houseNumberController.dispose();
    zipCodeController.dispose();
    cityController.dispose();
    phoneController.dispose();
    mobileController.dispose();
    emailController.dispose();
    preferredContactHoursController.dispose();
    companyController.dispose();
    commentsController.dispose();
  }

  List<int> get yearList {
    final currentYear = DateTime.now().year;
    return List.generate(100, (index) => currentYear - index);
  }

  void selectSiteVisitDate() async {
    final date = await showDatePicker(
      context: Get.context!,
      initialDate: siteVisitDate.value ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      siteVisitDate.value = date;
    }
  }

  String get formattedSiteVisitDate {
    if (siteVisitDate.value == null) return 'Select Date';
    return DateFormat('MMM dd, yyyy').format(siteVisitDate.value!);
  }

  Future<void> saveAndContinue() async {
    if (!formKey.currentState!.validate()) return;

    isLoading.value = true;

    try {
      final customerData = CustomerData(
        lastName: lastNameController.text.trim(),
        firstName: firstNameController.text.trim(),
        street: streetController.text.trim(),
        houseNumber: houseNumberController.text.trim(),
        zipCode: zipCodeController.text.trim(),
        city: cityController.text.trim(),
        phone: phoneController.text.trim(),
        mobile: mobileController.text.trim(),
        email: emailController.text.trim(),
        preferredContactHours: preferredContactHoursController.text.trim(),
        company: companyController.text.trim().isEmpty 
            ? null 
            : companyController.text.trim(),
        buildingType: selectedBuildingType.value,
        yearOfConstruction: selectedYear.value,
        isBuildingOwner: isBuildingOwner.value,
        interestedInTenantElectricity: interestedInTenantElectricity.value,
        comments: commentsController.text.trim().isEmpty 
            ? null 
            : commentsController.text.trim(),
        siteVisitDate: siteVisitDate.value,
      );

      if (currentProject == null) {
        currentProject = Project(
          projectName: projectNameController.text.trim(),
          customerData: customerData,
        );
      } else {
        currentProject!.projectName = projectNameController.text.trim();
        currentProject!.customerData = customerData;
        currentProject!.lastChange = DateTime.now();
      }

      await _storageService.saveProject(currentProject!);

      Get.toNamed(Routes.ROOF_DATA, arguments: {
        'project': currentProject,
      });

    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to save customer data: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> saveDraft() async {
    final draftData = {
      'projectName': projectNameController.text,
      'lastName': lastNameController.text,
      'firstName': firstNameController.text,
      'street': streetController.text,
      'houseNumber': houseNumberController.text,
      'zipCode': zipCodeController.text,
      'city': cityController.text,
      'phone': phoneController.text,
      'mobile': mobileController.text,
      'email': emailController.text,
      'preferredContactHours': preferredContactHoursController.text,
      'company': companyController.text,
      'comments': commentsController.text,
      'buildingType': selectedBuildingType.value.index,
      'yearOfConstruction': selectedYear.value,
      'isBuildingOwner': isBuildingOwner.value,
      'interestedInTenantElectricity': interestedInTenantElectricity.value,
      'siteVisitDate': siteVisitDate.value?.toIso8601String(),
    };

    await _storageService.saveDraft('customer_data', draftData);
    
    Get.snackbar(
      'Draft Saved',
      'Your progress has been saved',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Validation methods
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  String? validateEmail(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Email is required';
    }
    if (!GetUtils.isEmail(value.trim())) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  String? validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Phone number is required';
    }
    if (value.trim().length < 10) {
      return 'Please enter a valid phone number';
    }
    return null;
  }

  String? validateZipCode(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Zip code is required';
    }
    if (value.trim().length < 5) {
      return 'Please enter a valid zip code';
    }
    return null;
  }
}
