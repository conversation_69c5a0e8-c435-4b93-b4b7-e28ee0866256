import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/project_model.dart';
import '../controllers/customer_data_controller.dart';

class CustomerDataView extends GetView<CustomerDataController> {
  const CustomerDataView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Customer & Location'),
        actions: [
          TextButton(
            onPressed: controller.saveDraft,
            child: const Text(
              'Save Draft',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: controller.formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Project Name
              TextFormField(
                controller: controller.projectNameController,
                decoration: const InputDecoration(
                  labelText: 'Project Name *',
                  hintText: 'Enter project name',
                ),
                validator: (value) => controller.validateRequired(value, 'Project name'),
              ),
              const SizedBox(height: 16),

              // Customer Data Section
              const Text(
                'Customer Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Name Fields
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: controller.firstNameController,
                      decoration: const InputDecoration(
                        labelText: 'First Name *',
                      ),
                      validator: (value) => controller.validateRequired(value, 'First name'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: controller.lastNameController,
                      decoration: const InputDecoration(
                        labelText: 'Last Name *',
                      ),
                      validator: (value) => controller.validateRequired(value, 'Last name'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Address Fields
              Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: TextFormField(
                      controller: controller.streetController,
                      decoration: const InputDecoration(
                        labelText: 'Street *',
                      ),
                      validator: (value) => controller.validateRequired(value, 'Street'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: controller.houseNumberController,
                      decoration: const InputDecoration(
                        labelText: 'House No. *',
                      ),
                      validator: (value) => controller.validateRequired(value, 'House number'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: controller.zipCodeController,
                      decoration: const InputDecoration(
                        labelText: 'Zip Code *',
                      ),
                      validator: controller.validateZipCode,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: controller.cityController,
                      decoration: const InputDecoration(
                        labelText: 'City *',
                      ),
                      validator: (value) => controller.validateRequired(value, 'City'),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Contact Fields
              TextFormField(
                controller: controller.phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone *',
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: controller.validatePhone,
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: controller.mobileController,
                decoration: const InputDecoration(
                  labelText: 'Mobile *',
                  prefixIcon: Icon(Icons.smartphone),
                ),
                keyboardType: TextInputType.phone,
                validator: controller.validatePhone,
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: controller.emailController,
                decoration: const InputDecoration(
                  labelText: 'Email *',
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: controller.validateEmail,
              ),
              const SizedBox(height: 16),

              TextFormField(
                controller: controller.preferredContactHoursController,
                decoration: const InputDecoration(
                  labelText: 'Preferred Contact Hours *',
                  hintText: 'e.g., 9 AM - 5 PM',
                ),
                validator: (value) => controller.validateRequired(value, 'Preferred contact hours'),
              ),
              const SizedBox(height: 16),

              // Company (Optional)
              TextFormField(
                controller: controller.companyController,
                decoration: const InputDecoration(
                  labelText: 'Company (Optional)',
                  hintText: 'For business customers',
                ),
              ),
              const SizedBox(height: 24),

              // Building Information Section
              const Text(
                'Building Information',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // Building Type Dropdown
              Obx(() => DropdownButtonFormField<BuildingType>(
                value: controller.selectedBuildingType.value,
                decoration: const InputDecoration(
                  labelText: 'Building Type *',
                ),
                items: BuildingType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.displayName),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectedBuildingType.value = value;
                  }
                },
              )),
              const SizedBox(height: 16),

              // Year of Construction
              Obx(() => DropdownButtonFormField<int>(
                value: controller.selectedYear.value,
                decoration: const InputDecoration(
                  labelText: 'Year of Construction *',
                ),
                items: controller.yearList.map((year) {
                  return DropdownMenuItem(
                    value: year,
                    child: Text(year.toString()),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectedYear.value = value;
                  }
                },
              )),
              const SizedBox(height: 16),

              // Boolean Questions
              Obx(() => CheckboxListTile(
                title: const Text('Is the customer the building owner?'),
                value: controller.isBuildingOwner.value,
                onChanged: (value) {
                  controller.isBuildingOwner.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),

              Obx(() => CheckboxListTile(
                title: const Text('Interested in tenant electricity?'),
                value: controller.interestedInTenantElectricity.value,
                onChanged: (value) {
                  controller.interestedInTenantElectricity.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),
              const SizedBox(height: 16),

              // Site Visit Date
              InkWell(
                onTap: controller.selectSiteVisitDate,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'Date of Site Visit',
                    suffixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Obx(() => Text(controller.formattedSiteVisitDate)),
                ),
              ),
              const SizedBox(height: 16),

              // Comments
              TextFormField(
                controller: controller.commentsController,
                decoration: const InputDecoration(
                  labelText: 'Comments',
                  hintText: 'Customer/object notes',
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 32),

              // Continue Button
              SizedBox(
                width: double.infinity,
                child: Obx(() => ElevatedButton(
                  onPressed: controller.isLoading.value 
                      ? null 
                      : controller.saveAndContinue,
                  child: controller.isLoading.value
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Continue to Roof Data'),
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
