import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/additional_comments_controller.dart';

class AdditionalCommentsView extends GetView<AdditionalCommentsController> {
  const AdditionalCommentsView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Additional Comments'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.comment,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'Additional Comments',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This screen will contain:\n'
              '• Comments field for other notes\n'
              '• Remarks and open questions\n'
              '• Special requests\n'
              '• Internal communication notes',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: controller.continueToNext,
          child: const Text('Continue to Confirmation'),
        ),
      ),
    );
  }
}
