import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import '../../../data/models/project_model.dart';
import '../../../data/services/storage_service.dart';
import '../../../data/services/localization_service.dart';
import '../../../routes/app_pages.dart';

class PhotoUploadController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();
  final LocalizationService _strings = Get.find<LocalizationService>();

  // Observable variables
  final isLoading = false.obs;
  final photos = <ProjectPhoto>[].obs;
  final documents = <ProjectDocument>[].obs;

  // Project data
  Project? currentProject;
  final ImagePicker _picker = ImagePicker();

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  void _initializeData() {
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      currentProject = arguments['project'];
      if (currentProject?.photos != null) {
        photos.value = currentProject!.photos!;
      }
      if (currentProject?.documents != null) {
        documents.value = currentProject!.documents!;
      }
    }
  }

  Future<void> takePhoto(PhotoCategory category) async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photo != null) {
        await _showPhotoCommentDialog(photo.path, category);
      }
    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to take photo: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> selectPhotosFromGallery(PhotoCategory category) async {
    try {
      final List<XFile> selectedPhotos = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (selectedPhotos.isNotEmpty) {
        for (final photo in selectedPhotos) {
          await _showPhotoCommentDialog(photo.path, category);
        }
      }
    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to select photos: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _showPhotoCommentDialog(String photoPath, PhotoCategory category) async {
    final commentController = TextEditingController();

    final result = await Get.dialog<String>(
      AlertDialog(
        title: Text(_strings.getString('photoComment')),
        content: TextField(
          controller: commentController,
          decoration: InputDecoration(
            hintText: _strings.getString('enterPhotoComment'),
            border: const OutlineInputBorder(),
          ),
          maxLines: 3,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(_strings.getString('cancel')),
          ),
          TextButton(
            onPressed: () => Get.back(result: commentController.text),
            child: Text(_strings.getString('save')),
          ),
        ],
      ),
    );

    if (result != null) {
      final projectPhoto = ProjectPhoto(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        path: photoPath,
        category: category,
        comment: result.isEmpty ? null : result,
      );

      photos.add(projectPhoto);

      Get.snackbar(
        _strings.getString('success'),
        'Photo added successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> addDocument() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        for (final file in result.files) {
          if (file.path != null) {
            await _showDocumentCommentDialog(file.path!, file.name);
          }
        }
      }
    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to select documents: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> _showDocumentCommentDialog(String documentPath, String fileName) async {
    final commentController = TextEditingController();
    DocumentType selectedType = DocumentType.other;

    final result = await Get.dialog<Map<String, dynamic>>(
      AlertDialog(
        title: Text(_strings.getString('documentComment')),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<DocumentType>(
              value: selectedType,
              decoration: const InputDecoration(
                labelText: 'Document Type',
                border: OutlineInputBorder(),
              ),
              items: DocumentType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(_getDocumentTypeDisplayName(type)),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  selectedType = value;
                }
              },
            ),
            const SizedBox(height: 16),
            TextField(
              controller: commentController,
              decoration: InputDecoration(
                hintText: _strings.getString('enterDocumentComment'),
                border: const OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(_strings.getString('cancel')),
          ),
          TextButton(
            onPressed: () => Get.back(result: {
              'comment': commentController.text,
              'type': selectedType,
            }),
            child: Text(_strings.getString('save')),
          ),
        ],
      ),
    );

    if (result != null) {
      final projectDocument = ProjectDocument(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        path: documentPath,
        name: fileName,
        type: result['type'],
        comment: result['comment'].isEmpty ? null : result['comment'],
      );

      documents.add(projectDocument);

      Get.snackbar(
        _strings.getString('success'),
        'Document added successfully',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  void removePhoto(int index) {
    photos.removeAt(index);
  }

  void removeDocument(int index) {
    documents.removeAt(index);
  }

  List<ProjectPhoto> getPhotosByCategory(PhotoCategory category) {
    return photos.where((photo) => photo.category == category).toList();
  }

  Future<void> saveAndContinue() async {
    isLoading.value = true;

    try {
      currentProject!.photos = photos.isNotEmpty ? photos.toList() : null;
      currentProject!.documents = documents.isNotEmpty ? documents.toList() : null;
      currentProject!.lastChange = DateTime.now();

      await _storageService.saveProject(currentProject!);

      Get.toNamed(Routes.ADDITIONAL_COMMENTS, arguments: {
        'project': currentProject,
      });

    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to save photos and documents: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> saveDraft() async {
    final draftData = {
      'photos': photos.map((photo) => photo.toJson()).toList(),
      'documents': documents.map((doc) => doc.toJson()).toList(),
    };

    await _storageService.saveDraft('photo_upload', draftData);

    Get.snackbar(
      _strings.getString('success'),
      _strings.getString('progressSaved'),
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  String getPhotoCategoryDisplayName(PhotoCategory category) {
    switch (category) {
      case PhotoCategory.roof:
        return _strings.getString('roof');
      case PhotoCategory.meter:
        return _strings.getString('meter');
      case PhotoCategory.controlCabinet:
        return _strings.getString('controlCabinet');
      case PhotoCategory.obstacles:
        return _strings.getString('obstacles');
      case PhotoCategory.surroundings:
        return _strings.getString('surroundings');
    }
  }

  String _getDocumentTypeDisplayName(DocumentType type) {
    switch (type) {
      case DocumentType.electricityBill:
        return _strings.getString('electricityBill');
      case DocumentType.permit:
        return _strings.getString('permit');
      case DocumentType.buildingPlan:
        return _strings.getString('buildingPlan');
      case DocumentType.other:
        return _strings.getString('other');
    }
  }
}
