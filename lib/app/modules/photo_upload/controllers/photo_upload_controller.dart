import 'package:get/get.dart';
import '../../../data/models/project_model.dart';
import '../../../routes/app_pages.dart';

class PhotoUploadController extends GetxController {
  Project? currentProject;

  @override
  void onInit() {
    super.onInit();
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      currentProject = arguments['project'];
    }
  }

  void continueToNext() {
    Get.toNamed(Routes.ADDITIONAL_COMMENTS, arguments: {
      'project': currentProject,
    });
  }
}
