import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/project_model.dart';
import '../../../data/services/localization_service.dart';
import '../controllers/photo_upload_controller.dart';

class PhotoUploadView extends GetView<PhotoUploadController> {
  const PhotoUploadView({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = Get.find<LocalizationService>();

    return Scaffold(
      appBar: AppBar(
        title: Text(strings.getString('photoDocumentUpload')),
        actions: [
          TextButton(
            onPressed: controller.saveDraft,
            child: Text(
              strings.getString('saveDraft'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Photo Upload Checklist
            Text(
              strings.getString('photoUploadChecklist'),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Photo Categories
            ...PhotoCategory.values.map((category) =>
              _buildPhotoCategorySection(category, strings)),

            const SizedBox(height: 24),

            // Document Upload Section
            Text(
              strings.getString('documentUpload'),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              strings.getString('electricityBillPermitsPlans'),
              style: TextStyle(
                color: Colors.grey.shade600,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),

            // Document Upload Button
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: controller.addDocument,
                icon: const Icon(Icons.upload_file),
                label: Text(strings.getString('addDocument')),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Documents List
            Obx(() => controller.documents.isEmpty
                ? Container(
                    height: 120,
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.description,
                            size: 48,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            strings.getString('noDocumentsAdded'),
                            style: TextStyle(color: Colors.grey.shade600),
                          ),
                          Text(
                            strings.getString('tapToAddDocuments'),
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : Column(
                    children: controller.documents.asMap().entries.map((entry) {
                      final index = entry.key;
                      final document = entry.value;
                      return _buildDocumentCard(document, index, strings);
                    }).toList(),
                  )),
            const SizedBox(height: 32),

            // Continue Button
            SizedBox(
              width: double.infinity,
              child: Obx(() => ElevatedButton(
                onPressed: controller.isLoading.value
                    ? null
                    : controller.saveAndContinue,
                child: controller.isLoading.value
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(strings.getString('continueToAdditionalComments')),
              )),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoCategorySection(PhotoCategory category, LocalizationService strings) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category Header
        Row(
          children: [
            Expanded(
              child: Text(
                controller.getPhotoCategoryDisplayName(category),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // Photo Action Buttons
            IconButton(
              onPressed: () => controller.takePhoto(category),
              icon: const Icon(Icons.camera_alt),
              tooltip: strings.getString('takePhoto'),
            ),
            IconButton(
              onPressed: () => controller.selectPhotosFromGallery(category),
              icon: const Icon(Icons.photo_library),
              tooltip: strings.getString('selectFromGallery'),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Photos Grid for this category
        Obx(() {
          final categoryPhotos = controller.getPhotosByCategory(category);

          if (categoryPhotos.isEmpty) {
            return Container(
              height: 80,
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'No ${controller.getPhotoCategoryDisplayName(category).toLowerCase()} photos',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 12,
                  ),
                ),
              ),
            );
          }

          return Container(
            margin: const EdgeInsets.only(bottom: 16),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
                childAspectRatio: 1,
              ),
              itemCount: categoryPhotos.length,
              itemBuilder: (context, index) {
                final photo = categoryPhotos[index];
                final globalIndex = controller.photos.indexOf(photo);

                return Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.file(
                        File(photo.path),
                        fit: BoxFit.cover,
                        width: double.infinity,
                        height: double.infinity,
                      ),
                    ),
                    if (photo.comment != null && photo.comment!.isNotEmpty)
                      Positioned(
                        bottom: 4,
                        left: 4,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.7),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: const Icon(
                            Icons.comment,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () => controller.removePhoto(globalIndex),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            color: Colors.white,
                            size: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          );
        }),
      ],
    );
  }

  Widget _buildDocumentCard(ProjectDocument document, int index, LocalizationService strings) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const Icon(Icons.description, color: Colors.orange),
        title: Text(
          document.name,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_getDocumentTypeDisplayName(document.type, strings)),
            if (document.comment != null && document.comment!.isNotEmpty)
              Text(
                document.comment!,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: IconButton(
          onPressed: () => controller.removeDocument(index),
          icon: const Icon(Icons.delete, color: Colors.red),
        ),
        isThreeLine: document.comment != null && document.comment!.isNotEmpty,
      ),
    );
  }

  String _getDocumentTypeDisplayName(DocumentType type, LocalizationService strings) {
    switch (type) {
      case DocumentType.electricityBill:
        return strings.getString('electricityBill');
      case DocumentType.permit:
        return strings.getString('permit');
      case DocumentType.buildingPlan:
        return strings.getString('buildingPlan');
      case DocumentType.other:
        return strings.getString('other');
    }
  }
}
