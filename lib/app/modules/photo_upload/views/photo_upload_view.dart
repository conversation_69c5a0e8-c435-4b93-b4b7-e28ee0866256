import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/photo_upload_controller.dart';

class PhotoUploadView extends GetView<PhotoUploadController> {
  const PhotoUploadView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Photo & Document Upload'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.photo_camera,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'Photo & Document Upload',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This screen will contain:\n'
              '• Photo upload checklist\n'
              '• Roof, meter, control cabinet photos\n'
              '• Obstacles and surroundings\n'
              '• Document upload (PDF/IMG)\n'
              '• Comments for each photo/document',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: controller.continueToNext,
          child: const Text('Continue to Additional Comments'),
        ),
      ),
    );
  }
}
