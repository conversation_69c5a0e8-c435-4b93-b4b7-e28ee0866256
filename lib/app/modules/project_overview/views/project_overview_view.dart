import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/project_model.dart';
import '../../../core/widgets/language_selector.dart';
import '../../../core/utils/extensions.dart';
import '../controllers/project_overview_controller.dart';

class ProjectOverviewView extends GetView<ProjectOverviewController> {
  const ProjectOverviewView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Projects'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            onPressed: controller.logout,
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        final visibleProjects = controller.visibleProjects;

        return Column(
          children: [
            // Show Hidden Projects Toggle
            if (controller.projects.any((p) => p.isHidden))
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Obx(() => Checkbox(
                      value: controller.showHiddenProjects.value,
                      onChanged: (_) => controller.toggleShowHiddenProjects(),
                    )),
                    const Text('Show hidden projects'),
                  ],
                ),
              ),

            // Projects List
            Expanded(
              child: visibleProjects.isEmpty
                  ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.folder_open,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'No projects found',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.grey,
                            ),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Tap the + button to create your first project',
                            style: TextStyle(color: Colors.grey),
                          ),
                        ],
                      ),
                    )
                  : RefreshIndicator(
                      onRefresh: () async => controller.loadProjects(),
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: visibleProjects.length,
                        itemBuilder: (context, index) {
                          final project = visibleProjects[index];
                          return ProjectCard(
                            project: project,
                            controller: controller,
                          );
                        },
                      ),
                    ),
            ),
          ],
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: controller.addNewProject,
        backgroundColor: Colors.orange,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }
}

class ProjectCard extends StatelessWidget {
  final Project project;
  final ProjectOverviewController controller;

  const ProjectCard({
    super.key,
    required this.project,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Project Name and Status
            Row(
              children: [
                Expanded(
                  child: Text(
                    project.projectName,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                if (project.isHidden)
                  const Icon(
                    Icons.visibility_off,
                    color: Colors.grey,
                    size: 20,
                  ),
                if (project.isSubmitted)
                  const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 20,
                  ),
              ],
            ),
            const SizedBox(height: 8),

            // Status Progress Bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  project.status.displayName,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: project.status.progress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    project.status.progress == 1.0 ? Colors.green : Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Last Change
            Text(
              'Last change: ${controller.formatLastChange(project.lastChange)}',
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => controller.editProject(project),
                    icon: const Icon(Icons.edit, size: 16),
                    label: const Text('Edit'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.orange,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                if (!project.isSubmitted)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => controller.submitProject(project),
                      icon: const Icon(Icons.send, size: 16),
                      label: const Text('Submit'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'hide':
                        if (project.isHidden) {
                          controller.unhideProject(project);
                        } else {
                          controller.hideProject(project);
                        }
                        break;
                      case 'delete':
                        controller.deleteProject(project);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'hide',
                      child: Row(
                        children: [
                          Icon(
                            project.isHidden 
                                ? Icons.visibility 
                                : Icons.visibility_off,
                            size: 16,
                          ),
                          const SizedBox(width: 8),
                          Text(project.isHidden ? 'Unhide' : 'Hide'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  child: const Icon(Icons.more_vert),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
