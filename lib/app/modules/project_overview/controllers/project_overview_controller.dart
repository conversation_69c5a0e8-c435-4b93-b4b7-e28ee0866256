import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/project_model.dart';
import '../../../data/services/storage_service.dart';
import '../../../routes/app_pages.dart';

class ProjectOverviewController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();
  
  final projects = <Project>[].obs;
  final showHiddenProjects = false.obs;
  final isLoading = false.obs;

  @override
  void onInit() {
    super.onInit();
    loadProjects();
  }

  void loadProjects() {
    isLoading.value = true;
    try {
      final allProjects = _storageService.getProjects();
      projects.value = allProjects;
    } catch (e) {
      Get.snackbar('Error', 'Failed to load projects: ${e.toString()}');
    } finally {
      isLoading.value = false;
    }
  }

  List<Project> get visibleProjects {
    if (showHiddenProjects.value) {
      return projects;
    }
    return projects.where((project) => !project.isHidden).toList();
  }

  void toggleShowHiddenProjects() {
    showHiddenProjects.value = !showHiddenProjects.value;
  }

  void addNewProject() {
    Get.toNamed(Routes.CUSTOMER_DATA, arguments: {'isNewProject': true});
  }

  void editProject(Project project) {
    Get.toNamed(Routes.CUSTOMER_DATA, arguments: {
      'project': project,
      'isNewProject': false,
    });
  }

  Future<void> submitProject(Project project) async {
    try {
      project.isSubmitted = true;
      project.submissionDate = DateTime.now();
      project.lastChange = DateTime.now();
      
      await _storageService.saveProject(project);
      loadProjects();
      
      Get.snackbar(
        'Success',
        'Project "${project.projectName}" submitted successfully!',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to submit project: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> hideProject(Project project) async {
    try {
      project.isHidden = true;
      project.lastChange = DateTime.now();
      
      await _storageService.saveProject(project);
      loadProjects();
      
      Get.snackbar(
        'Success',
        'Project "${project.projectName}" hidden',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to hide project: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> unhideProject(Project project) async {
    try {
      project.isHidden = false;
      project.lastChange = DateTime.now();
      
      await _storageService.saveProject(project);
      loadProjects();
      
      Get.snackbar(
        'Success',
        'Project "${project.projectName}" unhidden',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to unhide project: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> deleteProject(Project project) async {
    final confirmed = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('Delete Project'),
        content: Text('Are you sure you want to delete "${project.projectName}"?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _storageService.deleteProject(project.id!);
        loadProjects();
        
        Get.snackbar(
          'Success',
          'Project "${project.projectName}" deleted',
          snackPosition: SnackPosition.BOTTOM,
        );
      } catch (e) {
        Get.snackbar(
          'Error',
          'Failed to delete project: ${e.toString()}',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }

  void logout() {
    Get.dialog(
      AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _storageService.clearUserCredentials();
              Get.offAllNamed(Routes.LOGIN);
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }

  String formatLastChange(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays} day${difference.inDays == 1 ? '' : 's'} ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hour${difference.inHours == 1 ? '' : 's'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minute${difference.inMinutes == 1 ? '' : 's'} ago';
    } else {
      return 'Just now';
    }
  }
}
