import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import '../../../data/models/project_model.dart';
import '../../../data/services/storage_service.dart';
import '../../../data/services/localization_service.dart';
import '../../../routes/app_pages.dart';

class RoofDataController extends GetxController {
  final StorageService _storageService = Get.find<StorageService>();
  final LocalizationService _strings = Get.find<LocalizationService>();

  final formKey = GlobalKey<FormState>();

  // Form Controllers
  final dimensionsController = TextEditingController();
  final slopeController = TextEditingController();
  final remarksController = TextEditingController();

  // Observable variables
  final selectedOrientation = RoofOrientation.south.obs;
  final selectedRoofType = RoofType.gabled.obs;
  final selectedTileType = RoofTileType.clay.obs;
  final hasWindows = false.obs;
  final hasDormers = false.obs;
  final hasChimneyVent = false.obs;
  final isLoading = false.obs;
  final roofPhotos = <String>[].obs;

  // Project data
  Project? currentProject;
  final ImagePicker _picker = ImagePicker();

  @override
  void onInit() {
    super.onInit();
    _initializeData();
  }

  @override
  void onClose() {
    _disposeControllers();
    super.onClose();
  }

  void _initializeData() {
    final arguments = Get.arguments as Map<String, dynamic>?;
    if (arguments != null) {
      currentProject = arguments['project'];
      if (currentProject?.roofData != null) {
        _loadRoofData();
      }
    }
  }

  void _loadRoofData() {
    final roofData = currentProject!.roofData!;

    dimensionsController.text = roofData.dimensions;
    slopeController.text = roofData.slope;
    remarksController.text = roofData.remarks ?? '';

    selectedOrientation.value = roofData.orientation;
    selectedRoofType.value = roofData.roofType;
    selectedTileType.value = roofData.roofTileType;
    hasWindows.value = roofData.hasWindows;
    hasDormers.value = roofData.hasDormers;
    hasChimneyVent.value = roofData.hasChimneyVent;

    if (roofData.photosPaths != null) {
      roofPhotos.value = roofData.photosPaths!;
    }
  }

  void _disposeControllers() {
    dimensionsController.dispose();
    slopeController.dispose();
    remarksController.dispose();
  }

  Future<void> takePhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photo != null) {
        roofPhotos.add(photo.path);
        Get.snackbar(
          _strings.getString('success'),
          'Photo added successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to take photo: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  Future<void> selectFromGallery() async {
    try {
      final List<XFile> photos = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photos.isNotEmpty) {
        for (final photo in photos) {
          roofPhotos.add(photo.path);
        }
        Get.snackbar(
          _strings.getString('success'),
          '${photos.length} photo(s) added successfully',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to select photos: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  void removePhoto(int index) {
    roofPhotos.removeAt(index);
  }

  Future<void> saveAndContinue() async {
    if (!formKey.currentState!.validate()) return;

    isLoading.value = true;

    try {
      final roofData = RoofData(
        orientation: selectedOrientation.value,
        dimensions: dimensionsController.text.trim(),
        roofType: selectedRoofType.value,
        slope: slopeController.text.trim(),
        roofTileType: selectedTileType.value,
        hasWindows: hasWindows.value,
        hasDormers: hasDormers.value,
        hasChimneyVent: hasChimneyVent.value,
        photosPaths: roofPhotos.isNotEmpty ? roofPhotos.toList() : null,
        remarks: remarksController.text.trim().isEmpty
            ? null
            : remarksController.text.trim(),
      );

      currentProject!.roofData = roofData;
      currentProject!.lastChange = DateTime.now();

      await _storageService.saveProject(currentProject!);

      Get.toNamed(Routes.ELECTRICAL_DATA, arguments: {
        'project': currentProject,
      });

    } catch (e) {
      Get.snackbar(
        _strings.getString('error'),
        'Failed to save roof data: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> saveDraft() async {
    final draftData = {
      'orientation': selectedOrientation.value.index,
      'dimensions': dimensionsController.text,
      'roofType': selectedRoofType.value.index,
      'slope': slopeController.text,
      'tileType': selectedTileType.value.index,
      'hasWindows': hasWindows.value,
      'hasDormers': hasDormers.value,
      'hasChimneyVent': hasChimneyVent.value,
      'photos': roofPhotos.toList(),
      'remarks': remarksController.text,
    };

    await _storageService.saveDraft('roof_data', draftData);

    Get.snackbar(
      _strings.getString('success'),
      _strings.getString('progressSaved'),
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // Validation methods
  String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }

  String getOrientationDisplayName(RoofOrientation orientation) {
    switch (orientation) {
      case RoofOrientation.north:
        return _strings.getString('north');
      case RoofOrientation.northeast:
        return _strings.getString('northeast');
      case RoofOrientation.east:
        return _strings.getString('east');
      case RoofOrientation.southeast:
        return _strings.getString('southeast');
      case RoofOrientation.south:
        return _strings.getString('south');
      case RoofOrientation.southwest:
        return _strings.getString('southwest');
      case RoofOrientation.west:
        return _strings.getString('west');
      case RoofOrientation.northwest:
        return _strings.getString('northwest');
    }
  }

  String getRoofTypeDisplayName(RoofType type) {
    switch (type) {
      case RoofType.gabled:
        return _strings.getString('gabled');
      case RoofType.hipped:
        return _strings.getString('hipped');
      case RoofType.flat:
        return _strings.getString('flat');
      case RoofType.shed:
        return _strings.getString('shed');
      case RoofType.gambrel:
        return _strings.getString('gambrel');
    }
  }

  String getTileTypeDisplayName(RoofTileType type) {
    switch (type) {
      case RoofTileType.clay:
        return _strings.getString('clay');
      case RoofTileType.concrete:
        return _strings.getString('concrete');
      case RoofTileType.slate:
        return _strings.getString('slate');
      case RoofTileType.metal:
        return _strings.getString('metal');
      case RoofTileType.asphalt:
        return _strings.getString('asphalt');
    }
  }
}
