import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../data/models/project_model.dart';
import '../../../data/services/localization_service.dart';
import '../controllers/roof_data_controller.dart';

class RoofDataView extends GetView<RoofDataController> {
  const RoofDataView({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = Get.find<LocalizationService>();

    return Scaffold(
      appBar: AppBar(
        title: Text(strings.getString('roofDataSiteDetails')),
        actions: [
          TextButton(
            onPressed: controller.saveDraft,
            child: Text(
              strings.getString('saveDraft'),
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: controller.formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Roof Orientation
              Text(
                strings.getString('roofOrientation'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Obx(() => DropdownButtonFormField<RoofOrientation>(
                value: controller.selectedOrientation.value,
                decoration: InputDecoration(
                  labelText: strings.getString('roofOrientation'),
                  border: const OutlineInputBorder(),
                ),
                items: RoofOrientation.values.map((orientation) {
                  return DropdownMenuItem(
                    value: orientation,
                    child: Text(controller.getOrientationDisplayName(orientation)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectedOrientation.value = value;
                  }
                },
              )),
              const SizedBox(height: 16),

              // Dimensions
              TextFormField(
                controller: controller.dimensionsController,
                decoration: InputDecoration(
                  labelText: '${strings.getString('dimensions')} *',
                  hintText: strings.getString('enterDimensions'),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) => controller.validateRequired(value, strings.getString('dimensions')),
              ),
              const SizedBox(height: 16),

              // Roof Type
              Obx(() => DropdownButtonFormField<RoofType>(
                value: controller.selectedRoofType.value,
                decoration: InputDecoration(
                  labelText: strings.getString('roofType'),
                  border: const OutlineInputBorder(),
                ),
                items: RoofType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(controller.getRoofTypeDisplayName(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectedRoofType.value = value;
                  }
                },
              )),
              const SizedBox(height: 16),

              // Slope
              TextFormField(
                controller: controller.slopeController,
                decoration: InputDecoration(
                  labelText: '${strings.getString('slope')} *',
                  hintText: strings.getString('enterSlope'),
                  border: const OutlineInputBorder(),
                ),
                validator: (value) => controller.validateRequired(value, strings.getString('slope')),
              ),
              const SizedBox(height: 16),

              // Roof Tile Type
              Obx(() => DropdownButtonFormField<RoofTileType>(
                value: controller.selectedTileType.value,
                decoration: InputDecoration(
                  labelText: strings.getString('roofTileType'),
                  border: const OutlineInputBorder(),
                ),
                items: RoofTileType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(controller.getTileTypeDisplayName(type)),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectedTileType.value = value;
                  }
                },
              )),
              const SizedBox(height: 24),

              // Roof Details Section
              Text(
                strings.getString('roofDetails'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              // Windows, Dormers, Chimney checkboxes
              Obx(() => CheckboxListTile(
                title: Text(strings.getString('hasWindows')),
                value: controller.hasWindows.value,
                onChanged: (value) {
                  controller.hasWindows.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),

              Obx(() => CheckboxListTile(
                title: Text(strings.getString('hasDormers')),
                value: controller.hasDormers.value,
                onChanged: (value) {
                  controller.hasDormers.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),

              Obx(() => CheckboxListTile(
                title: Text(strings.getString('hasChimneyVent')),
                value: controller.hasChimneyVent.value,
                onChanged: (value) {
                  controller.hasChimneyVent.value = value ?? false;
                },
                controlAffinity: ListTileControlAffinity.leading,
              )),
              const SizedBox(height: 24),

              // Photos Section
              Text(
                strings.getString('photos'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),

              // Photo buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: controller.takePhoto,
                      icon: const Icon(Icons.camera_alt),
                      label: Text(strings.getString('takePhoto')),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: controller.selectFromGallery,
                      icon: const Icon(Icons.photo_library),
                      label: Text(strings.getString('selectFromGallery')),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Photos Grid
              Obx(() => controller.roofPhotos.isEmpty
                  ? Container(
                      height: 120,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.photo_camera,
                              size: 48,
                              color: Colors.grey.shade400,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              strings.getString('noPhotosAdded'),
                              style: TextStyle(color: Colors.grey.shade600),
                            ),
                            Text(
                              strings.getString('tapToAddPhotos'),
                              style: TextStyle(
                                color: Colors.grey.shade500,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  : GridView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        crossAxisSpacing: 8,
                        mainAxisSpacing: 8,
                      ),
                      itemCount: controller.roofPhotos.length,
                      itemBuilder: (context, index) {
                        return Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: Image.file(
                                File(controller.roofPhotos[index]),
                                fit: BoxFit.cover,
                                width: double.infinity,
                                height: double.infinity,
                              ),
                            ),
                            Positioned(
                              top: 4,
                              right: 4,
                              child: GestureDetector(
                                onTap: () => controller.removePhoto(index),
                                child: Container(
                                  padding: const EdgeInsets.all(4),
                                  decoration: const BoxDecoration(
                                    color: Colors.red,
                                    shape: BoxShape.circle,
                                  ),
                                  child: const Icon(
                                    Icons.close,
                                    color: Colors.white,
                                    size: 16,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    )),
              const SizedBox(height: 24),

              // Remarks
              TextFormField(
                controller: controller.remarksController,
                decoration: InputDecoration(
                  labelText: strings.getString('remarks'),
                  hintText: strings.getString('enterRemarks'),
                  border: const OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 32),

              // Continue Button
              SizedBox(
                width: double.infinity,
                child: Obx(() => ElevatedButton(
                  onPressed: controller.isLoading.value
                      ? null
                      : controller.saveAndContinue,
                  child: controller.isLoading.value
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(strings.getString('continueToElectricalData')),
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
