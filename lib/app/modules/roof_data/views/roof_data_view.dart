import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/roof_data_controller.dart';

class RoofDataView extends GetView<RoofDataController> {
  const RoofDataView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Roof Data & Site Details'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.roofing,
              size: 64,
              color: Colors.orange,
            ),
            SizedBox(height: 16),
            Text(
              'Roof Data & Site Details',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'This screen will contain:\n'
              '• Roof orientation\n'
              '• Dimensions\n'
              '• Roof type and slope\n'
              '• Roof tile type\n'
              '• Windows, dormers, chimney details\n'
              '• Photo capture\n'
              '• Roof sketch tool\n'
              '• Remarks',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: ElevatedButton(
          onPressed: controller.continueToNext,
          child: const Text('Continue to Electrical Data'),
        ),
      ),
    );
  }
}
