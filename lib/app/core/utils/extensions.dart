import 'package:get/get.dart';
import '../../data/services/localization_service.dart';

extension AppStringExtension on String {
  /// Get localized string using the key
  String get localized {
    final localizationService = Get.find<LocalizationService>();
    return localizationService.getString(this);
  }
}

extension LocalizationHelper on GetxController {
  /// Quick access to localization service
  LocalizationService get localization => Get.find<LocalizationService>();
  
  /// Quick translation method
  String tr(String key) => localization.getString(key);
}

extension LocalizationViewHelper on GetView {
  /// Quick access to localization service for views
  LocalizationService get localization => Get.find<LocalizationService>();
  
  /// Quick translation method for views
  String tr(String key) => localization.getString(key);
}
