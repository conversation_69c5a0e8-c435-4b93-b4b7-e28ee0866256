import 'package:get/get.dart';
import '../../data/services/localization_service.dart';

extension AppStringExtension on String {
  /// Get string from AppStrings constants
  String get text {
    final localizationService = Get.find<LocalizationService>();
    return localizationService.getString(this);
  }
}

extension StringHelper on GetxController {
  /// Quick access to localization service
  LocalizationService get strings => Get.find<LocalizationService>();

  /// Quick string method
  String text(String key) => strings.getString(key);
}

extension StringViewHelper on GetView {
  /// Quick access to localization service for views
  LocalizationService get strings => Get.find<LocalizationService>();

  /// Quick string method for views
  String text(String key) => strings.getString(key);
}
