import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/services/localization_service.dart';
import '../../core/widgets/language_selector.dart';

/// Example screen showing how to use the localization system
class LocalizationExampleScreen extends StatelessWidget {
  const LocalizationExampleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final localization = Get.find<LocalizationService>();

    return Scaffold(
      appBar: AppBar(
        title: Text(localization.getString('appName')),
        actions: [
          IconButton(
            icon: const Icon(Icons.language),
            onPressed: () => LanguageSelectorDialog.show(),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Method 1: Using localization service directly
            Text(
              'Method 1: Direct Service Call',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(localization.getString('login')),
            Text(localization.getString('save')),
            Text(localization.getString('cancel')),
            const SizedBox(height: 24),

            // Method 2: Using reactive updates
            Text(
              'Method 2: Reactive Updates',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Obx(() => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Current Language: ${localization.currentLanguage.value}'),
                Text('App Name: ${localization.getString('appName')}'),
                Text('Projects: ${localization.getString('projects')}'),
              ],
            )),
            const SizedBox(height: 24),

            // Method 3: In form fields
            Text(
              'Method 3: Form Fields',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            TextFormField(
              decoration: InputDecoration(
                labelText: localization.getString('username'),
                hintText: 'Enter ${localization.getString('username').toLowerCase()}',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: InputDecoration(
                labelText: localization.getString('email'),
                hintText: 'Enter ${localization.getString('email').toLowerCase()}',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 24),

            // Method 4: In buttons and actions
            Text(
              'Method 4: Buttons and Actions',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Get.snackbar(
                      localization.getString('success'),
                      'This is a ${localization.getString('success').toLowerCase()} message',
                    );
                  },
                  child: Text(localization.getString('save')),
                ),
                const SizedBox(width: 16),
                OutlinedButton(
                  onPressed: () {
                    Get.snackbar(
                      localization.getString('info'),
                      'This is an ${localization.getString('info').toLowerCase()} message',
                    );
                  },
                  child: Text(localization.getString('cancel')),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Language Selector
            const LanguageSelector(),
          ],
        ),
      ),
    );
  }
}

/// Example controller showing localization in business logic
class LocalizationExampleController extends GetxController {
  final LocalizationService _localization = Get.find<LocalizationService>();

  void showSuccessMessage() {
    Get.snackbar(
      _localization.getString('success'),
      _localization.getString('projectSaved'),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  void showErrorMessage() {
    Get.snackbar(
      _localization.getString('error'),
      _localization.getString('failedToSaveProject'),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  String validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return _localization.getString('emailRequired');
    }
    if (!GetUtils.isEmail(value)) {
      return _localization.getString('validEmailRequired');
    }
    return '';
  }

  String validateRequired(String? value, String fieldKey) {
    if (value == null || value.isEmpty) {
      final fieldName = _localization.getString(fieldKey);
      return '$fieldName ${_localization.getString('required').toLowerCase()}';
    }
    return '';
  }
}

/// Example of how to add new strings to the system
class ExampleNewStrings {
  // 1. Add to AppStrings class
  static const String newFeature = 'New Feature';
  static const String newFeatureDescription = 'This is a new feature';
  
  // 2. Add to LocalizationService._getEnglishString()
  // case 'newFeature':
  //   return AppStrings.newFeature;
  // case 'newFeatureDescription':
  //   return AppStrings.newFeatureDescription;
  
  // 3. Add to LocalizationService._getGermanString()
  // case 'newFeature':
  //   return 'Neue Funktion';
  // case 'newFeatureDescription':
  //   return 'Das ist eine neue Funktion';
  
  // 4. Use in your code
  void useNewStrings() {
    final localization = Get.find<LocalizationService>();
    final title = localization.getString('newFeature');
    final description = localization.getString('newFeatureDescription');
    
    Get.dialog(
      AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(localization.getString('ok')),
          ),
        ],
      ),
    );
  }
}
