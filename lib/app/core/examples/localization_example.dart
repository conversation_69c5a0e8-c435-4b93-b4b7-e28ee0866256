import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/services/localization_service.dart';

/// Example screen showing how to use the string management system
class StringManagementExampleScreen extends StatelessWidget {
  const StringManagementExampleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final strings = Get.find<LocalizationService>();

    return Scaffold(
      appBar: AppBar(
        title: Text(strings.getString('appName')),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Method 1: Using string service directly
            Text(
              'Method 1: Direct Service Call',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(strings.getString('login')),
            Text(strings.getString('save')),
            Text(strings.getString('cancel')),
            const SizedBox(height: 24),

            // Method 2: Centralized string management
            Text(
              'Method 2: Centralized Strings',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('App Name: ${strings.getString('appName')}'),
                Text('Projects: ${strings.getString('projects')}'),
                Text('Customer Location: ${strings.getString('customerLocation')}'),
              ],
            ),
            const SizedBox(height: 24),

            // Method 3: In form fields
            Text(
              'Method 3: Form Fields',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            TextFormField(
              decoration: InputDecoration(
                labelText: strings.getString('username'),
                hintText: 'Enter ${strings.getString('username').toLowerCase()}',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: InputDecoration(
                labelText: strings.getString('email'),
                hintText: 'Enter ${strings.getString('email').toLowerCase()}',
                border: const OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 24),

            // Method 4: In buttons and actions
            Text(
              'Method 4: Buttons and Actions',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                ElevatedButton(
                  onPressed: () {
                    Get.snackbar(
                      strings.getString('success'),
                      'This is a ${strings.getString('success').toLowerCase()} message',
                    );
                  },
                  child: Text(strings.getString('save')),
                ),
                const SizedBox(width: 16),
                OutlinedButton(
                  onPressed: () {
                    Get.snackbar(
                      strings.getString('info'),
                      'This is an ${strings.getString('info').toLowerCase()} message',
                    );
                  },
                  child: Text(strings.getString('cancel')),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // String Management Info
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'String Management System',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'All text content is managed centrally through AppStrings constants. '
                    'This ensures consistency and makes it easy to update text throughout the app.',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Example controller showing string management in business logic
class StringManagementExampleController extends GetxController {
  final LocalizationService _strings = Get.find<LocalizationService>();

  void showSuccessMessage() {
    Get.snackbar(
      _strings.getString('success'),
      _strings.getString('projectSaved'),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  void showErrorMessage() {
    Get.snackbar(
      _strings.getString('error'),
      _strings.getString('failedToSaveProject'),
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  String validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return _strings.getString('emailRequired');
    }
    if (!GetUtils.isEmail(value)) {
      return _strings.getString('validEmailRequired');
    }
    return '';
  }

  String validateRequired(String? value, String fieldKey) {
    if (value == null || value.isEmpty) {
      final fieldName = _strings.getString(fieldKey);
      return '$fieldName ${_strings.getString('required').toLowerCase()}';
    }
    return '';
  }
}

/// Example of how to add new strings to the system
class ExampleNewStrings {
  // 1. Add to AppStrings class
  static const String newFeature = 'New Feature';
  static const String newFeatureDescription = 'This is a new feature';
  
  // 2. Add to LocalizationService._getEnglishString()
  // case 'newFeature':
  //   return AppStrings.newFeature;
  // case 'newFeatureDescription':
  //   return AppStrings.newFeatureDescription;

  // 3. Use in your code
  void useNewStrings() {
    final strings = Get.find<LocalizationService>();
    final title = strings.getString('newFeature');
    final description = strings.getString('newFeatureDescription');

    Get.dialog(
      AlertDialog(
        title: Text(title),
        content: Text(description),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text(strings.getString('ok')),
          ),
        ],
      ),
    );
  }
}
