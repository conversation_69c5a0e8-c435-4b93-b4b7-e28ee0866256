import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../data/services/localization_service.dart';

class LanguageSelector extends StatelessWidget {
  final bool showTitle;
  final EdgeInsets? padding;

  const LanguageSelector({
    super.key,
    this.showTitle = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final localizationService = Get.find<LocalizationService>();

    return Padding(
      padding: padding ?? const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (showTitle) ...[
            const Text(
              'Language / Sprache',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
          ],
          Obx(() => DropdownButtonFormField<String>(
            value: localizationService.currentLanguage.value,
            decoration: const InputDecoration(
              labelText: 'Select Language',
              prefixIcon: Icon(Icons.language),
              border: OutlineInputBorder(),
            ),
            items: localizationService.availableLanguages.map((languageCode) {
              return DropdownMenuItem(
                value: languageCode,
                child: Row(
                  children: [
                    _getLanguageFlag(languageCode),
                    const SizedBox(width: 12),
                    Text(localizationService.languageNames[languageCode] ?? languageCode),
                  ],
                ),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                localizationService.changeLanguage(value);
                Get.snackbar(
                  'Language Changed',
                  'Language has been changed to ${localizationService.languageNames[value]}',
                  snackPosition: SnackPosition.BOTTOM,
                );
              }
            },
          )),
        ],
      ),
    );
  }

  Widget _getLanguageFlag(String languageCode) {
    String flag;
    switch (languageCode) {
      case 'en':
        flag = '🇺🇸';
        break;
      case 'de':
        flag = '🇩🇪';
        break;
      case 'fr':
        flag = '🇫🇷';
        break;
      case 'es':
        flag = '🇪🇸';
        break;
      default:
        flag = '🌐';
    }
    
    return Text(
      flag,
      style: const TextStyle(fontSize: 20),
    );
  }
}

class LanguageSelectorDialog extends StatelessWidget {
  const LanguageSelectorDialog({super.key});

  static void show() {
    Get.dialog(
      AlertDialog(
        title: const Text('Select Language'),
        content: const LanguageSelector(
          showTitle: false,
          padding: EdgeInsets.zero,
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return const SizedBox.shrink();
  }
}
