import 'dart:io';
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'modern_buttons.dart';

class PhotoGrid extends StatelessWidget {
  final List<String> photoPaths;
  final Function(int)? onPhotoTap;
  final Function(int)? onPhotoDelete;
  final int crossAxisCount;
  final double aspectRatio;

  const PhotoGrid({
    super.key,
    required this.photoPaths,
    this.onPhotoTap,
    this.onPhotoDelete,
    this.crossAxisCount = 3,
    this.aspectRatio = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    if (photoPaths.isEmpty) {
      return const EmptyPhotoState();
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: aspectRatio,
      ),
      itemCount: photoPaths.length,
      itemBuilder: (context, index) {
        return PhotoTile(
          photoPath: photoPaths[index],
          onTap: () => onPhotoTap?.call(index),
          onDelete: () => onPhotoDelete?.call(index),
        );
      },
    );
  }
}

class PhotoTile extends StatelessWidget {
  final String photoPath;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final bool showDeleteButton;

  const PhotoTile({
    super.key,
    required this.photoPath,
    this.onTap,
    this.onDelete,
    this.showDeleteButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.cardShadow,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // Photo
            Image.file(
              File(photoPath),
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Container(
                  color: Colors.grey.shade200,
                  child: const Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                    size: 32,
                  ),
                );
              },
            ),
            
            // Overlay gradient
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    Colors.black.withOpacity(0.3),
                  ],
                ),
              ),
            ),

            // Tap area
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onTap,
                child: Container(),
              ),
            ),

            // Delete button
            if (showDeleteButton && onDelete != null)
              Positioned(
                top: 8,
                right: 8,
                child: GestureDetector(
                  onTap: onDelete,
                  child: Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.9),
                      shape: BoxShape.circle,
                      boxShadow: AppTheme.cardShadow,
                    ),
                    child: const Icon(
                      Icons.close,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class EmptyPhotoState extends StatelessWidget {
  final String? message;
  final String? subtitle;
  final IconData? icon;

  const EmptyPhotoState({
    super.key,
    this.message,
    this.subtitle,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade300,
          style: BorderStyle.solid,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon ?? Icons.photo_camera,
              size: 48,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 12),
            Text(
              message ?? 'No photos added yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 4),
              Text(
                subtitle!,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey.shade500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class PhotoActionButtons extends StatelessWidget {
  final VoidCallback? onTakePhoto;
  final VoidCallback? onSelectFromGallery;
  final String? takePhotoText;
  final String? selectFromGalleryText;

  const PhotoActionButtons({
    super.key,
    this.onTakePhoto,
    this.onSelectFromGallery,
    this.takePhotoText,
    this.selectFromGalleryText,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ModernOutlinedButton(
            text: takePhotoText ?? 'Take Photo',
            icon: Icons.camera_alt,
            onPressed: onTakePhoto,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ModernOutlinedButton(
            text: selectFromGalleryText ?? 'Gallery',
            icon: Icons.photo_library,
            onPressed: onSelectFromGallery,
          ),
        ),
      ],
    );
  }
}

class PhotoCategorySection extends StatelessWidget {
  final String title;
  final List<String> photos;
  final VoidCallback? onTakePhoto;
  final VoidCallback? onSelectFromGallery;
  final Function(int)? onPhotoDelete;
  final Function(int)? onPhotoTap;

  const PhotoCategorySection({
    super.key,
    required this.title,
    required this.photos,
    this.onTakePhoto,
    this.onSelectFromGallery,
    this.onPhotoDelete,
    this.onPhotoTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Category Header
        Row(
          children: [
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            IconButtonModern(
              icon: Icons.camera_alt,
              onPressed: onTakePhoto,
              size: 40,
              tooltip: 'Take Photo',
            ),
            const SizedBox(width: 8),
            IconButtonModern(
              icon: Icons.photo_library,
              onPressed: onSelectFromGallery,
              size: 40,
              tooltip: 'Select from Gallery',
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Photos Grid
        photos.isEmpty
            ? EmptyPhotoState(
                message: 'No $title photos',
                subtitle: 'Tap the camera or gallery button to add photos',
              )
            : PhotoGrid(
                photoPaths: photos,
                onPhotoTap: onPhotoTap,
                onPhotoDelete: onPhotoDelete,
              ),
      ],
    );
  }
}

class PhotoViewer extends StatelessWidget {
  final String photoPath;
  final String? title;
  final VoidCallback? onClose;

  const PhotoViewer({
    super.key,
    required this.photoPath,
    this.title,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          onPressed: onClose ?? () => Navigator.of(context).pop(),
          icon: const Icon(Icons.close, color: Colors.white),
        ),
        title: title != null
            ? Text(
                title!,
                style: const TextStyle(color: Colors.white),
              )
            : null,
      ),
      body: Center(
        child: InteractiveViewer(
          child: Image.file(
            File(photoPath),
            fit: BoxFit.contain,
            errorBuilder: (context, error, stackTrace) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.broken_image,
                      color: Colors.white,
                      size: 64,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Failed to load image',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
