class AppStrings {
  // App Info
  static const String appName = 'Sunalyze';
  static const String appSubtitle = 'Solar Panel Installation Management';

  // Common Words
  static const String login = 'Login';
  static const String logout = 'Logout';
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String submit = 'Submit';
  static const String continue_ = 'Continue';
  static const String back = 'Back';
  static const String next = 'Next';
  static const String previous = 'Previous';
  static const String ok = 'OK';
  static const String yes = 'Yes';
  static const String no = 'No';
  static const String required = 'Required';
  static const String optional = 'Optional';
  static const String loading = 'Loading...';
  static const String success = 'Success';
  static const String error = 'Error';
  static const String warning = 'Warning';
  static const String info = 'Info';

  // Login Screen
  static const String username = 'Username';
  static const String partnerCode = 'Partner Code';
  static const String usernamePartnerCode = 'Username / Partner Code';
  static const String password = 'Password';
  static const String rememberMe = 'Remember Me';
  static const String forgotPassword = 'Forgot Password?';
  static const String contactSupport = 'Contact Support';
  static const String loginSuccessful = 'Login successful!';
  static const String loginFailed = 'Login failed';
  static const String enterUsernamePassword = 'Please enter both username and password';
  static const String usernameMinLength = 'Username must be at least 3 characters';
  static const String passwordMinLength = 'Password must be at least 6 characters';
  static const String enterUsername = 'Please enter your username';
  static const String enterPassword = 'Please enter your password';

  // Forgot Password Dialog
  static const String forgotPasswordTitle = 'Forgot Password';
  static const String forgotPasswordMessage = 'Please contact your system administrator to reset your password.';

  // Contact Support Dialog
  static const String contactSupportTitle = 'Contact Support';
  static const String supportContactInfo = 'Support Contact Information:';
  static const String supportEmail = 'Email: <EMAIL>';
  static const String supportPhone = 'Phone: +****************';
  static const String supportHours = 'Hours: Mon-Fri 9AM-5PM';

  // Project Overview Screen
  static const String projects = 'Projects';
  static const String showHiddenProjects = 'Show hidden projects';
  static const String noProjectsFound = 'No projects found';
  static const String createFirstProject = 'Tap the + button to create your first project';
  static const String addProject = 'Add Project';
  static const String lastChange = 'Last change';
  static const String hide = 'Hide';
  static const String unhide = 'Unhide';
  static const String projectSubmitted = 'Project submitted successfully!';
  static const String projectHidden = 'Project hidden';
  static const String projectUnhidden = 'Project unhidden';
  static const String projectDeleted = 'Project deleted';
  static const String deleteProject = 'Delete Project';
  static const String deleteProjectConfirm = 'Are you sure you want to delete';
  static const String logoutConfirm = 'Are you sure you want to logout?';

  // Time Formats
  static const String justNow = 'Just now';
  static const String minuteAgo = 'minute ago';
  static const String minutesAgo = 'minutes ago';
  static const String hourAgo = 'hour ago';
  static const String hoursAgo = 'hours ago';
  static const String dayAgo = 'day ago';
  static const String daysAgo = 'days ago';

  // Project Status
  static const String siteVisit = 'Site Visit';
  static const String proposal = 'Proposal';
  static const String orderReceived = 'Order Received';
  static const String installation = 'Installation';
  static const String completed = 'Completed';

  // Customer Data Screen
  static const String customerLocation = 'Customer & Location';
  static const String customerInformation = 'Customer Information';
  static const String buildingInformation = 'Building Information';
  static const String projectName = 'Project Name';
  static const String firstName = 'First Name';
  static const String lastName = 'Last Name';
  static const String street = 'Street';
  static const String houseNumber = 'House No.';
  static const String zipCode = 'Zip Code';
  static const String city = 'City';
  static const String phone = 'Phone';
  static const String mobile = 'Mobile';
  static const String email = 'Email';
  static const String preferredContactHours = 'Preferred Contact Hours';
  static const String company = 'Company';
  static const String buildingType = 'Building Type';
  static const String yearOfConstruction = 'Year of Construction';
  static const String isBuildingOwner = 'Is the customer the building owner?';
  static const String interestedTenantElectricity = 'Interested in tenant electricity?';
  static const String siteVisitDate = 'Date of Site Visit';
  static const String comments = 'Comments';
  static const String customerObjectNotes = 'Customer/object notes';
  static const String saveDraft = 'Save Draft';
  static const String draftSaved = 'Draft Saved';
  static const String progressSaved = 'Your progress has been saved';
  static const String continueToRoofData = 'Continue to Roof Data';

  // Building Types
  static const String singleFamilyHouse = 'Single Family House';
  static const String multiFamilyHouse = 'Multi Family House';
  static const String commercialBuilding = 'Commercial Building';
  static const String industrialBuilding = 'Industrial Building';

  // Form Validation Messages
  static const String projectNameRequired = 'Project name is required';
  static const String firstNameRequired = 'First name is required';
  static const String lastNameRequired = 'Last name is required';
  static const String streetRequired = 'Street is required';
  static const String houseNumberRequired = 'House number is required';
  static const String zipCodeRequired = 'Zip code is required';
  static const String cityRequired = 'City is required';
  static const String phoneRequired = 'Phone number is required';
  static const String emailRequired = 'Email is required';
  static const String contactHoursRequired = 'Preferred contact hours is required';
  static const String validEmailRequired = 'Please enter a valid email address';
  static const String validPhoneRequired = 'Please enter a valid phone number';
  static const String validZipRequired = 'Please enter a valid zip code';
  static const String phoneMinLength = 'Please enter a valid phone number';
  static const String zipMinLength = 'Please enter a valid zip code';

  // Placeholder Hints
  static const String enterProjectName = 'Enter project name';
  static const String contactHoursExample = 'e.g., 9 AM - 5 PM';
  static const String businessCustomers = 'For business customers';
  static const String selectDate = 'Select Date';

  // Roof Data Screen
  static const String roofDataSiteDetails = 'Roof Data & Site Details';
  static const String roofOrientation = 'Roof Orientation';
  static const String dimensions = 'Dimensions';
  static const String roofType = 'Roof Type';
  static const String slope = 'Slope';
  static const String roofTileType = 'Roof Tile Type';
  static const String roofDetails = 'Roof Details';
  static const String hasWindows = 'Windows';
  static const String hasDormers = 'Dormers';
  static const String hasChimneyVent = 'Chimney/Vent';
  static const String photos = 'Photos';
  static const String roofSketch = 'Roof Sketch';
  static const String remarks = 'Remarks';
  static const String continueToElectricalData = 'Continue to Electrical Data';

  // Roof Orientations
  static const String north = 'North';
  static const String northeast = 'Northeast';
  static const String east = 'East';
  static const String southeast = 'Southeast';
  static const String south = 'South';
  static const String southwest = 'Southwest';
  static const String west = 'West';
  static const String northwest = 'Northwest';

  // Roof Types
  static const String gabled = 'Gabled';
  static const String hipped = 'Hipped';
  static const String flat = 'Flat';
  static const String shed = 'Shed';
  static const String gambrel = 'Gambrel';

  // Roof Tile Types
  static const String clay = 'Clay';
  static const String concrete = 'Concrete';
  static const String slate = 'Slate';
  static const String metal = 'Metal';
  static const String asphalt = 'Asphalt';

  // Electrical Data Screen
  static const String meterElectricalConnection = 'Meter & Electrical Connection';
  static const String meterBoxNumber = 'Meter Box Number';
  static const String emptyConduitPresent = 'Empty conduit present?';
  static const String sufficientSpaceAvailable = 'Sufficient space available?';
  static const String meterBoxPhoto = 'Photo of meter box';
  static const String consumptionData = 'Consumption Data';
  static const String annualElectricityConsumption = 'Annual electricity consumption';
  static const String specialConsumers = 'Special Consumers';
  static const String evChargingStation = 'EV/charging station';
  static const String airConditioning = 'Air conditioning';
  static const String heatPump = 'Heat pump';
  static const String other = 'Other';
  static const String powerMeasurementRequested = 'Power measurement requested before proposal?';
  static const String continueToPlanningOptions = 'Continue to Planning & Options';

  // Planning Options Screen
  static const String planningOptions = 'Planning & Options';
  static const String offerModules = 'Offer Modules';
  static const String batteryStorage = 'Battery storage';
  static const String wallbox = 'Wallbox';
  static const String optimizer = 'Optimizer';
  static const String financingDesired = 'Financing desired?';
  static const String scaffoldingProvidedByCustomer = 'Scaffolding provided by customer?';
  static const String furtherNotes = 'Further notes';
  static const String buildingPlansStructuralAnalysis = 'Building plans/structural analysis';
  static const String continueToPhotoUpload = 'Continue to Photo Upload';

  // Scaffolding Options
  static const String customerProvides = 'Customer Provides';

  // Photo Upload Screen
  static const String photoDocumentUpload = 'Photo & Document Upload';
  static const String photoUploadChecklist = 'Photo upload checklist';
  static const String roof = 'Roof';
  static const String meter = 'Meter';
  static const String controlCabinet = 'Control cabinet';
  static const String obstacles = 'Obstacles';
  static const String surroundings = 'Surroundings';
  static const String documentUpload = 'Document upload';
  static const String electricityBillPermitsPlans = 'e.g. electricity bill, permits, plans';
  static const String commentsForEachPhoto = 'Comments field for each photo/document';
  static const String continueToAdditionalComments = 'Continue to Additional Comments';

  // Additional Comments Screen
  static const String additionalComments = 'Additional Comments';
  static const String separateCommentsField = 'Separate comments field for other notes, remarks, or open questions';
  static const String specialRequestsOpenPoints = 'e.g., special requests, open points, internal communication';
  static const String continueToConfirmation = 'Continue to Confirmation';

  // Confirmation Screen
  static const String confirmationSubmission = 'Confirmation & Submission';
  static const String dataAccuracyConfirmation = 'Confirmation of data accuracy & owner\'s consent';
  static const String privacyNotice = 'Privacy Notice';
  static const String dataUsageNotice = 'Your data will only be used for PV system planning and transmitted securely.';
  static const String submitProject = 'Submit Project';
  static const String projectSubmittedSuccessfully = 'Project submitted successfully!';

  // Error Messages
  static const String failedToLoadProjects = 'Failed to load projects';
  static const String failedToSaveProject = 'Failed to save project';
  static const String failedToSubmitProject = 'Failed to submit project';
  static const String failedToHideProject = 'Failed to hide project';
  static const String failedToUnhideProject = 'Failed to unhide project';
  static const String failedToDeleteProject = 'Failed to delete project';
  static const String failedToSaveCustomerData = 'Failed to save customer data';

  // Document Types
  static const String electricityBill = 'Electricity Bill';
  static const String permit = 'Permit';
  static const String buildingPlan = 'Building Plan';

  // Photo Categories
  static const String roofPhotos = 'Roof Photos';
  static const String meterPhotos = 'Meter Photos';
  static const String controlCabinetPhotos = 'Control Cabinet Photos';
  static const String obstaclePhotos = 'Obstacle Photos';
  static const String surroundingPhotos = 'Surrounding Photos';

  // Additional strings for new screens
  static const String enterDimensions = 'Enter roof dimensions';
  static const String enterSlope = 'Enter roof slope (e.g., 30°)';
  static const String enterRemarks = 'Enter any additional remarks';
  static const String enterMeterBoxNumber = 'Enter meter box number';
  static const String enterAnnualConsumption = 'Enter annual consumption in kWh';
  static const String enterOtherConsumers = 'Specify other special consumers';
  static const String takePhoto = 'Take Photo';
  static const String selectFromGallery = 'Select from Gallery';
  static const String addDocument = 'Add Document';
  static const String photoComment = 'Photo Comment';
  static const String documentComment = 'Document Comment';
  static const String enterPhotoComment = 'Enter comment for this photo';
  static const String enterDocumentComment = 'Enter comment for this document';
  static const String noPhotosAdded = 'No photos added yet';
  static const String noDocumentsAdded = 'No documents added yet';
  static const String tapToAddPhotos = 'Tap the camera button to add photos';
  static const String tapToAddDocuments = 'Tap the document button to add documents';

  // Validation Messages for new screens
  static const String dimensionsRequired = 'Dimensions are required';
  static const String slopeRequired = 'Slope is required';
  static const String meterBoxNumberRequired = 'Meter box number is required';
  static const String annualConsumptionRequired = 'Annual consumption is required';
  static const String validConsumptionRequired = 'Please enter a valid consumption value';

  // Helper method to get field name with required indicator
  static String requiredField(String fieldName) => '$fieldName *';

  // Helper method to get validation message
  static String fieldRequired(String fieldName) => '$fieldName is required';
}
