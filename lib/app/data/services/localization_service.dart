import 'package:get/get.dart';
import 'app_strings.dart';

class LocalizationService extends GetxService {
  // Get string from AppStrings constants
  String getString(String key) {
    return _getEnglishString(key);
  }

  String _getEnglishString(String key) {
    // Return the constant from AppStrings
    switch (key) {
      case 'appName':
        return AppStrings.appName;
      case 'appSubtitle':
        return AppStrings.appSubtitle;
      case 'login':
        return AppStrings.login;
      case 'logout':
        return AppStrings.logout;
      case 'save':
        return AppStrings.save;
      case 'cancel':
        return AppStrings.cancel;
      case 'delete':
        return AppStrings.delete;
      case 'edit':
        return AppStrings.edit;
      case 'submit':
        return AppStrings.submit;
      case 'continue':
        return AppStrings.continue_;
      case 'back':
        return AppStrings.back;
      case 'next':
        return AppStrings.next;
      case 'ok':
        return AppStrings.ok;
      case 'yes':
        return AppStrings.yes;
      case 'no':
        return AppStrings.no;
      case 'username':
        return AppStrings.username;
      case 'password':
        return AppStrings.password;
      case 'rememberMe':
        return AppStrings.rememberMe;
      case 'forgotPassword':
        return AppStrings.forgotPassword;
      case 'contactSupport':
        return AppStrings.contactSupport;
      case 'projects':
        return AppStrings.projects;
      case 'customerLocation':
        return AppStrings.customerLocation;
      case 'projectName':
        return AppStrings.projectName;
      case 'firstName':
        return AppStrings.firstName;
      case 'lastName':
        return AppStrings.lastName;
      case 'email':
        return AppStrings.email;
      case 'phone':
        return AppStrings.phone;
      case 'mobile':
        return AppStrings.mobile;
      case 'street':
        return AppStrings.street;
      case 'city':
        return AppStrings.city;
      case 'zipCode':
        return AppStrings.zipCode;
      case 'buildingType':
        return AppStrings.buildingType;
      case 'comments':
        return AppStrings.comments;
      case 'siteVisit':
        return AppStrings.siteVisit;
      case 'proposal':
        return AppStrings.proposal;
      case 'orderReceived':
        return AppStrings.orderReceived;
      case 'installation':
        return AppStrings.installation;
      case 'completed':
        return AppStrings.completed;
      case 'error':
        return AppStrings.error;
      case 'success':
        return AppStrings.success;
      case 'enterUsernamePassword':
        return AppStrings.enterUsernamePassword;
      case 'usernamePartnerCode':
        return AppStrings.usernamePartnerCode;
      case 'saveDraft':
        return AppStrings.saveDraft;
      // Roof Data Strings
      case 'roofDataSiteDetails':
        return AppStrings.roofDataSiteDetails;
      case 'roofOrientation':
        return AppStrings.roofOrientation;
      case 'dimensions':
        return AppStrings.dimensions;
      case 'roofType':
        return AppStrings.roofType;
      case 'slope':
        return AppStrings.slope;
      case 'roofTileType':
        return AppStrings.roofTileType;
      case 'roofDetails':
        return AppStrings.roofDetails;
      case 'hasWindows':
        return AppStrings.hasWindows;
      case 'hasDormers':
        return AppStrings.hasDormers;
      case 'hasChimneyVent':
        return AppStrings.hasChimneyVent;
      case 'roofSketch':
        return AppStrings.roofSketch;
      case 'remarks':
        return AppStrings.remarks;
      case 'continueToElectricalData':
        return AppStrings.continueToElectricalData;
      // Roof Orientations
      case 'north':
        return AppStrings.north;
      case 'northeast':
        return AppStrings.northeast;
      case 'east':
        return AppStrings.east;
      case 'southeast':
        return AppStrings.southeast;
      case 'south':
        return AppStrings.south;
      case 'southwest':
        return AppStrings.southwest;
      case 'west':
        return AppStrings.west;
      case 'northwest':
        return AppStrings.northwest;
      // Roof Types
      case 'gabled':
        return AppStrings.gabled;
      case 'hipped':
        return AppStrings.hipped;
      case 'flat':
        return AppStrings.flat;
      case 'shed':
        return AppStrings.shed;
      case 'gambrel':
        return AppStrings.gambrel;
      // Roof Tile Types
      case 'clay':
        return AppStrings.clay;
      case 'concrete':
        return AppStrings.concrete;
      case 'slate':
        return AppStrings.slate;
      case 'metal':
        return AppStrings.metal;
      case 'asphalt':
        return AppStrings.asphalt;
      // Electrical Data Strings
      case 'meterElectricalConnection':
        return AppStrings.meterElectricalConnection;
      case 'meterBoxNumber':
        return AppStrings.meterBoxNumber;
      case 'emptyConduitPresent':
        return AppStrings.emptyConduitPresent;
      case 'sufficientSpaceAvailable':
        return AppStrings.sufficientSpaceAvailable;
      case 'meterBoxPhoto':
        return AppStrings.meterBoxPhoto;
      case 'consumptionData':
        return AppStrings.consumptionData;
      case 'annualElectricityConsumption':
        return AppStrings.annualElectricityConsumption;
      case 'specialConsumers':
        return AppStrings.specialConsumers;
      case 'evChargingStation':
        return AppStrings.evChargingStation;
      case 'airConditioning':
        return AppStrings.airConditioning;
      case 'heatPump':
        return AppStrings.heatPump;
      case 'other':
        return AppStrings.other;
      case 'powerMeasurementRequested':
        return AppStrings.powerMeasurementRequested;
      case 'continueToPlanningOptions':
        return AppStrings.continueToPlanningOptions;
      // Photo Upload Strings
      case 'photoDocumentUpload':
        return AppStrings.photoDocumentUpload;
      case 'photoUploadChecklist':
        return AppStrings.photoUploadChecklist;
      case 'roof':
        return AppStrings.roof;
      case 'meter':
        return AppStrings.meter;
      case 'controlCabinet':
        return AppStrings.controlCabinet;
      case 'obstacles':
        return AppStrings.obstacles;
      case 'surroundings':
        return AppStrings.surroundings;
      case 'documentUpload':
        return AppStrings.documentUpload;
      case 'continueToAdditionalComments':
        return AppStrings.continueToAdditionalComments;
      case 'takePhoto':
        return AppStrings.takePhoto;
      case 'selectFromGallery':
        return AppStrings.selectFromGallery;
      case 'addDocument':
        return AppStrings.addDocument;
      case 'photoComment':
        return AppStrings.photoComment;
      case 'noPhotosAdded':
        return AppStrings.noPhotosAdded;
      case 'tapToAddPhotos':
        return AppStrings.tapToAddPhotos;
      default:
        return key; // Return key if not found
    }
  }

  // Convenience method for controllers
  String tr(String key) => getString(key);
}
