import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'storage_service.dart';
import 'app_strings.dart';

class LocalizationService extends GetxService {
  final StorageService _storageService = Get.find<StorageService>();
  
  final currentLanguage = 'en'.obs;
  final availableLanguages = ['en', 'de', 'fr', 'es'].obs;
  
  // Language display names
  final languageNames = {
    'en': 'English',
    'de': 'Deutsch',
    'fr': 'Français',
    'es': 'Español',
  };

  @override
  void onInit() {
    super.onInit();
    _loadSavedLanguage();
  }

  void _loadSavedLanguage() {
    final settings = _storageService.getAppSettings();
    final savedLanguage = settings['language'] as String?;
    if (savedLanguage != null && availableLanguages.contains(savedLanguage)) {
      currentLanguage.value = savedLanguage;
    }
  }

  Future<void> changeLanguage(String languageCode) async {
    if (availableLanguages.contains(languageCode)) {
      currentLanguage.value = languageCode;
      
      // Save to storage
      final settings = _storageService.getAppSettings();
      settings['language'] = languageCode;
      await _storageService.saveAppSettings(settings);
      
      // Update GetX locale
      final locale = _getLocaleFromLanguage(languageCode);
      Get.updateLocale(locale);
    }
  }

  Locale _getLocaleFromLanguage(String languageCode) {
    switch (languageCode) {
      case 'de':
        return const Locale('de', 'DE');
      case 'fr':
        return const Locale('fr', 'FR');
      case 'es':
        return const Locale('es', 'ES');
      default:
        return const Locale('en', 'US');
    }
  }

  // Get localized string based on current language
  String getString(String key) {
    switch (currentLanguage.value) {
      case 'de':
        return _getGermanString(key);
      case 'fr':
        return _getFrenchString(key);
      case 'es':
        return _getSpanishString(key);
      default:
        return _getEnglishString(key);
    }
  }

  String _getEnglishString(String key) {
    // Return the constant from AppStrings
    switch (key) {
      case 'appName':
        return AppStrings.appName;
      case 'appSubtitle':
        return AppStrings.appSubtitle;
      case 'login':
        return AppStrings.login;
      case 'logout':
        return AppStrings.logout;
      case 'save':
        return AppStrings.save;
      case 'cancel':
        return AppStrings.cancel;
      case 'delete':
        return AppStrings.delete;
      case 'edit':
        return AppStrings.edit;
      case 'submit':
        return AppStrings.submit;
      case 'continue':
        return AppStrings.continue_;
      case 'back':
        return AppStrings.back;
      case 'next':
        return AppStrings.next;
      case 'ok':
        return AppStrings.ok;
      case 'yes':
        return AppStrings.yes;
      case 'no':
        return AppStrings.no;
      case 'username':
        return AppStrings.username;
      case 'password':
        return AppStrings.password;
      case 'rememberMe':
        return AppStrings.rememberMe;
      case 'forgotPassword':
        return AppStrings.forgotPassword;
      case 'contactSupport':
        return AppStrings.contactSupport;
      case 'projects':
        return AppStrings.projects;
      case 'customerLocation':
        return AppStrings.customerLocation;
      case 'projectName':
        return AppStrings.projectName;
      case 'firstName':
        return AppStrings.firstName;
      case 'lastName':
        return AppStrings.lastName;
      case 'email':
        return AppStrings.email;
      case 'phone':
        return AppStrings.phone;
      case 'mobile':
        return AppStrings.mobile;
      case 'street':
        return AppStrings.street;
      case 'city':
        return AppStrings.city;
      case 'zipCode':
        return AppStrings.zipCode;
      case 'buildingType':
        return AppStrings.buildingType;
      case 'comments':
        return AppStrings.comments;
      case 'siteVisit':
        return AppStrings.siteVisit;
      case 'proposal':
        return AppStrings.proposal;
      case 'orderReceived':
        return AppStrings.orderReceived;
      case 'installation':
        return AppStrings.installation;
      case 'completed':
        return AppStrings.completed;
      case 'error':
        return AppStrings.error;
      case 'success':
        return AppStrings.success;
      case 'enterUsernamePassword':
        return AppStrings.enterUsernamePassword;
      case 'usernamePartnerCode':
        return AppStrings.usernamePartnerCode;
      case 'customerLocation':
        return AppStrings.customerLocation;
      case 'saveDraft':
        return AppStrings.saveDraft;
      default:
        return key; // Return key if not found
    }
  }

  String _getGermanString(String key) {
    switch (key) {
      case 'appName':
        return 'Sunalyze';
      case 'appSubtitle':
        return 'Solaranlagen-Installationsmanagement';
      case 'login':
        return 'Anmelden';
      case 'logout':
        return 'Abmelden';
      case 'save':
        return 'Speichern';
      case 'cancel':
        return 'Abbrechen';
      case 'delete':
        return 'Löschen';
      case 'edit':
        return 'Bearbeiten';
      case 'submit':
        return 'Einreichen';
      case 'continue':
        return 'Weiter';
      case 'back':
        return 'Zurück';
      case 'next':
        return 'Weiter';
      case 'ok':
        return 'OK';
      case 'yes':
        return 'Ja';
      case 'no':
        return 'Nein';
      case 'username':
        return 'Benutzername';
      case 'password':
        return 'Passwort';
      case 'rememberMe':
        return 'Angemeldet bleiben';
      case 'forgotPassword':
        return 'Passwort vergessen?';
      case 'contactSupport':
        return 'Support kontaktieren';
      case 'projects':
        return 'Projekte';
      case 'customerLocation':
        return 'Kunde & Standort';
      case 'projectName':
        return 'Projektname';
      case 'firstName':
        return 'Vorname';
      case 'lastName':
        return 'Nachname';
      case 'email':
        return 'E-Mail';
      case 'phone':
        return 'Telefon';
      case 'mobile':
        return 'Mobil';
      case 'street':
        return 'Straße';
      case 'city':
        return 'Stadt';
      case 'zipCode':
        return 'Postleitzahl';
      case 'buildingType':
        return 'Gebäudetyp';
      case 'comments':
        return 'Kommentare';
      case 'siteVisit':
        return 'Vor-Ort-Termin';
      case 'proposal':
        return 'Angebot';
      case 'orderReceived':
        return 'Auftrag erhalten';
      case 'installation':
        return 'Installation';
      case 'completed':
        return 'Abgeschlossen';
      case 'error':
        return 'Fehler';
      case 'success':
        return 'Erfolg';
      case 'enterUsernamePassword':
        return 'Bitte geben Sie Benutzername und Passwort ein';
      case 'usernamePartnerCode':
        return 'Benutzername / Partner-Code';
      case 'customerLocation':
        return 'Kunde & Standort';
      case 'saveDraft':
        return 'Entwurf speichern';
      default:
        return _getEnglishString(key); // Fallback to English
    }
  }

  String _getFrenchString(String key) {
    switch (key) {
      case 'appName':
        return 'Sunalyze';
      case 'appSubtitle':
        return 'Gestion d\'installation de panneaux solaires';
      case 'login':
        return 'Connexion';
      case 'logout':
        return 'Déconnexion';
      case 'save':
        return 'Enregistrer';
      case 'cancel':
        return 'Annuler';
      case 'delete':
        return 'Supprimer';
      case 'edit':
        return 'Modifier';
      case 'submit':
        return 'Soumettre';
      case 'continue':
        return 'Continuer';
      case 'back':
        return 'Retour';
      case 'next':
        return 'Suivant';
      case 'ok':
        return 'OK';
      case 'yes':
        return 'Oui';
      case 'no':
        return 'Non';
      case 'username':
        return 'Nom d\'utilisateur';
      case 'password':
        return 'Mot de passe';
      case 'rememberMe':
        return 'Se souvenir de moi';
      case 'forgotPassword':
        return 'Mot de passe oublié?';
      case 'contactSupport':
        return 'Contacter le support';
      case 'projects':
        return 'Projets';
      case 'customerLocation':
        return 'Client et localisation';
      case 'projectName':
        return 'Nom du projet';
      case 'firstName':
        return 'Prénom';
      case 'lastName':
        return 'Nom de famille';
      case 'email':
        return 'E-mail';
      case 'phone':
        return 'Téléphone';
      case 'mobile':
        return 'Mobile';
      case 'street':
        return 'Rue';
      case 'city':
        return 'Ville';
      case 'zipCode':
        return 'Code postal';
      case 'buildingType':
        return 'Type de bâtiment';
      case 'comments':
        return 'Commentaires';
      case 'siteVisit':
        return 'Visite sur site';
      case 'proposal':
        return 'Proposition';
      case 'orderReceived':
        return 'Commande reçue';
      case 'installation':
        return 'Installation';
      case 'completed':
        return 'Terminé';
      default:
        return _getEnglishString(key); // Fallback to English
    }
  }

  String _getSpanishString(String key) {
    switch (key) {
      case 'appName':
        return 'Sunalyze';
      case 'appSubtitle':
        return 'Gestión de instalación de paneles solares';
      case 'login':
        return 'Iniciar sesión';
      case 'logout':
        return 'Cerrar sesión';
      case 'save':
        return 'Guardar';
      case 'cancel':
        return 'Cancelar';
      case 'delete':
        return 'Eliminar';
      case 'edit':
        return 'Editar';
      case 'submit':
        return 'Enviar';
      case 'continue':
        return 'Continuar';
      case 'back':
        return 'Atrás';
      case 'next':
        return 'Siguiente';
      case 'ok':
        return 'OK';
      case 'yes':
        return 'Sí';
      case 'no':
        return 'No';
      case 'username':
        return 'Nombre de usuario';
      case 'password':
        return 'Contraseña';
      case 'rememberMe':
        return 'Recordarme';
      case 'forgotPassword':
        return '¿Olvidaste tu contraseña?';
      case 'contactSupport':
        return 'Contactar soporte';
      case 'projects':
        return 'Proyectos';
      case 'customerLocation':
        return 'Cliente y ubicación';
      case 'projectName':
        return 'Nombre del proyecto';
      case 'firstName':
        return 'Nombre';
      case 'lastName':
        return 'Apellido';
      case 'email':
        return 'Correo electrónico';
      case 'phone':
        return 'Teléfono';
      case 'mobile':
        return 'Móvil';
      case 'street':
        return 'Calle';
      case 'city':
        return 'Ciudad';
      case 'zipCode':
        return 'Código postal';
      case 'buildingType':
        return 'Tipo de edificio';
      case 'comments':
        return 'Comentarios';
      case 'siteVisit':
        return 'Visita al sitio';
      case 'proposal':
        return 'Propuesta';
      case 'orderReceived':
        return 'Pedido recibido';
      case 'installation':
        return 'Instalación';
      case 'completed':
        return 'Completado';
      default:
        return _getEnglishString(key); // Fallback to English
    }
  }

  // Convenience method for controllers
  String tr(String key) => getString(key);
}
