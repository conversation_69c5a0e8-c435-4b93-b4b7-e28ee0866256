import 'dart:convert';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/project_model.dart';

class StorageService extends GetxService {
  late SharedPreferences _prefs;

  Future<StorageService> init() async {
    _prefs = await SharedPreferences.getInstance();
    return this;
  }

  // User Authentication
  Future<void> saveUserCredentials(String username, String password) async {
    await _prefs.setString('username', username);
    await _prefs.setString('password', password);
  }

  String? getUsername() => _prefs.getString('username');
  String? getPassword() => _prefs.getString('password');

  Future<void> clearUserCredentials() async {
    await _prefs.remove('username');
    await _prefs.remove('password');
  }

  bool get isLoggedIn => getUsername() != null && getPassword() != null;

  // Projects Management
  Future<void> saveProjects(List<Project> projects) async {
    final projectsJson = projects.map((project) => project.toJson()).toList();
    await _prefs.setString('projects', jsonEncode(projectsJson));
  }

  List<Project> getProjects() {
    final projectsString = _prefs.getString('projects');
    if (projectsString == null) return [];
    
    final projectsJson = jsonDecode(projectsString) as List;
    return projectsJson.map((json) => Project.fromJson(json)).toList();
  }

  Future<void> saveProject(Project project) async {
    final projects = getProjects();
    final existingIndex = projects.indexWhere((p) => p.id == project.id);
    
    if (existingIndex != -1) {
      projects[existingIndex] = project;
    } else {
      project.id = DateTime.now().millisecondsSinceEpoch.toString();
      projects.add(project);
    }
    
    await saveProjects(projects);
  }

  Future<void> deleteProject(String projectId) async {
    final projects = getProjects();
    projects.removeWhere((project) => project.id == projectId);
    await saveProjects(projects);
  }

  Project? getProject(String projectId) {
    final projects = getProjects();
    try {
      return projects.firstWhere((project) => project.id == projectId);
    } catch (e) {
      return null;
    }
  }

  // App Settings
  Future<void> saveAppSettings(Map<String, dynamic> settings) async {
    await _prefs.setString('app_settings', jsonEncode(settings));
  }

  Map<String, dynamic> getAppSettings() {
    final settingsString = _prefs.getString('app_settings');
    if (settingsString == null) return {};
    return jsonDecode(settingsString);
  }

  // Draft Management
  Future<void> saveDraft(String key, Map<String, dynamic> data) async {
    await _prefs.setString('draft_$key', jsonEncode(data));
  }

  Map<String, dynamic>? getDraft(String key) {
    final draftString = _prefs.getString('draft_$key');
    if (draftString == null) return null;
    return jsonDecode(draftString);
  }

  Future<void> clearDraft(String key) async {
    await _prefs.remove('draft_$key');
  }

  // Clear all data
  Future<void> clearAllData() async {
    await _prefs.clear();
  }
}
