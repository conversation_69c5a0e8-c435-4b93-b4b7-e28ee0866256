# Localization System Documentation

## Overview
The Sunalyze app uses a comprehensive localization system that allows for easy translation and management of all text content throughout the application. The system supports multiple languages and provides a centralized way to manage all strings.

## File Structure

### 1. `app_strings.dart`
Contains all English text constants used throughout the app. This serves as the base language and fallback for any missing translations.

### 2. `localization_service.dart`
The main service that handles language switching and string retrieval. It supports:
- English (en) - Default
- German (de)
- French (fr)
- Spanish (es)

### 3. `extensions.dart`
Provides convenient extension methods for easy access to localized strings.

## How to Use

### Method 1: Using Extensions (Recommended)
```dart
import '../../../core/utils/extensions.dart';

// In any widget or controller
Text('projectName'.tr)  // Returns localized "Project Name"
Text('save'.tr)         // Returns localized "Save"
```

### Method 2: Using the Service Directly
```dart
final localization = Get.find<LocalizationService>();
Text(localization.getString('projectName'))
```

### Method 3: In Controllers
```dart
class MyController extends GetxController {
  void showMessage() {
    Get.snackbar(
      tr('success'),  // Using the extension method
      tr('projectSaved'),
    );
  }
}
```

## Adding New Strings

### Step 1: Add to AppStrings
```dart
// In app_strings.dart
class AppStrings {
  static const String newFeature = 'New Feature';
  static const String newFeatureDescription = 'This is a new feature description';
}
```

### Step 2: Add to Localization Service
```dart
// In localization_service.dart - _getEnglishString method
case 'newFeature':
  return AppStrings.newFeature;
case 'newFeatureDescription':
  return AppStrings.newFeatureDescription;

// In _getGermanString method
case 'newFeature':
  return 'Neue Funktion';
case 'newFeatureDescription':
  return 'Dies ist eine neue Funktionsbeschreibung';

// Repeat for other languages...
```

### Step 3: Use in Your Code
```dart
Text('newFeature'.tr)
Text('newFeatureDescription'.tr)
```

## Language Support

### Currently Supported Languages:
- **English (en)** - Default/Fallback
- **German (de)** - Deutsch
- **French (fr)** - Français
- **Spanish (es)** - Español

### Adding a New Language:

1. **Update LocalizationService:**
```dart
final availableLanguages = ['en', 'de', 'fr', 'es', 'it'].obs; // Add 'it'

final languageNames = {
  'en': 'English',
  'de': 'Deutsch',
  'fr': 'Français',
  'es': 'Español',
  'it': 'Italiano', // Add Italian
};
```

2. **Add Translation Method:**
```dart
String _getItalianString(String key) {
  switch (key) {
    case 'appName':
      return 'Sunalyze';
    case 'login':
      return 'Accedi';
    case 'save':
      return 'Salva';
    // ... add all translations
    default:
      return _getEnglishString(key); // Fallback to English
  }
}
```

3. **Update Main getString Method:**
```dart
String getString(String key) {
  switch (currentLanguage.value) {
    case 'de':
      return _getGermanString(key);
    case 'fr':
      return _getFrenchString(key);
    case 'es':
      return _getSpanishString(key);
    case 'it':
      return _getItalianString(key); // Add this line
    default:
      return _getEnglishString(key);
  }
}
```

## Language Switching

### Using the Language Selector Widget:
```dart
// Show language selector dialog
LanguageSelectorDialog.show();

// Embed language selector in a screen
LanguageSelector(
  showTitle: true,
  padding: EdgeInsets.all(16),
)
```

### Programmatic Language Change:
```dart
final localization = Get.find<LocalizationService>();
await localization.changeLanguage('de'); // Switch to German
```

## Best Practices

### 1. Key Naming Convention
- Use camelCase for keys: `projectName`, `customerLocation`
- Use descriptive names: `loginSuccessful` instead of `msg1`
- Group related keys: `errorInvalidEmail`, `errorPasswordTooShort`

### 2. Fallback Strategy
- Always provide English as fallback
- If a translation is missing, the system falls back to English
- If English is also missing, the key itself is returned

### 3. Context-Aware Translations
```dart
// For form validation
static String fieldRequired(String fieldName) => '$fieldName is required';

// Usage
String validateEmail(String? value) {
  if (value?.isEmpty ?? true) {
    return AppStrings.fieldRequired('email'.tr);
  }
  return null;
}
```

### 4. Pluralization
```dart
// In AppStrings
static String itemCount(int count) {
  return count == 1 ? '$count item' : '$count items';
}

// In German translation
static String itemCountGerman(int count) {
  return count == 1 ? '$count Element' : '$count Elemente';
}
```

## Testing Localization

### 1. Test All Languages
```dart
void testAllLanguages() {
  final localization = Get.find<LocalizationService>();
  
  for (String lang in localization.availableLanguages) {
    localization.changeLanguage(lang);
    // Test your screens here
  }
}
```

### 2. Check for Missing Translations
```dart
void checkMissingTranslations() {
  final localization = Get.find<LocalizationService>();
  
  // If a key returns itself, translation is missing
  String result = localization.getString('someKey');
  if (result == 'someKey') {
    print('Missing translation for: someKey');
  }
}
```

## Performance Considerations

1. **Lazy Loading**: Translations are loaded on-demand
2. **Caching**: Current language is cached in SharedPreferences
3. **Memory**: Only current language strings are kept in memory
4. **Fallback**: Fast fallback to English if translation missing

## Example Implementation

Here's a complete example of implementing localization in a new screen:

```dart
// 1. Add strings to app_strings.dart
static const String newScreenTitle = 'New Screen';
static const String newScreenButton = 'Click Me';

// 2. Add translations to localization_service.dart
case 'newScreenTitle':
  return 'Neue Bildschirm'; // German
case 'newScreenButton':
  return 'Klick mich'; // German

// 3. Use in your widget
class NewScreen extends GetView<NewController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('newScreenTitle'.tr),
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {},
          child: Text('newScreenButton'.tr),
        ),
      ),
    );
  }
}
```

This localization system ensures that your app can easily support multiple languages while maintaining clean, maintainable code.
