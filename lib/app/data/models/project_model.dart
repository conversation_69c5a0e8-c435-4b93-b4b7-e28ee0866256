class Project {
  String? id;
  String projectName;
  ProjectStatus status;
  DateTime lastChange;
  bool isHidden;
  CustomerData? customerData;
  RoofData? roofData;
  ElectricalData? electricalData;
  PlanningOptions? planningOptions;
  List<ProjectPhoto>? photos;
  List<ProjectDocument>? documents;
  String? additionalComments;
  bool isSubmitted;
  DateTime? submissionDate;

  Project({
    this.id,
    required this.projectName,
    this.status = ProjectStatus.siteVisit,
    DateTime? lastChange,
    this.isHidden = false,
    this.customerData,
    this.roofData,
    this.electricalData,
    this.planningOptions,
    this.photos,
    this.documents,
    this.additionalComments,
    this.isSubmitted = false,
    this.submissionDate,
  }) : lastChange = lastChange ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'projectName': projectName,
      'status': status.index,
      'lastChange': lastChange.toIso8601String(),
      'isHidden': isHidden,
      'customerData': customerData?.toJson(),
      'roofData': roofData?.toJson(),
      'electricalData': electricalData?.toJson(),
      'planningOptions': planningOptions?.toJson(),
      'photos': photos?.map((photo) => photo.toJson()).toList(),
      'documents': documents?.map((doc) => doc.toJson()).toList(),
      'additionalComments': additionalComments,
      'isSubmitted': isSubmitted,
      'submissionDate': submissionDate?.toIso8601String(),
    };
  }

  factory Project.fromJson(Map<String, dynamic> json) {
    return Project(
      id: json['id'],
      projectName: json['projectName'],
      status: ProjectStatus.values[json['status'] ?? 0],
      lastChange: DateTime.parse(json['lastChange']),
      isHidden: json['isHidden'] ?? false,
      customerData: json['customerData'] != null 
          ? CustomerData.fromJson(json['customerData']) 
          : null,
      roofData: json['roofData'] != null 
          ? RoofData.fromJson(json['roofData']) 
          : null,
      electricalData: json['electricalData'] != null 
          ? ElectricalData.fromJson(json['electricalData']) 
          : null,
      planningOptions: json['planningOptions'] != null 
          ? PlanningOptions.fromJson(json['planningOptions']) 
          : null,
      photos: json['photos'] != null 
          ? (json['photos'] as List).map((photo) => ProjectPhoto.fromJson(photo)).toList()
          : null,
      documents: json['documents'] != null 
          ? (json['documents'] as List).map((doc) => ProjectDocument.fromJson(doc)).toList()
          : null,
      additionalComments: json['additionalComments'],
      isSubmitted: json['isSubmitted'] ?? false,
      submissionDate: json['submissionDate'] != null 
          ? DateTime.parse(json['submissionDate']) 
          : null,
    );
  }
}

enum ProjectStatus {
  siteVisit,
  proposal,
  orderReceived,
  installation,
  completed,
}

extension ProjectStatusExtension on ProjectStatus {
  String get displayName {
    switch (this) {
      case ProjectStatus.siteVisit:
        return 'Site Visit';
      case ProjectStatus.proposal:
        return 'Proposal';
      case ProjectStatus.orderReceived:
        return 'Order Received';
      case ProjectStatus.installation:
        return 'Installation';
      case ProjectStatus.completed:
        return 'Completed';
    }
  }

  double get progress {
    switch (this) {
      case ProjectStatus.siteVisit:
        return 0.2;
      case ProjectStatus.proposal:
        return 0.4;
      case ProjectStatus.orderReceived:
        return 0.6;
      case ProjectStatus.installation:
        return 0.8;
      case ProjectStatus.completed:
        return 1.0;
    }
  }
}

class CustomerData {
  String lastName;
  String firstName;
  String street;
  String houseNumber;
  String zipCode;
  String city;
  String phone;
  String mobile;
  String email;
  String preferredContactHours;
  String? company;
  BuildingType buildingType;
  int yearOfConstruction;
  bool isBuildingOwner;
  bool interestedInTenantElectricity;
  String? comments;
  DateTime? siteVisitDate;

  CustomerData({
    required this.lastName,
    required this.firstName,
    required this.street,
    required this.houseNumber,
    required this.zipCode,
    required this.city,
    required this.phone,
    required this.mobile,
    required this.email,
    required this.preferredContactHours,
    this.company,
    required this.buildingType,
    required this.yearOfConstruction,
    required this.isBuildingOwner,
    required this.interestedInTenantElectricity,
    this.comments,
    this.siteVisitDate,
  });

  Map<String, dynamic> toJson() {
    return {
      'lastName': lastName,
      'firstName': firstName,
      'street': street,
      'houseNumber': houseNumber,
      'zipCode': zipCode,
      'city': city,
      'phone': phone,
      'mobile': mobile,
      'email': email,
      'preferredContactHours': preferredContactHours,
      'company': company,
      'buildingType': buildingType.index,
      'yearOfConstruction': yearOfConstruction,
      'isBuildingOwner': isBuildingOwner,
      'interestedInTenantElectricity': interestedInTenantElectricity,
      'comments': comments,
      'siteVisitDate': siteVisitDate?.toIso8601String(),
    };
  }

  factory CustomerData.fromJson(Map<String, dynamic> json) {
    return CustomerData(
      lastName: json['lastName'],
      firstName: json['firstName'],
      street: json['street'],
      houseNumber: json['houseNumber'],
      zipCode: json['zipCode'],
      city: json['city'],
      phone: json['phone'],
      mobile: json['mobile'],
      email: json['email'],
      preferredContactHours: json['preferredContactHours'],
      company: json['company'],
      buildingType: BuildingType.values[json['buildingType']],
      yearOfConstruction: json['yearOfConstruction'],
      isBuildingOwner: json['isBuildingOwner'],
      interestedInTenantElectricity: json['interestedInTenantElectricity'],
      comments: json['comments'],
      siteVisitDate: json['siteVisitDate'] != null 
          ? DateTime.parse(json['siteVisitDate']) 
          : null,
    );
  }
}

enum BuildingType {
  singleFamily,
  multiFamily,
  commercial,
  industrial,
}

extension BuildingTypeExtension on BuildingType {
  String get displayName {
    switch (this) {
      case BuildingType.singleFamily:
        return 'Single Family House';
      case BuildingType.multiFamily:
        return 'Multi Family House';
      case BuildingType.commercial:
        return 'Commercial Building';
      case BuildingType.industrial:
        return 'Industrial Building';
    }
  }
}

class RoofData {
  RoofOrientation orientation;
  String dimensions;
  RoofType roofType;
  String slope;
  RoofTileType roofTileType;
  bool hasWindows;
  bool hasDormers;
  bool hasChimneyVent;
  List<String>? photosPaths;
  String? roofSketch;
  String? remarks;

  RoofData({
    required this.orientation,
    required this.dimensions,
    required this.roofType,
    required this.slope,
    required this.roofTileType,
    required this.hasWindows,
    required this.hasDormers,
    required this.hasChimneyVent,
    this.photosPaths,
    this.roofSketch,
    this.remarks,
  });

  Map<String, dynamic> toJson() {
    return {
      'orientation': orientation.index,
      'dimensions': dimensions,
      'roofType': roofType.index,
      'slope': slope,
      'roofTileType': roofTileType.index,
      'hasWindows': hasWindows,
      'hasDormers': hasDormers,
      'hasChimneyVent': hasChimneyVent,
      'photosPaths': photosPaths,
      'roofSketch': roofSketch,
      'remarks': remarks,
    };
  }

  factory RoofData.fromJson(Map<String, dynamic> json) {
    return RoofData(
      orientation: RoofOrientation.values[json['orientation']],
      dimensions: json['dimensions'],
      roofType: RoofType.values[json['roofType']],
      slope: json['slope'],
      roofTileType: RoofTileType.values[json['roofTileType']],
      hasWindows: json['hasWindows'],
      hasDormers: json['hasDormers'],
      hasChimneyVent: json['hasChimneyVent'],
      photosPaths: json['photosPaths']?.cast<String>(),
      roofSketch: json['roofSketch'],
      remarks: json['remarks'],
    );
  }
}

enum RoofOrientation {
  north,
  northeast,
  east,
  southeast,
  south,
  southwest,
  west,
  northwest,
}

enum RoofType {
  gabled,
  hipped,
  flat,
  shed,
  gambrel,
}

enum RoofTileType {
  clay,
  concrete,
  slate,
  metal,
  asphalt,
}

class ElectricalData {
  String meterBoxNumber;
  bool hasEmptyConduit;
  bool hasSufficientSpace;
  String? meterBoxPhotoPath;
  double annualConsumption;
  bool hasEVChargingStation;
  bool hasAirConditioning;
  bool hasHeatPump;
  String? otherConsumers;
  bool powerMeasurementRequested;

  ElectricalData({
    required this.meterBoxNumber,
    required this.hasEmptyConduit,
    required this.hasSufficientSpace,
    this.meterBoxPhotoPath,
    required this.annualConsumption,
    required this.hasEVChargingStation,
    required this.hasAirConditioning,
    required this.hasHeatPump,
    this.otherConsumers,
    required this.powerMeasurementRequested,
  });

  Map<String, dynamic> toJson() {
    return {
      'meterBoxNumber': meterBoxNumber,
      'hasEmptyConduit': hasEmptyConduit,
      'hasSufficientSpace': hasSufficientSpace,
      'meterBoxPhotoPath': meterBoxPhotoPath,
      'annualConsumption': annualConsumption,
      'hasEVChargingStation': hasEVChargingStation,
      'hasAirConditioning': hasAirConditioning,
      'hasHeatPump': hasHeatPump,
      'otherConsumers': otherConsumers,
      'powerMeasurementRequested': powerMeasurementRequested,
    };
  }

  factory ElectricalData.fromJson(Map<String, dynamic> json) {
    return ElectricalData(
      meterBoxNumber: json['meterBoxNumber'],
      hasEmptyConduit: json['hasEmptyConduit'],
      hasSufficientSpace: json['hasSufficientSpace'],
      meterBoxPhotoPath: json['meterBoxPhotoPath'],
      annualConsumption: json['annualConsumption'].toDouble(),
      hasEVChargingStation: json['hasEVChargingStation'],
      hasAirConditioning: json['hasAirConditioning'],
      hasHeatPump: json['hasHeatPump'],
      otherConsumers: json['otherConsumers'],
      powerMeasurementRequested: json['powerMeasurementRequested'],
    );
  }
}

class PlanningOptions {
  bool batteryStorage;
  bool wallbox;
  bool optimizer;
  bool airConditioning;
  String? otherModules;
  bool financingDesired;
  ScaffoldingOption scaffoldingOption;
  String? furtherNotes;
  String? buildingPlansPath;

  PlanningOptions({
    required this.batteryStorage,
    required this.wallbox,
    required this.optimizer,
    required this.airConditioning,
    this.otherModules,
    required this.financingDesired,
    required this.scaffoldingOption,
    this.furtherNotes,
    this.buildingPlansPath,
  });

  Map<String, dynamic> toJson() {
    return {
      'batteryStorage': batteryStorage,
      'wallbox': wallbox,
      'optimizer': optimizer,
      'airConditioning': airConditioning,
      'otherModules': otherModules,
      'financingDesired': financingDesired,
      'scaffoldingOption': scaffoldingOption.index,
      'furtherNotes': furtherNotes,
      'buildingPlansPath': buildingPlansPath,
    };
  }

  factory PlanningOptions.fromJson(Map<String, dynamic> json) {
    return PlanningOptions(
      batteryStorage: json['batteryStorage'],
      wallbox: json['wallbox'],
      optimizer: json['optimizer'],
      airConditioning: json['airConditioning'],
      otherModules: json['otherModules'],
      financingDesired: json['financingDesired'],
      scaffoldingOption: ScaffoldingOption.values[json['scaffoldingOption']],
      furtherNotes: json['furtherNotes'],
      buildingPlansPath: json['buildingPlansPath'],
    );
  }
}

enum ScaffoldingOption {
  yes,
  no,
  customerProvides,
}

class ProjectPhoto {
  String id;
  String path;
  PhotoCategory category;
  String? comment;
  DateTime createdAt;

  ProjectPhoto({
    required this.id,
    required this.path,
    required this.category,
    this.comment,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'path': path,
      'category': category.index,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ProjectPhoto.fromJson(Map<String, dynamic> json) {
    return ProjectPhoto(
      id: json['id'],
      path: json['path'],
      category: PhotoCategory.values[json['category']],
      comment: json['comment'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

enum PhotoCategory {
  roof,
  meter,
  controlCabinet,
  obstacles,
  surroundings,
}

class ProjectDocument {
  String id;
  String path;
  String name;
  DocumentType type;
  String? comment;
  DateTime createdAt;

  ProjectDocument({
    required this.id,
    required this.path,
    required this.name,
    required this.type,
    this.comment,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'path': path,
      'name': name,
      'type': type.index,
      'comment': comment,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ProjectDocument.fromJson(Map<String, dynamic> json) {
    return ProjectDocument(
      id: json['id'],
      path: json['path'],
      name: json['name'],
      type: DocumentType.values[json['type']],
      comment: json['comment'],
      createdAt: DateTime.parse(json['createdAt']),
    );
  }
}

enum DocumentType {
  electricityBill,
  permit,
  buildingPlan,
  other,
}
