import 'package:get/get.dart';
import '../modules/login/bindings/login_binding.dart';
import '../modules/login/views/login_view.dart';
import '../modules/project_overview/bindings/project_overview_binding.dart';
import '../modules/project_overview/views/project_overview_view.dart';
import '../modules/customer_data/bindings/customer_data_binding.dart';
import '../modules/customer_data/views/customer_data_view.dart';
import '../modules/roof_data/bindings/roof_data_binding.dart';
import '../modules/roof_data/views/roof_data_view.dart';
import '../modules/electrical_data/bindings/electrical_data_binding.dart';
import '../modules/electrical_data/views/electrical_data_view.dart';
import '../modules/planning_options/bindings/planning_options_binding.dart';
import '../modules/planning_options/views/planning_options_view.dart';
import '../modules/photo_upload/bindings/photo_upload_binding.dart';
import '../modules/photo_upload/views/photo_upload_view.dart';
import '../modules/additional_comments/bindings/additional_comments_binding.dart';
import '../modules/additional_comments/views/additional_comments_view.dart';
import '../modules/confirmation/bindings/confirmation_binding.dart';
import '../modules/confirmation/views/confirmation_view.dart';

part 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.LOGIN;

  static final routes = [
    GetPage(
      name: _Paths.LOGIN,
      page: () => const LoginView(),
      binding: LoginBinding(),
    ),
    GetPage(
      name: _Paths.PROJECT_OVERVIEW,
      page: () => const ProjectOverviewView(),
      binding: ProjectOverviewBinding(),
    ),
    GetPage(
      name: _Paths.CUSTOMER_DATA,
      page: () => const CustomerDataView(),
      binding: CustomerDataBinding(),
    ),
    GetPage(
      name: _Paths.ROOF_DATA,
      page: () => const RoofDataView(),
      binding: RoofDataBinding(),
    ),
    GetPage(
      name: _Paths.ELECTRICAL_DATA,
      page: () => const ElectricalDataView(),
      binding: ElectricalDataBinding(),
    ),
    GetPage(
      name: _Paths.PLANNING_OPTIONS,
      page: () => const PlanningOptionsView(),
      binding: PlanningOptionsBinding(),
    ),
    GetPage(
      name: _Paths.PHOTO_UPLOAD,
      page: () => const PhotoUploadView(),
      binding: PhotoUploadBinding(),
    ),
    GetPage(
      name: _Paths.ADDITIONAL_COMMENTS,
      page: () => const AdditionalCommentsView(),
      binding: AdditionalCommentsBinding(),
    ),
    GetPage(
      name: _Paths.CONFIRMATION,
      page: () => const ConfirmationView(),
      binding: ConfirmationBinding(),
    ),
  ];
}
