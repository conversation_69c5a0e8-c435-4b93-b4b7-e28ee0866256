import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../controller/profile_controller.dart';

class EditProfilePhotosView extends StatefulWidget {
  const EditProfilePhotosView({super.key});

  @override
  State<EditProfilePhotosView> createState() => _EditProfilePhotosViewState();
}

class _EditProfilePhotosViewState extends State<EditProfilePhotosView> {
  late ProfileController authenticationController;  final int maxImages = 6;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    authenticationController = Get.find();

  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          centerTitle: true,
          automaticallyImplyLeading: true,
          elevation: 0,
          backgroundColor: Colors.white,
          title: Texts.textBold("My Photos", size: 18, color: Colors.black)),

      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child:  Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Grid of images
               GetBuilder<UserController>(
                 builder: (controller) {
                   return GridView.builder(
                          shrinkWrap: true,
                          itemCount: controller.userModel!.profileImages!.length + 1,
                          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            mainAxisSpacing: 10,
                            crossAxisSpacing: 10,
                            childAspectRatio: 1,
                          ),
                          itemBuilder: (context, index) {
                            if (index < controller.userModel!.profileImages!.length) {
                              return Stack( clipBehavior: Clip.none,
                                children: [
                                  ClipRRect(
                                    borderRadius: BorderRadius.circular(10),
                                    child: Widgets.networkImage(
                                      controller.userModel!.profileImages![index],
                                      width: double.infinity,
                                      height: double.infinity,

                                    ),
                                  ),
                                  Positioned(
                                    top: -6,
                                    right: -6,
                                    child: GestureDetector(
                                      onTap: () {
                                        authenticationController.deleteProfilePicture(controller.userModel!.profileImages![index],index);
                                      },
                                      child: CircleAvatar(
                                        radius: 15,
                                        backgroundColor: Colors.red,
                                        child: Icon(Icons.close, color: Colors.white, size: 16),
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            } else {
                              return GestureDetector(
                                onTap:() {
                                  showImageSourceDialog(context,index);
                                },
                                child: Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10),
                                    border: Border.all(color: Colors.black),
                                  ),
                                  child: const Center(
                                    child: Icon(Icons.add, size: 20, color: Colors.black),
                                  ),
                                ),
                              );
                            }
                          },
                        );
                 }
               )

            ],
          ),
        ),

    );
  }
  void showImageSourceDialog(BuildContext context,int index) {if (authenticationController.userController.userModel!.profileImages!.length >= maxImages) {
    Widgets.showSnackBar(
        "Maximum Images",
        "You can only upload up to $maxImages photos"
    );
    return;
  }
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          child: Container(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textBlock("Select an action",
                        align: TextAlign.center),
                    GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.clear,
                          color: Colors.black54,
                        ))
                  ],
                ),
                Widgets.heightSpaceH2,
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.camera_alt_outlined),
                  title: Text("Camera"),
                  onTap: () {
                    Get.back();
                    authenticationController.pickImage(ImageSource.camera,index);
                  },
                ),
                Divider(
                  color: Colors.black12,
                  thickness: .3,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.photo),
                  title: Text("Gallery"),
                  onTap: () {
                    Get.back();
                    authenticationController.pickImage(ImageSource.gallery,index);
                  },
                ),
                Divider(
                  color: Colors.black12,
                  thickness: .3,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.cancel_outlined),
                  title: Text("Cancel"),
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

}
