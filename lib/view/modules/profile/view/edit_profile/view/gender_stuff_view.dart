import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../../controller/profile_controller.dart';

class EditProfileGenderView extends StatefulWidget {
  const EditProfileGenderView({super.key});

  @override
  State<EditProfileGenderView> createState() => _EditProfileGenderViewState();
}

class _EditProfileGenderViewState extends State<EditProfileGenderView> {


  late ProfileController authenticationController;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    authenticationController = Get.find();

  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          centerTitle: true,
          automaticallyImplyLeading: true,
          elevation: 0,
          backgroundColor: Colors.white,
          title: Texts.textBold("Gender", size: 18, color: Colors.black)),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(15.0),
        child: CustomButton(
          label: "Update Changes",
          onTap: () {authenticationController.updateProfileApi();},
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(15.0),
        child:  Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Texts.textSemiBold("Which gender best describes you?",
                  size: 15,
                  maxline: 2,
                  align: TextAlign.start,
                  color: Colors.black),

              Widgets.heightSpaceH05,

              Obx(() => ListView.builder(
                  itemCount: authenticationController.genderSelection.length,
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    bool isSelected = authenticationController.genderSelected.value == index;

                    return Container(
                      margin: EdgeInsets.only(top: 10),
                      padding: EdgeInsets.only(left: 15, right: 15),
                      height: 54,
                      decoration: BoxDecoration(
                          color: isSelected?ColorConstants.lightPrimaryColor:ColorConstants.lightGrey,
                          borderRadius: BorderRadius.all(Radius.circular(12.0)),
                          border: Border.all(
                              color: isSelected ? ColorConstants.blackColor : Colors.white,
                              width: 1
                          )
                      ),
                      child: InkWell(
                        onTap: () {setState(() {});
                        authenticationController.genderSelected.value =index;authenticationController.genderValue.value = authenticationController.genderSelection[index];
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Texts.textBlock(
                              authenticationController.genderSelection[index],
                              fontWeight:  FontWeight.w500,
                              size: 14,
                            ),
                            SvgPicture.asset(
                                isSelected ? Assets.selectedItem : Assets.unselectedItem,
                                width: 20,
                                height: 20
                            )
                          ],
                        ),
                      ),
                    );
                  }
              )),

            ],
          ),
        ),

    );
  }
}
