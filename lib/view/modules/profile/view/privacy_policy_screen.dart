import 'package:banana/constants/my_images.dart';
import 'package:banana/constants/my_string.dart';
import 'package:banana/utils/custom_appbar.dart';
import 'package:flutter/material.dart';

import '../../../../core/constants/assets_constants.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {

  @override
  Widget build(BuildContext context) {
    final data=MediaQuery.of(context);
    return MediaQuery(
      data: data.copyWith(textScaler: const TextScaler.linear(1.0)),
      child: Scaffold(
        appBar: PreferredSize(preferredSize: Size.fromHeight(46), child: CustomAppbar(
          imageLeading:   Assets.backCircleIcon,
          title: MyString.privacyPolicy,
          onBackTap: () {
            Navigator.of(context).pop(true);
          },
        )),
        body:Container(),
      ),
    );
  }

}
