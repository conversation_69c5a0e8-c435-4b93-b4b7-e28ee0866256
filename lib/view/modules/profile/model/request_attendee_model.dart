class RequestAttende {
  int? attendeeId;
  int? eventId;
  int? noOfAttendees;
  String? status;
  User? user;

  RequestAttende(
      {this.attendeeId,
        this.eventId,
        this.noOfAttendees,
        this.status,
        this.user});

  RequestAttende.fromJson(Map<String, dynamic> json) {
    attendeeId = json['attendee_id'];
    eventId = json['event_id'];
    noOfAttendees = json['no_of_attendees'];
    status = json['status'];
    user = json['user'] != null ? new User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['attendee_id'] = this.attendeeId;
    data['event_id'] = this.eventId;
    data['no_of_attendees'] = this.noOfAttendees;
    data['status'] = this.status;
    if (this.user != null) {
      data['user'] = this.user!.toJson();
    }
    return data;
  }
}

class User {
  String? active;
  int? age;
  String? education;
  String? height;
  String? intention;
  String? interestedIn;
  String? latitude;
  String? longitude;
  String? name;
  String? notifArn;
  String? profileThumbnail;
  String? sex;
  String? spokenLanguages;
  String? status;
  String? thought;
  String? timezone;
  int? userId;

  User(
      {this.active,
        this.age,
        this.education,
        this.height,
        this.intention,
        this.interestedIn,
        this.latitude,
        this.longitude,
        this.name,
        this.notifArn,
        this.profileThumbnail,
        this.sex,
        this.spokenLanguages,
        this.status,
        this.thought,
        this.timezone,
        this.userId});

  User.fromJson(Map<String, dynamic> json) {
    active = json['active'];
    age = json['age'];
    education = json['education'];
    height = json['height'];
    intention = json['intention'];
    interestedIn = json['interested_in'];
    latitude = json['latitude'].toString();
    longitude = json['longitude'].toString();
    name = json['name'];
    notifArn = json['notif_arn'];
    profileThumbnail = json['profile_thumbnail'];
    sex = json['sex'];
    spokenLanguages = json['spoken_languages'];
    status = json['status'];
    thought = json['thought'];
    timezone = json['timezone'];
    userId = json['user_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['active'] = this.active;
    data['age'] = this.age;
    data['education'] = this.education;
    data['height'] = this.height;
    data['intention'] = this.intention;
    data['interested_in'] = this.interestedIn;
    data['latitude'] = this.latitude;
    data['longitude'] = this.longitude;
    data['name'] = this.name;
    data['notif_arn'] = this.notifArn;
    data['profile_thumbnail'] = this.profileThumbnail;
    data['sex'] = this.sex;
    data['spoken_languages'] = this.spokenLanguages;
    data['status'] = this.status;
    data['thought'] = this.thought;
    data['timezone'] = this.timezone;
    data['user_id'] = this.userId;
    return data;
  }
}
