import 'dart:convert';
import 'dart:io';
import 'package:banana/constants/text_error.dart';
import 'package:banana/core/constants/constants_list.dart' show Data;
import 'package:banana/core/widgets/custom_button.dart';
import 'package:banana/core/widgets/entry_field.dart';
import 'package:banana/response/ImageListResponse.dart';
import 'package:banana/utils/custom_appbar.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:banana/address/AddnewAddressLocationMap.dart';
import 'package:banana/constants/my_app_buttons.dart';
import 'package:banana/constants/my_colors.dart';
import 'package:banana/constants/my_fonts.dart';
import 'package:banana/constants/my_images.dart';
import 'package:banana/constants/my_string.dart';
import 'package:banana/constants/sized_box.dart';
import 'package:banana/utils/custom_text_field.dart';
import 'package:banana/utils/my_navigations.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../core/constants/assets_constants.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../controller/event_controller.dart';

class AddEventView extends StatefulWidget {
  const AddEventView({super.key});

  @override
  State<AddEventView> createState() => _AddEventViewState();
}

class _AddEventViewState extends State<AddEventView> {
  String postType = "Event";

  var selectedCategory = ["Event", "Event1"];
  final ImagePicker picker = ImagePicker();
  String? selectCategory;
  double? latitude;
  double? longitude;
String? apiFormatDate;
  bool nameErrorMsg = false;
  bool categoryErrorMsg = false;
  bool locationErrorMsg = false;
  bool dateErrorMsg = false;
  bool timeErrorMsg = false;
  bool durationErrorMsg = false;
  bool numOfAttendessErrorMsg = false;
  bool descriptionErrorMsg = false;
  bool isExceeded = false;

  String? errorWordsLimitMessage;
  late EventController eventController;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    eventController = Get.find();eventController.selectedImagesBase64.clear();
eventController.locationController.clear();eventController.nameController.clear();eventController.selectDateController.clear();eventController.selectTimeController.clear();eventController.durationController.clear();eventController.noOfAttendeesController.clear();eventController.enterDescriptionController.clear();
  }



  onCallBack(double? lat, double? long, String? address) {
    latitude = lat;
    longitude = long;

    debugPrint("latitude>>>$latitude");
    debugPrint("latitude>>>$longitude");
    debugPrint("address>>>$address");
    setState(() {});
  }

  void proceedWithEventCreation() {

        List<ImageListResponse> imageList =
            eventController.selectedImagesBase64.map((base64Image) {
          ImageListResponse imageListResponse = ImageListResponse();
          imageListResponse.content =
              base64Image; // Assign individual Base64 string
          imageListResponse.photoType = "event";
          imageListResponse.isPrimary = "True";
          return imageListResponse;
        }).toList();

            eventController.postCreateApi(
              context,
              eventController.nameController.text,
              eventController.locationController.text,
              apiFormatDate ?? "",
              eventController.selectTimeController.text,
              eventController.durationController.text,
              eventController.noOfAttendeesController.text,
              eventController.enterDescriptionController.text,
              imageList, // Pass the List<ImageListResponse>
              latitude,
              longitude,
              postType,
            );
          }




  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: () {
      FocusScope.of(context).unfocus();
    },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
            centerTitle: true,
            automaticallyImplyLeading: true,
            elevation: 0,
            backgroundColor: Colors.white,
            title:
                Texts.textBold("Create an Event", size: 18, color: Colors.black)),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(15.0),
            child: Column(
              children: [
                EntryField(
                  label: "Event Title",
                  hint: "Enter here",
                  textInputType: TextInputType.text,
                  textCapitalization: TextCapitalization.words,
                  controller: eventController.nameController,
                ),EntryField(
                  label: "Event Category",
                  hint: "Select category",
                  readOnly: true,
                  onTap: () {
                    showCategoryBottomSheet();
                  },
                  controller: eventController.categoryController,
                ),
                EntryField(onTap: () {

                },
                  label: "Event Location",trailingTitle: latitude==null?"Select Coordinates":"",
                  hint: "Enter here",readOnly: false,
                  suffixIcon: Icons.gps_fixed,onTrailingTap: () {
                    CustomNavigator.pushNavigate(
                        context,
                        SelectLocationView(
                          userLat: latitude,
                          userLng: longitude,
                          oncallback: onCallBack,
                        ));setState(() {});
                  },
                  controller: eventController.locationController,
                ),

                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child:
                      EntryField(onTap: () {
                        selectDate();
                      },
                        label: "Date",
                        hint: "MM/DD/YYYY",readOnly: true,
                        controller: eventController.selectDateController,

                      )
                    ),
                    SizedBox(width: 10,),
                    Expanded(
                      flex: 1,
                      child: EntryField(onTap: () {
                        selectTime(context);
                      },
                        label: "Time",
                        hint: "---:-- --",readOnly: true,
                        controller: eventController.selectTimeController,

                      )
                    ),
                  ],
                ),

                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child:
                      EntryField(
                        label: "Duration",
                        hint: "Enter here",
                        textInputType: TextInputType.number,
                        controller: eventController.durationController,
                      )
                    ),
                    SizedBox(width: 10,),
                    Expanded(
                      flex: 1,
                      child: EntryField(
                        label: "No. of Attendees (1 to 100)",
                        hint: "Enter here",
                        textInputType: TextInputType.number,
                        controller: eventController.noOfAttendeesController,
                      )
                    ),
                  ],
                ),
               EntryBigField(label: "Description",hint: "Enter here",maxLines: 5,minLines: 5,textCapitalization: TextCapitalization.sentences,
                 controller: eventController.enterDescriptionController,
                ),

                Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Event Images',
                            style: Theme.of(context).textTheme.titleSmall!.copyWith(
                                color: Colors.black,
                                fontSize: 12,fontFamily: "sansRegular"
                            ),
                          ),
                          SizedBox(height: 5),
            SizedBox(
              height: 100, // Ajuste para incluir la imagen y el botón
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: eventController.selectedImagesBase64.length +
                    (eventController.selectedImagesBase64.length < 5 ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index < eventController.selectedImagesBase64.length) {
                    final base64Image = eventController.selectedImagesBase64[index];

                    return Stack(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: 8.0),
                          child: ClipRRect(
                            borderRadius: BorderRadius.all(Radius.circular(12)),
                            child: Image.memory(
                              base64Decode(base64Image.replaceFirst(
                                  RegExp(r'data:image\/[a-zA-Z]+;base64,'), '')),
                              height: 100,
                              width: 100,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          right: 12,
                          top: 5,
                          child: InkWell(
                            onTap: () {
                              eventController.selectedImagesBase64.removeAt(index);
                              eventController.update();
                            },
                            child: SvgPicture.asset(
                              Assets.cancelredIcon,
                              height: 18,
                              width: 18,
                            ),
                          ),
                        ),
                      ],
                    );
                  } else {
                    // Botón de agregar imagen
                    return GestureDetector(
                      onTap: () {
                        showImageSourceDialog();
                      },
                      child:  Container(width: 100,height: 100,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(12)),
                          border: Border.all(color: Colors.black),
                        ),
                        child: const Center(
                          child: Icon(Icons.add, size: 20, color: Colors.black),
                        ),
                      ),
                    );
                  }
                },
              ),
            )

            ],
                      ),

                Widgets.heightSpaceH2,
                CustomButton(
                  label: "Create Event",
                  onTap: () {
                      FocusScope.of(context).unfocus();
                    proceedWithEventCreation();
                  },
                ),
                Widgets.heightSpaceH2,
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future selectDate() async {
    DateTime? pickedDate = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        firstDate: DateTime.now(),
        //DateTime.now() - not to allow to choose before today.
        lastDate: DateTime(2100));

    if (pickedDate != null) {
      debugPrint(
          "pickedDate>>>$pickedDate"); //pickedDate output format => 2021-03-10 00:00:00.000
      // String formattedDate = DateFormat('yyyy-MM-dd').format(pickedDate);
     apiFormatDate = DateFormat('dd/MM/yyyy').format(pickedDate);

      String formattedDate = DateFormat('MM/dd/yyyy').format(pickedDate);
      debugPrint(
          "formattedDate>>>$formattedDate"); //formatted date output using intl package =>  2021-03-16
      setState(() {
        eventController.selectDateController.text =
            formattedDate; //set output date to TextField value.
      });
    } else {}
  }

  Future<void> selectTime(BuildContext context) async {
    final TimeOfDay? time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (time != null) {
      final String formattedTime = formatTime24Hour(time);
      eventController.selectTimeController.text = formattedTime;
    }
  }

  String formatTime24Hour(TimeOfDay time) {
    final String hour =
        time.hour.toString().padLeft(2, '0'); // Ensure two-digit hour
    final String minute = time.minute.toString().padLeft(2, '0'); // Ensur
    // e two-digit minutes
    return "$hour:$minute";
  }

  void showImageSourceDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          child: Container(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textBlock("Select an action",
                        align: TextAlign.center),
                    GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.clear,
                          color: Colors.black54,
                        ))
                  ],
                ),
                Widgets.heightSpaceH2,
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.camera_alt_outlined),
                  title: Text("Camera"),
                  onTap: () {
                    Get.back();
                    pickImage(ImageSource.camera);
                  },
                ),
                Divider(
                  color: Colors.black12,
                  thickness: .3,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.photo),
                  title: Text("Gallery"),
                  onTap: () {
                    Get.back();
                    pickImage(ImageSource.gallery);
                  },
                ),
                Divider(
                  color: Colors.black12,
                  thickness: .3,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.cancel_outlined),
                  title: Text("Cancel"),
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> pickImage(ImageSource source) async {
    if (eventController.selectedImagesBase64.length >= 5) {
      Widgets.showSnackBar("Alert", "You can only upload up to 5 images");
      return;
    }

    final XFile? image = await picker.pickImage(source: source);
    if (image != null) {
      final File imageFile = File(image.path);
      final bytes = imageFile.readAsBytesSync();
      final compressedBytes = await FlutterImageCompress.compressWithList(
        bytes,
        quality: 50,
      );
      final String base64String =
          "data:image/jpeg;base64,${base64Encode(compressedBytes)}";
      setState(() {
        eventController.selectedImagesBase64.add(base64String);
      });
    }
  }

  void showCategoryBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(20),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBold("Select Category", size: 18),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              Divider(),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: Data.eventCategories.length,
                  itemBuilder: (context, index) {
                    final category = Data.eventCategories[index];
                    return ListTile(
                      title: Texts.textMedium(category),
                      onTap: () {
                        setState(() {
                          eventController.categoryController.text = category;
                          selectCategory = category;
                        });
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

class WordLimitFormatter extends TextInputFormatter {
  final int maxWords;
  final void Function(bool) onLimitExceeded;

  WordLimitFormatter(this.maxWords, this.onLimitExceeded);

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final wordCount = newValue.text
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .length;

    if (wordCount > maxWords) {
      onLimitExceeded(true);
      return oldValue; // Prevent further input if word limit exceeded
    }

    onLimitExceeded(false);
    return newValue; // Allow input if within the limit
  }
}
