import 'package:banana/api_services/WebServices.dart';
import 'package:banana/core/constants/constants_list.dart' show Data;
import 'package:banana/response/GetEventTrsytDetailsResponse.dart';
import 'package:banana/response/GetHomeDataResponse.dart';
import 'package:flutter/material.dart';
import 'package:banana/utils/custom_appbar.dart';
import 'dart:convert';
import 'dart:io';
import 'package:banana/constants/text_error.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:banana/address/AddnewAddressLocationMap.dart';
import 'package:banana/constants/my_app_buttons.dart';
import 'package:banana/constants/my_colors.dart';
import 'package:banana/constants/my_fonts.dart';
import 'package:banana/constants/my_images.dart';
import 'package:banana/constants/my_string.dart';
import 'package:banana/constants/sized_box.dart';
import 'package:banana/utils/custom_text_field.dart';
import 'package:banana/utils/my_navigations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../controller/user_controller.dart';
import '../../../../core/constants/api_endpoints.dart';
import '../../../../core/constants/assets_constants.dart';
import '../../../../core/constants/color_constants.dart';
import '../../../../core/services/http_service.dart';
import '../../../../core/utils/utils.dart';
import '../../../../core/widgets/custom_button.dart';
import '../../../../core/widgets/entry_field.dart';
import '../../../../core/widgets/text_widgets.dart';
import '../../../../core/widgets/widgets.dart';
import '../../home/<USER>/event_detail_model.dart';
import '../controller/event_controller.dart';
import 'add_event_view.dart';

class EventEditView extends StatefulWidget {
  final EventsCard? event;
  const EventEditView({super.key, this.event});

  @override
  State<EventEditView> createState() => _EventEditViewState();
}

class _EventEditViewState extends State<EventEditView> {
  TextEditingController nameController = TextEditingController();
  TextEditingController locationController = TextEditingController();
  TextEditingController selectDateController = TextEditingController();
  TextEditingController selectTimeController = TextEditingController();
  TextEditingController durationController = TextEditingController();
  TextEditingController enterDescriptionController = TextEditingController();
  TextEditingController noOfAttendeesController = TextEditingController();
  late EventController eventController;

  String postType = "Event";
  void showCategoryBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.all(20),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.7,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBold("Select Category", size: 18),
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              Divider(),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: Data.eventCategories.length,
                  itemBuilder: (context, index) {
                    final category = Data.eventCategories[index];
                    return ListTile(
                      title: Texts.textMedium(category),
                      onTap: () {
                        setState(() {
                          eventController.categoryController.text = category;
                          selectCategory = category;
                        });
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  var categroySelect = ["Event", "Event1"];

  String? selectCategory;
  double? latitude;
  double? longitude;

  bool nameErrorMsg = false;
  bool categoryErrorMsg = false;
  bool locationErrorMsg = false;
  bool dateErrorMsg = false;
  bool timeErrorMsg = false;
  bool durationErrorMsg = false;
  bool numOfAttendessErrorMsg = false;
  bool descriptionErrorMsg = false;
  bool isExceeded = false;
String? apiFormatDate;
  String? errorWordsLimitMessage;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    eventController = Get.find();eventController.eventDetail.value = EventDetail();
    eventController.fetchEventTrystDetailsApiBackground(widget.event?.eventId ?? "");

    getInfo();
    eventController.selectedImagesBase64.clear();
  }

  getInfo() async {
    eventController.nameController.text = widget.event?.name ?? "";
    eventController.locationController.text = widget.event?.locationName ?? "";
    eventController.selectDateController.text = widget.event?.startDate ?? "";
    eventController.selectTimeController.text = widget.event?.startHour ?? "";
    eventController.durationController.text = widget.event?.duration ?? "";
  eventController.noOfAttendeesController.text = widget.event?.noOfAttendees ?? "";
  eventController.categoryController.text = widget.event?.eventCategory ?? "";
    String startDate = widget.event?.startDate ?? "";
    if (startDate.isNotEmpty) {
      try {
        // Parse the incoming date (assuming it's in DD/MM/YYYY format)
        List<String> dateParts = startDate.split('/');
        if (dateParts.length == 3) {
          // Rearrange to MM/DD/YYYY format
          String formattedDate = "${dateParts[0]}/${dateParts[1]}/${dateParts[2]}";
          eventController.selectDateController.text = formattedDate;
          apiFormatDate = formattedDate; // Store API format for later use
        } else {
          eventController.selectDateController.text = startDate; // Fallback to original
        }
      } catch (e) {
        print("Date format error: $e");
        eventController.selectDateController.text = startDate; // Fallback to original
      }
    } else {
      eventController.selectDateController.text = "";
    }
    print(widget.event?.duration ?? "");
    latitude = double.parse(widget.event?.latitude ?? "");
    longitude = double.parse(widget.event?.longitude ?? "");
    eventController.enterDescriptionController.text = widget.event?.description ?? "";

    setState(() {});
  }

  onCallBack(double? lat, double? long, String? address) {
    latitude = lat;
    longitude = long;

    debugPrint("latitude>>>$latitude");
    debugPrint("latitude>>>$longitude");
    debugPrint("address>>>$address");
    setState(() {});
  }

  void isChechkPostEvents(BuildContext context) {
    setState(() {
      nameErrorMsg = nameController.text.trim().isEmpty;
      //  categoryErrorMsg =nameController.text.isEmpty;
      locationErrorMsg = locationController.text.trim().isEmpty;
      dateErrorMsg = selectDateController.text.trim().isEmpty;
      timeErrorMsg = selectTimeController.text.trim().isEmpty;
      durationErrorMsg = durationController.text.trim().isEmpty;
      numOfAttendessErrorMsg = noOfAttendeesController.text.trim().isEmpty;
      descriptionErrorMsg = enterDescriptionController.text.trim().isEmpty;

      bool isPostEvent = !nameErrorMsg &&
          !locationErrorMsg &&
          !dateErrorMsg &&
          !timeErrorMsg &&
          !durationErrorMsg &&
          !numOfAttendessErrorMsg &&
          !descriptionErrorMsg;

      if (isPostEvent) {
        eventController.editEventApi(
            context,
            nameController.text,
            locationController.text,
            apiFormatDate ?? "",
            selectTimeController.text,
            durationController.text,
            noOfAttendeesController.text,
            enterDescriptionController.text,
            latitude,
            longitude,
            postType,
            widget.event?.eventId ?? "");
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: () {
      FocusScope.of(context).unfocus();
    },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
            centerTitle: true,
            automaticallyImplyLeading: true,
            elevation: 0,
            backgroundColor: Colors.white,actions: [

              IconButton(onPressed: (){

                eventController.deleteEventSuccessDialog(
                    context, widget.event?.eventId ?? "");
              }, icon: Icon(Icons.delete,color: Colors.red)),


        ],
            title: Texts.textBold("Edit Event", size: 18, color: Colors.black)),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              EntryField(readOnly: true,
                label: "Event Title",
                hint: "Enter here",
                textInputType: TextInputType.text,
                textCapitalization: TextCapitalization.words,
                controller: eventController.nameController,
              ),EntryField(
        label: "Event Category",
        hint: "Select category",
        readOnly: true,
        onTap: () {
      // showCategoryBottomSheet(context);
      },
        controller: eventController.categoryController,
      ),
              EntryField(


                onTap: () {

              },
                label: "Event Location",
                hint: "Enter here",readOnly:true,
                suffixIcon: Icons.gps_fixed,onTrailingTap: () {
                  // CustomNavigator.pushNavigate(
                  //     context,
                  //     SelectLocationView(
                  //       userLat: latitude,
                  //       userLng: longitude,
                  //       oncallback: onCallBack,
                  //     ));
                },
                controller: eventController.locationController,
              ),

              Row(
                children: [
                  Expanded(
                      flex: 1,
                      child: EntryField(
                        onTap: () {
                          // selectDate(context);
                        },
                        label: "Date",
                        hint: "MM/DD/YYYY",
                        readOnly: true,
                        controller: eventController.selectDateController,
                      )),
                  SizedBox(
                    width: 10,
                  ),
                  Expanded(
                      flex: 1,
                      child: EntryField(
                        onTap: () {
                          // selectTime(context);
                        },
                        label: "Time",
                        hint: "---:-- --",
                        readOnly: true,
                        controller: eventController.selectTimeController,
                      )),
                ],
              ),
              Row(
                children: [
                  Expanded(
                      flex: 1,
                      child:
                      EntryField(readOnly: true,
                        label: "Duration",
                        hint: "Enter here",
                        textInputType: TextInputType.number,
                        controller: eventController.durationController,
                      )
                  ),
                  SizedBox(width: 10,),
                  Expanded(
                      flex: 1,
                      child: EntryField(readOnly: true,
                        label: "No. of Attendees (1 to 100)",
                        hint: "Enter here",
                        textInputType: TextInputType.number,
                        controller: eventController.noOfAttendeesController,
                      )
                  ),
                ],
              ),
              EntryBigField(
                label: "Description",
                hint: "Enter here",
                maxLines: 5,
                minLines: 5,
                textCapitalization: TextCapitalization.sentences,
                controller: eventController.enterDescriptionController,
              ),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Event Images',
                    style: Theme.of(context).textTheme.titleSmall!.copyWith(
                        color: Colors.black,
                        fontSize: 12,
                        fontFamily: "sansRegular"),
                  ),
                  SizedBox(height: 5),
                  Obx(() {
                      return GridView.builder(shrinkWrap: true,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                          childAspectRatio: 1,
                        ),
                        itemCount: (eventController.eventDetail.value.images?.length ?? 0) +1,
                        itemBuilder: (context, index) {
                          // Show existing event images first
                          if (index < (eventController.eventDetail.value.images?.length ?? 0)) {
                            return Stack(
                              clipBehavior: Clip.none,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.network(
                                  Utils.cleanString( eventController.eventDetail.value.images![index]),
                                    height: double.infinity,
                                    width: double.infinity,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Positioned(
                                  right: -6,
                                  top: -6,
                                  child: GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        eventController.eventDetail.value.images?.removeAt(index);
                                      });
                                    },
                                    child: CircleAvatar(
                                      radius: 12,
                                      backgroundColor: Colors.red,
                                      child: Icon(Icons.close, color: Colors.white, size: 14),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          }

                          else {
                            return GestureDetector(
                              onTap: () {
                                showImageSourceDialog(context);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.black),
                                ),
                                child: const Center(
                                  child: Icon(Icons.add, size: 24, color: Colors.black),
                                ),
                              ),
                            );
                          }
                        },
                      );
                    }
                  )
                ],
              ),
              Widgets.heightSpaceH2,
              CustomButton(
                label:"Update Event",
                onTap: () {
                  eventController.editEventApi(
                      context,
                      eventController.nameController.text,
                      eventController.locationController.text,
                      apiFormatDate ?? "",
                      eventController.selectTimeController.text,
                      eventController.durationController.text,
                      eventController.noOfAttendeesController.text,
                      eventController. enterDescriptionController.text,
                      latitude,
                      longitude,
                      postType,
                      widget.event?.eventId ?? "");
                },
              ),

              Widgets.heightSpaceH2,
            ],
          ),
        ),
      ),
    );
  }

  Future selectDate(BuildContext context) async {
    DateTime? pickedDate = await showDatePicker(
        context: context,
        initialDate: DateTime.now(),
        firstDate: DateTime(1950),
        //DateTime.now() - not to allow to choose before today.
        lastDate: DateTime(2100));

    if (pickedDate != null) {
      debugPrint(
          "pickedDate>>>$pickedDate"); //pickedDate output format => 2021-03-10 00:00:00.000
      apiFormatDate = DateFormat('dd/MM/yyyy').format(pickedDate);

      String formattedDate = DateFormat('MM/dd/yyyy').format(pickedDate);
      debugPrint(
          "formattedDate>>>$formattedDate"); //formatted date output using intl package =>  2021-03-16
      setState(() {
        eventController.selectDateController.text =
            formattedDate; //set output date to TextField value.
      });
    } else {}
  }

  @override
  void dispose() {
    selectTimeController.dispose();
    super.dispose();
  }

  Future<void> selectTime(BuildContext context) async {
    final TimeOfDay? time = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (time != null) {
      // Custom format for time with AM/PM
      final String formattedTime = _formatTimeWithAmPm(time);
      selectTimeController.text = formattedTime;
    }
  }

  String _formatTimeWithAmPm(TimeOfDay time) {
    final int hour = time.hourOfPeriod == 0
        ? 12
        : time.hourOfPeriod; // Adjust for 12-hour format
    final String period = time.period == DayPeriod.am ? "AM" : "PM";
    final String minute =
        time.minute.toString().padLeft(2, '0'); // Ensure two-digit minutes
    return "$hour:$minute $period";
  }

  final ImagePicker picker = ImagePicker();



  void showImageSourceDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
          child: Container(
            padding: EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textBlock("Select an action",
                        align: TextAlign.center),
                    GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Icon(
                          Icons.clear,
                          color: Colors.black54,
                        ))
                  ],
                ),
                Widgets.heightSpaceH2,
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.camera_alt_outlined),
                  title: Text("Camera"),
                  onTap: () {
                    Get.back();
                    pickImage(ImageSource.camera);
                  },
                ),
                Divider(
                  color: Colors.black12,
                  thickness: .3,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.photo),
                  title: Text("Gallery"),
                  onTap: () {
                    Get.back();
                    pickImage(ImageSource.gallery);
                  },
                ),
                Divider(
                  color: Colors.black12,
                  thickness: .3,
                ),
                ListTile(
                  dense: true,
                  contentPadding: EdgeInsets.symmetric(horizontal: 2),
                  leading: Icon(Icons.cancel_outlined),
                  title: Text("Cancel"),
                  onTap: () {
                    Navigator.pop(context);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );

  }

  Future<void> pickImage(ImageSource source) async {
    final pickedFile = await picker.pickImage(source: source);
    if (pickedFile != null) {
      // Show cropping option
      File originalFile = File(pickedFile.path);

        File? compressedFile = await compressImage(File(originalFile.path));
        List<int> imageBytes = await compressedFile!.readAsBytes();
        String base64String = base64Encode(imageBytes);
        var request = {
          "fileName": "image",
          "photo_type": "event",	"is_primary": "False",
          "event_id": widget.event?.eventId ?? "",

          "content": "data:image/jpeg;base64,$base64String",
        };
        uploadProfilePicture(request);
      }
    }



  Future<File?> compressImage(File file) async {
    final dir = await getTemporaryDirectory();
    final targetPath = '${dir.path}/${basename(file.path)}_compressed_${Utils.generateUniqueNumber()}.jpg';

    var result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: 40, // 50% compression
    );

    return result != null ? File(result.path) : null;
  }
  uploadProfilePicture(var data) async {
    try {


      Widgets.showLoader("Updating profile photos..");

      Map<String, dynamic> request = {

        "images":[ data], "access_token": Get.find<UserController>().accessToken.value,
      };


      var response = await ApiService.postData(
        Endpoints.updateProfilePicture,
        request,
      );

      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar(
            "Success", "Event photo uploaded successfully");
eventController.fetchEventTrystDetailsApiBackground(widget.event?.eventId ?? "");

      } else {
        Widgets.showSnackBar("Error", response.message ?? "");return false;
      }
    } catch (exception) {
      debugPrint("exception>>>$exception");return false;
    } finally {
      Widgets.hideLoader();
    }
  }
  deleteProfilePicture(String url,int index) async {
    try {


      Widgets.showLoader("Deleting profile photo..");

      Map<String, dynamic> request = {
        "photo_url":url, "access_token": Get.find<UserController>().accessToken.value,
      };


      var response = await ApiService.postData(
        Endpoints.deleteProfilePicture,
        request,
      );

      Widgets.hideLoader();
      if (response.status == true) {

      } else {
        Widgets.showSnackBar("Error", response.message ?? "");return false;
      }
    } catch (exception) {
      debugPrint("exception>>>$exception");return false;
    } finally {
      Widgets.hideLoader();
    }
  }
}
