
import 'package:banana/api_services/WebServices.dart';
import 'package:banana/constants/my_colors.dart';
import 'package:banana/constants/my_fonts.dart';
import 'package:banana/constants/my_images.dart';
import 'package:banana/constants/my_string.dart';
import 'package:banana/constants/sized_box.dart';
import 'package:banana/view/modules/event/view/edit_event_view.dart';
import 'package:banana/response/GetAttendenceRequesResponse.dart';
import 'package:banana/utils/custom_appbar.dart';
import 'package:banana/utils/my_navigations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/constants/assets_constants.dart';

class EventRequestView extends StatefulWidget {
  const EventRequestView({super.key});

  @override
  State<EventRequestView> createState() => _EventRequestViewState();
}

class _EventRequestViewState extends State<EventRequestView> {

  String typeButton=MyString.accepted;
  GetAttendenceRequesResponse getAttendenceReqModel=  GetAttendenceRequesResponse();

  List<EventsAsOrganizerReq>listRequesEvent=<EventsAsOrganizerReq>[];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();


  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: MyColors.colorWhite,
      appBar: PreferredSize(
          preferredSize: Size.fromHeight(50),
          child: CustomAppbar(
            title: MyString.eventRequest,
            statusBarColor: MyColors.colorWhite,

            imageLeading:   Assets.backCircleIcon,
            onBackTap: () {
              Navigator.of(context).pop(true);
            },
            onTrailingTap: () {

            },
            trailingWidget:GestureDetector(onTap: (){
              showMenuDialog(context);            },
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Icon(Icons.more_vert_rounded),
              ),
            ),

          )),
      body: SingleChildScrollView(
        child: Container(
          margin: EdgeInsets.only(left: 14, right: 14, top: 26),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [

              Text("City Lights Sonderline Music Festival", style: TextStyle(
                  color: MyColors.color201F17,
                  fontWeight: FontWeight.w400,
                  fontSize: 25,
                  fontFamily: MyFonts.robotoRegular),),
              hSized18,
              Row(
                children: [
                  Text("12 Sept 2024  /  8:00 PM", style: TextStyle(
                      color: MyColors.color201F17,
                      fontWeight: FontWeight.w400,
                      fontSize: 17,
                      fontFamily: MyFonts.robotoRegular),),
                  Container(
                    margin: EdgeInsets.only(left: 10, right: 11, top: 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SvgPicture.asset(  Assets.clockIcon),
                        wSized5,
                        Text(
                          "2hrs",
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            fontSize: 17,
                            color: MyColors.color201F17,
                            fontWeight: FontWeight.w400,
                            fontFamily: MyFonts.robotoMedium,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              Container(
                padding: EdgeInsets.symmetric(vertical: 30,),
                margin: EdgeInsets.only(left: 15,right: 15,top:150 ),
                alignment: Alignment.center,
                decoration: BoxDecoration(
                  color: MyColors.color201F174,
                  borderRadius: BorderRadius.all(Radius.circular(20.0)),),
                child: Column(
                  children: [

                    SvgPicture.asset(  Assets.profileIcon),
                    hSized10,
                    Text(MyString.noRequestFound,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                      fontSize: 15,

                      color: MyColors.color201F17,
                      fontWeight: FontWeight.w500,
                      fontFamily: MyFonts.robotoMedium,
                    ),),
                    hSized10,
                    Text(
                      textAlign: TextAlign.center,
                      MyString.stayTuned,  style: TextStyle(
                      fontSize: 13,
                      color: MyColors.color201F17,
                      fontWeight: FontWeight.w400,
                      fontFamily: MyFonts.robotoMedium,
                    ),),

                  ],
                ),
              )


              /*ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: 4,
                  physics: AlwaysScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        Container(
                            alignment: Alignment.center,
                            margin: EdgeInsets.only(left: 0, right: 0, top: 20),
                            padding: EdgeInsets.only(
                                left: 0, right: 0, top: 10, bottom: 0),

                            decoration: BoxDecoration(
                              //  color: MyColors.colorWhite,
                              //borderRadius: BorderRadius.all(Radius.circular(15.0))
                            ),
                            child: Column(
                              children: [

                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    CircleAvatar(
                                        radius: 30,
                                        backgroundColor: Colors.transparent,
                                        child: ClipOval(
                                            child: Image.asset(
                                              MyAssetsImages.girlImg,
                                              width: 60,
                                              height: 60,
                                              fit: BoxFit.cover,
                                            ))),
                                    wSized15,
                                    Column(
                                      mainAxisAlignment: MainAxisAlignment
                                          .start,
                                      crossAxisAlignment: CrossAxisAlignment
                                          .start,
                                      children: [
                                        Text(
                                          "Jenny Doe, 20",
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 16,
                                            color: MyColors.color201F17,
                                            fontWeight: FontWeight.w500,
                                            fontFamily: MyFonts.robotoMedium,
                                          ),
                                        ),
                                        Text(
                                          "New York, 2 miles away",
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: MyColors.color201F17,
                                            fontWeight: FontWeight.w400,
                                            fontFamily: MyFonts.robotoRegular,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                hSized10,
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    MyAppButtonWithIcons(
                                      imageSocial: MyAssetsIcons.acceptIcon,
                                      btnWidth: 140,
                                      btnHeight: 35,
                                      borderRadius: 35.0,
                                      buttonColor: typeButton == "Accepted"
                                          ? MyColors.colorPrimary
                                          : MyColors.colorWhite,
                                      borderColor: typeButton == "Accepted"
                                          ? MyColors.colorPrimary
                                          : MyColors.color201F17,
                                      onPressed: () {
                                        typeButton = MyString.accepted;
                                        setState(() {});
                                      },
                                      name: MyString.accepted,
                                      textStyle: TextStyle(
                                          color: MyColors.colorBlack,
                                          fontSize: 13,
                                          fontFamily: MyFonts.robotoRegular,
                                          fontWeight: FontWeight.w400),
                                      // borderRadius:  BorderRadius.all(Radius.circular(radius)),
                                    ),
                                    wSized15,
                                    MyAppButtonWithIcons(
                                      imageSocial: MyAssetsIcons.declineIcon,
                                      btnWidth: 140,

                                      btnHeight: 35,
                                      borderRadius: 35.0,
                                      onPressed: () {
                                        typeButton = MyString.decline;
                                        setState(() {});
                                      },
                                      name: MyString.decline,
                                      buttonColor: typeButton == "Decline"
                                          ? MyColors.colorPrimary
                                          : MyColors.colorWhite,
                                      borderColor: typeButton == "Decline"
                                          ? MyColors.colorPrimary
                                          : MyColors.color201F17,

                                      textStyle: TextStyle(
                                          color: MyColors.colorBlack,
                                          fontSize: 13,
                                          fontFamily: MyFonts.robotoRegular,
                                          fontWeight: FontWeight.w400),
                                      // borderRadius:  BorderRadius.all(Radius.circular(radius)),
                                    ),
                                  ],
                                ),
                                hSized16,
                                Container(width: MediaQuery
                                    .of(context)
                                    .size
                                    .width,
                                  color: MyColors.color201F1720,
                                  height: 1,)

                              ],
                            ))
                      ],
                    );
                  })*/
            ],
          ),
        ),
      ),
    );
  }

  void showMenuDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierColor: Colors.black54,
      barrierDismissible: true,
      useSafeArea: true,

      builder: (BuildContext context) {
        return Align(
          alignment: Alignment.topRight,
          child: Container(
            decoration: BoxDecoration(
                color: MyColors.colorWhite,
                borderRadius: BorderRadius.all(Radius.circular(15.0))
            ),
            margin: EdgeInsets.only(right: 30, top: 50),


            child: Material(
              color: Colors.white,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15.0)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [

                  SizedBox(height: 10,),

                  InkWell(
                    onTap: (){
                      Navigator.of(context).pop();
                      CustomNavigator.pushNavigate(context, EventEditView());

                    },
                    child: Container(

                        margin: EdgeInsets.only(
                            left: 0, right: 0, top: 7, bottom: 4),
                        child: Text(MyString.edit,
                          style: TextStyle(color: MyColors.color201F17,
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              fontFamily: MyFonts.robotoRegular),)),
                  ),
                  SizedBox(height: 10,),
                  Container(
                    height: 1,
                    width: 130,
                    decoration: BoxDecoration(
                        color: MyColors.colorD8DBEE
                    ),
                  ),
                  SizedBox(height: 10,),
                  InkWell(
                    onTap: (){
                      Navigator.of(context).pop();

                    },
                    child: Container(

                        margin: EdgeInsets.only(
                            left: 0, right: 0, top: 7, bottom: 4),
                        child: Text(MyString.delete,
                          style: TextStyle(color: MyColors.colorF24A4A,
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              fontFamily: MyFonts.robotoRegular),)),
                  ),
                  SizedBox(height: 10,),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
