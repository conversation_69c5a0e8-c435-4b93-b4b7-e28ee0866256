import 'dart:ffi';

import 'package:banana/core/constants/color_constants.dart';
import 'package:banana/core/widgets/custom_button.dart';
import 'package:banana/view/modules/home/<USER>/home_controller.dart';
import 'package:banana/view/modules/home/<USER>/notifications_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../../../core/widgets/entry_field.dart';
import '../../../../../core/widgets/text_widgets.dart';

class NoteDialog {
  static void showNoteDialog(BuildContext context,{bool? isNotify,Notifications? noti}) {
    final HomeController controller = Get.find<HomeController>();
    TextEditingController textController =
    TextEditingController(text: controller.note.value);

    Get.dialog(
      AlertDialog(backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        title: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Texts.textBold("Add a note", size: 20, color: Colors.black),
            InkWell(
              child: Icon(Icons.close,size: 20,color: Colors.black),
              onTap: () => Get.back(),
            ),
          ],
        ),
        content: Column(crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            EntryBigField(
              controller: textController,
              maxLength: 30,minLines: 5,maxLines: 5,
              hint: "What's on your mind?",
            ),
            SizedBox(height: 10),
            Obx(() {
              if (controller.note.isEmpty) {
                return CustomButton(label: "Submit",
                  onTap: () => controller.submitNote(textController.text),
                );
              } else {
                return Row(
                  children: [
                    Expanded(
                      child:CustomButton(label: "Delete Note",backgroundColor: ColorConstants.redColor,textColor: Colors.white,
                  onTap: () =>controller.deleteNote(),fontSize: 10,padding: 10)
                ),
                    SizedBox(width: 5),
                    Expanded(
                      child:CustomButton(label: "Save Changes",
              onTap: () =>
              controller.updateNote(textController.text,isNotify:isNotify,notification: noti),fontSize: 10,padding: 10
              ))]

                );
              }
            }),
          ],
        ),
      ),
    );
  }
}