class OrganizerDetail {
  int? userId;
  String? name;
  String? userType;
  String? profileThumbnail;
  String? whoWeAre;
  String? notifArn;
  String? status;
  String? active;
  bool? isFollowed;
  num? noOfFollowers;
  num? distance;

  OrganizerDetail(
      {this.userId,
        this.name,
        this.userType,
        this.profileThumbnail,
        this.whoWeAre,
        this.notifArn,
        this.status,
        this.active,
        this.isFollowed,
        this.noOfFollowers,
        this.distance});

  OrganizerDetail.fromJson(Map<String, dynamic> json) {
    userId = json['user_id'];
    name = json['name'];
    userType = json['user_type'];
    profileThumbnail = json['profile_thumbnail'];
    whoWeAre = json['who_we_are'];
    notifArn = json['notif_arn'];
    status = json['status'];
    active = json['active'];
    isFollowed = json['is_followed'];
    noOfFollowers = json['no_of_followers']??0;
    distance = json['distance']??0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this.userId;
    data['name'] = this.name;
    data['user_type'] = this.userType;
    data['profile_thumbnail'] = this.profileThumbnail;
    data['who_we_are'] = this.whoWeAre;
    data['notif_arn'] = this.notifArn;
    data['status'] = this.status;
    data['active'] = this.active;
    data['is_followed'] = this.isFollowed;
    data['no_of_followers'] = this.noOfFollowers;
    data['distance'] = this.distance;
    return data;
  }
}
