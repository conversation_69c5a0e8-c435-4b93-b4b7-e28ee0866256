import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'dart:async';

import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/padding_constants.dart';
import '../../../core/widgets/custom_button.dart';

import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';

import '../controller/authentication_controller.dart';




class PhoneVerificationView extends StatefulWidget {
  @override
  State<PhoneVerificationView> createState() => _PhoneVerificationViewState();
}

class _PhoneVerificationViewState extends State<PhoneVerificationView> {
  TextEditingController otpController = TextEditingController();
  AuthenticationController authenticationController = Get.find();
  bool canResend = false;
  int timeLeft = 1800; // 30 minutes in seconds
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    startTimer();
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  String formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  void startTimer() {
    setState(() {
      canResend = false;
      timeLeft = 1800; // 30 minutes in seconds
    });

    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (timeLeft > 0) {
        setState(() {
          timeLeft--;
        });
      } else {
        setState(() {
          canResend = true;
          timer.cancel();
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder(
      init: authenticationController,
      builder: (_) {
        return Scaffold(
          backgroundColor: ColorConstants.backgroundColor,
          appBar: AppBar(
            backgroundColor: ColorConstants.backgroundColor,
            centerTitle: false,
            automaticallyImplyLeading: false,
            leading: IconButton(
              icon: Icon(Icons.arrow_back_ios_new_outlined),
              onPressed: () => Get.back(),
            ),
          ),
          body: SafeArea(
            child: Padding(
              padding: PaddingConstants.screenPaddingHalf,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Texts.textBold(
                    "verify_your_phone".tr,
                    color: ColorConstants.blackColor,
                    size: 24,
                    textAlign: TextAlign.start
                  ),
                  Widgets.heightSpaceH1,
                  Texts.textNormal(
                    "verify_phone_description".tr,
                    color: ColorConstants.textColor,
                    size: 11,
                    textAlign: TextAlign.start,
                  ),
                  Widgets.heightSpaceH5,
                  PinCodeTextField(
                    controller: otpController,
                    appContext: context,
                    length: 6,
                    autoDisposeControllers: true,
                    animationType: AnimationType.fade,
                    textStyle: const TextStyle(
                      color: Colors.black,
                      fontFamily: "InstrumentSansRegular"
                    ),
                    pinTheme: PinTheme(
                      fieldWidth: .12.sw,
                      shape: PinCodeFieldShape.box,
                      borderRadius: BorderRadius.circular(10),
                      borderWidth: .5,
                      selectedBorderWidth: .8,
                      activeBorderWidth: .5,
                      activeFillColor: ColorConstants.greyColor,
                      inactiveFillColor: ColorConstants.greyColor,
                      inactiveBorderWidth: .5,
                      selectedColor: ColorConstants.fullBlackColor,
                      activeColor: Colors.black12.withOpacity(.09),
                      selectedFillColor: ColorConstants.greyColor,
                      inactiveColor: Colors.grey.shade300
                    ),
                    cursorColor: ColorConstants.fullBlackColor,
                    animationDuration: Duration(milliseconds: 300),
                    enableActiveFill: true,
                    keyboardType: TextInputType.number,
                    onCompleted: (v) {},
                    onChanged: (value) {},
                    beforeTextPaste: (text) => true,
                  ),
                  Widgets.heightSpaceH2,
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Texts.textNormal(
                        "didnt_receive_otp".tr,
                        color: Colors.black45,
                        size: 14
                      ),
                      GestureDetector(
                        onTap: canResend ? () {
                          authenticationController.resendMobileOtp();
                          startTimer();
                        } : null,
                        child: Texts.textBlock(
                          canResend
                              ? "resend".tr
                              : "${"resend".tr} (${formatTime(timeLeft)})",
                          color: canResend
                              ? ColorConstants.primaryColor
                              : Colors.grey,
                          size: 15,
                          fontFamily: "InstrumentSansRegular",
                          fontWeight: FontWeight.w600
                        ),
                      ),
                    ],
                  ),
                  Spacer(),
                  CustomButton(
                    label: "verify_otp".tr,
                    textColor: ColorConstants.whiteColor,
                    backgroundColor: ColorConstants.primaryColor,
                    onTap: () {
                      otpController.text.length != 6
                          ? Widgets.showSnackBar(
                              "otp_form".tr,
                              "enter_six_digit_otp".tr
                            )
                          : authenticationController.verifyMobileOTP(otpController.text);
                    },
                  ),
                ],
              ),
            ),
          ),
        );
      }
    );
  }
}