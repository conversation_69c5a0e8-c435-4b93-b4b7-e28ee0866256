import 'dart:async';
import 'package:banana/api_services/ToastShow.dart';
import 'package:banana/api_services/WebServices.dart';
import 'package:banana/constants/my_app_buttons.dart';
import 'package:banana/constants/my_fonts.dart';
import 'package:banana/constants/my_string.dart';
import 'package:banana/core/constants/color_constants.dart';
import 'package:banana/organizer_flow/view/profile_creation/view/profile_process_view.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:banana/constants/my_colors.dart';
import 'package:banana/constants/my_images.dart';
import 'package:banana/constants/sized_box.dart';
import 'package:flutter/material.dart';

import '../../../core/constants/assets_constants.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../../../utils/my_navigations.dart';
import '../../profile_process/view/profile_process_view.dart';
import '../controller/authentication_controller.dart';
import 'otp_verify_view.dart';

class RoleSelectionView extends StatefulWidget {
  RoleSelectionView() : super();

  @override
  State<RoleSelectionView> createState() => _RoleSelectionViewState();
}

class _RoleSelectionViewState extends State<RoleSelectionView> {

  late AuthenticationController authenticationController;

  @override
  void initState() {
    super.initState();

    authenticationController = Get.find();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(

        appBar: AppBar(centerTitle: false,
          title: Widgets.backCircleButton(),
          automaticallyImplyLeading: false,
        ),
        backgroundColor: Colors.white,
        body: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[

                Texts.textBold('Welcome! How will you use this app?',
                    fontWeight: FontWeight.w100,
                    size: 30,
                    maxline: 2,
                    align: TextAlign.start,
                    color: Colors.black),
                Widgets.heightSpaceH2,
                Texts.textMedium("Select one to help us set things up the right way.",
                    align: TextAlign.start, color: Colors.black54, size: 13),
                Widgets.heightSpaceH4,

                Obx(() => Row(
                  children: [
                    Expanded(
                      child: _buildOption(
                        index: 0,
                        emoji: "❤️",
                        title: "I'm here to",
                        subtitle: "Date & Meet People",
                        isSelected: authenticationController.selectedRole.value ==0,
                        onTap: () => authenticationController.select(0),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildOption(
                        index: 1,
                        emoji: "🎉",
                        title: "I'm here to",
                        subtitle: "Host & Organize Events",
                        isSelected: authenticationController.selectedRole.value == 1,
                        onTap: () => authenticationController.select(1),
                      ),
                    ),
                  ],
                )),
                Widgets.heightSpaceH3,


                Spacer(),
                Obx(() => CustomButton(
                  label: "Next",
                  backgroundColor: authenticationController.selectedRole.value!=-1
                      ? ColorConstants.primaryColor
                      : Colors.grey[300],
                  textColor: authenticationController.selectedRole.value!=-1
                      ? Colors.black87
                      : Colors.grey[600],
                  onTap:  authenticationController.selectedRole.value!=-1
                      ? () {

                    if(authenticationController.selectedRole.value==1)
                      {
                        Get.to(() => OtpVerifyView());
                      }
                      }
                      : null,
                )),
              ],
            )));

  }

  Widget _buildOption({
    required int index,
    required String emoji,
    required String title,
    required String subtitle,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected ? ColorConstants.lightPrimaryColor : Colors.grey[100],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? Colors.black : Colors.transparent,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Text(emoji, style: const TextStyle(fontSize: 32)),
            const SizedBox(height: 8),
            Text(title, style: const TextStyle(fontSize: 12)),
            const SizedBox(height: 4),
            Text(subtitle,
                textAlign: TextAlign.center,
                style: const TextStyle(fontWeight: FontWeight.bold)),
          ],
        ),
      ),
    );
  }
}

