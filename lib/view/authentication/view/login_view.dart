import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:music_app/core/constants/padding_constants.dart';
import 'package:music_app/core/routes/app_routes.dart';
import 'package:music_app/core/utils/extensions.dart';
import 'package:music_app/view/authentication/view/forgot_password_view.dart';
import 'package:music_app/view/authentication/view/signup_view.dart';

import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/entry_field.dart';

import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../../musician/bottom_navigation/view/bottom_bar_view.dart';
import '../controller/authentication_controller.dart';

class LoginView extends StatelessWidget {
  final AuthenticationController authenticationController =
      Get.put(AuthenticationController());
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  LoginView({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: ColorConstants.whiteColor,
        body: Padding(
          padding: PaddingConstants.screenPaddingHalf,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Widgets.heightSpaceH4,
              Image.asset(
                Assets.logoWithNoTxt,
                width: .40.sw,
              ),
              Widgets.heightSpaceH2,
              formSection(context),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Texts.textNormal("Don't have an account?  ",
                      color: Colors.black45, size: 12),
                  InkWell(
                    onTap: () {
                      Get.to(() => SignUpView());
                    },
                    child: Texts.textBlock("Signup",
                        color: ColorConstants.blackColor,
                        size: 13,
                        decoration: TextDecoration.underline,
                        fontFamily: "PoppinsRegular",
                        fontWeight: FontWeight.w400),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
            ],
          ),
        ),
      ),
    );
  }

  formSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Texts.textBold("Welcome to MusicianNow !",
            color: ColorConstants.blackColor, size: 20),
        Widgets.heightSpaceH3,
        EntryField(
          color: ColorConstants.whiteColor,
          controller: emailController,
          prefixIcon: Assets.emailIcon,
          hint: "Type your email",
        ),
        Obx(
          () => EntryField(
            controller: passwordController,
            prefixIcon: Assets.lockIcon,
            hint: "Type your password",
            obscureText: authenticationController.obscured.value,
            suffixIcon: authenticationController.obscured.value == false
                ? CupertinoIcons.eye_slash
                : Icons.remove_red_eye_outlined,
            onTrailingTap: () {
              authenticationController.toggleObscured();
            },
          ),
        ),
        Widgets.heightSpaceH1,
        GestureDetector(
          onTap: () {
            Get.to(ForgotPasswordView());
          },
          child: Align(
            alignment: Alignment.topRight,
            child: Texts.textNormal(
              "Forgot Password?",
              color: Colors.black54,
              size: 12,
            ),
          ),
        ),
        Widgets.heightSpaceH3,
        Obx(() => CustomButton(
              label: authenticationController.isLoginLoading.value == true
                  ? ""
                  : "Login",
              icon: authenticationController.isLoginLoading.value == true
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(color: Colors.white))
                  : SizedBox(),
              textColor: ColorConstants.whiteColor,
              backgroundColor: ColorConstants.primaryColor,
              radius: 10,
              onTap: authenticationController.isLoginLoading.value == true
                  ? null
                  : () {
                      if (!GetUtils.isEmail(emailController.text)) {
                        Widgets.showSnackBar(
                            "Incomplete Form", "Please enter valid email");
                      } else if (passwordController.text.length < 6) {
                        Widgets.showSnackBar("Incomplete Form",
                            "Please enter password min length 6 characters");
                      } else {
                        context.hideKeyboard();
                        authenticationController.loginUser(
                            emailController.text.toString(),
                            passwordController.text.toString());
                      }
                    },
            )),
        Widgets.heightSpaceH1,
        CustomButton(
          label: "Continue as Guest",
          textColor: ColorConstants.whiteColor,
          backgroundColor: ColorConstants.redColor,
          radius: 10,
          onTap: () {
            Get.offAll(() => BottomBarNavigation());
          },
        ),
        Widgets.heightSpaceH2,
        Row(
          children: [
            const Expanded(
                child: Divider(
              color: Colors.black26,
              thickness: .5,
            )),
            Padding(
              padding: PaddingConstants.contentPadding,
              child: Container(
                child: Texts.textNormal("OR",
                    color: ColorConstants.blackColor, size: 12),
              ),
            ),
            const Expanded(
                child: Divider(
              color: Colors.black26,
              thickness: .5,
            )),
          ],
        ),
        Widgets.heightSpaceH2,
        Obx(() => CustomButton(
              label: authenticationController.isLoading.value == true
                  ? ""
                  : "Login With Google",
              icon: authenticationController.isLoading.value == true
                  ? SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                          color: ColorConstants.greyTextColor))
                  : Image.asset(
                      Assets.gooogleIcon,
                      height: 16,
                      width: 16,
                    ),
              textColor: ColorConstants.greyTextColor,
              backgroundColor: ColorConstants.grayFillColor,
              radius: 10,
              borderColor: ColorConstants.grayBorderColor,
              onTap: authenticationController.isLoading.value == true
                  ? null
                  : () {
                      authenticationController.signInWithGoogle();
                    },
            )),
      ],
    );
  }
}
