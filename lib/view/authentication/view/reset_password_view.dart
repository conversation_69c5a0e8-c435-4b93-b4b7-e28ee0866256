import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import 'package:padel_payer/core/utils/extensions.dart';

import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/constants/padding_constants.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/entry_field.dart';

import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../controller/authentication_controller.dart';

class ResetPasswordView extends StatelessWidget {
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  String userId; String email;

  ResetPasswordView(this.userId,this.email);

  AuthenticationController authenticationController = Get.find();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => context.hideKeyboard(),
      child: GetBuilder(
          init: authenticationController,
          builder: (_) {
            return Scaffold(
              resizeToAvoidBottomInset: false,
              backgroundColor: ColorConstants.backgroundColor,
              appBar: AppBar(
                automaticallyImplyLeading: false,
                backgroundColor: ColorConstants.backgroundColor,
                centerTitle: false,
                title: InkWell(
                    onTap: () {
                      Get.back();
                    },

                    child: Icon(Icons.arrow_back_ios_new_outlined)),
              ),

            body: SafeArea(
              child: Padding(
                padding: PaddingConstants.screenPaddingHalf,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Texts.textBold(
                      "reset_password".tr,
                      color: ColorConstants.blackColor,
                      size: 24,
                      textAlign: TextAlign.start
                    ),
                    Widgets.heightSpaceH1,
                    Texts.textMedium(
                      "reset_password_description".tr,
                      color: ColorConstants.textColor,
                      size: 11
                    ),
                    Widgets.heightSpaceH3,
                    EntryField(
                      controller: passwordController,
                      hint: "enter_new_password".tr,
                      prefixIcon: Assets.lockIcon,
                      obscureText: authenticationController.obscured.value,
                      suffixIcon: authenticationController.obscured.value
                          ? Icons.remove_red_eye_outlined
                          : CupertinoIcons.eye_slash,
                      onTrailingTap: authenticationController.toggleObscured,
                    ),
                    EntryField(
                      controller: confirmPasswordController,
                      hint: "confirm_new_password".tr,
                      prefixIcon: Assets.lockIcon,
                      obscureText: authenticationController.obscured.value,
                      suffixIcon: authenticationController.obscured.value
                          ? Icons.remove_red_eye_outlined
                          : CupertinoIcons.eye_slash,
                      onTrailingTap: authenticationController.toggleObscured,
                    ),
                    Spacer(),
                    CustomButton(
                      backgroundColor: ColorConstants.primaryColor,
                      textColor: ColorConstants.whiteColor,
                      label: "submit".tr,
                      onTap: () {
                        context.hideKeyboard();
                        if (passwordController.text.length < 6) {
                          Widgets.showSnackBar(
                            "incomplete_form".tr,
                            "password_length_error".tr
                          );
                        } else if (passwordController.text != confirmPasswordController.text) {
                          Widgets.showSnackBar(
                            "incomplete_form".tr,
                            "passwords_not_matching".tr
                          );
                        } else {
                          authenticationController.resetForgotPassword(
                            passwordController.text,
                            email,
                            userId
                          );
                        }
                      },
                    ),
                  ],
                ),
              ),
            ),
          );
        }
      ),
    );
  }
}
