import 'dart:developer';

import 'package:country_picker/country_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:padel_payer/view/authentication/view/phone_verification_view.dart';

import '../../../controller/user_controller.dart';
import '../../../core/constants/api_endpoints.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/services/http_service.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../../../model/user_model.dart';

import '../../dashboard/view/dashboard_view.dart';
import '../view/email_verification_view.dart';
import '../view/forgot_verification_view.dart';
import '../view/login_view.dart';
import '../view/reset_password_view.dart';

class AuthenticationController extends GetxController {
  late UserController userController;
  RxBool isCheck = false.obs;
  RxBool is18Check = false.obs;
  RxBool obscured = false.obs;
  RxString selectedCountry = "".obs;
  TextEditingController phoneController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  TextEditingController postalCodeController = TextEditingController();
  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  RxString selectedSex = "".obs;
  RxString selectedCountryCode = "1".obs;
  RxString? userID = "".obs;
  RxString? email = "".obs;
  RxString selectedCountryUnit = "".obs;

  void toggleObscured() {
    obscured.value = !obscured.value;
  }

  RxBool isHostSelected = false.obs;
  toggleButton() {
    isHostSelected.value = !isHostSelected.value;
  }

  @override
  void onInit() {
    super.onInit();
    userController = Get.find();
  }

  void loginUser(String emailString, String password) async {
    try {
      Widgets.showLoader("loading".tr);

      var response = await ApiService.postData(
          Endpoints.login, {"email": emailString, "password": password});
      Widgets.hideLoader();

      if (response.status == true) {
        UserModel userModel = UserModel.fromJson(response.data['player']);

        userID?.value = userModel.playerId.toString();

        if (userModel.isEmailVerified == true && userModel.isPhoneVerified == true) {
          if (userModel.status == "Active") {
            userController.saveUser(userModel, response.data['token']);
            userController.fetchUser();
            Get.offAll(() => DashboardView());
          } else {
            Widgets.showSnackBar(
                "alert".tr, "account_inactive".tr);
          }
        } else {

            UserModel userModel = UserModel.fromJson(response.data['player']);

            userID?.value = userModel.playerId.toString();
            if(userModel.isEmailVerified==false){
              Get.to(() => EmailVerificationView());
            }else{
              Get.to(() => PhoneVerificationView());
            }


        }
      }else{
    Widgets.showSnackBar("error".tr, response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("alert".tr, e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  void signupUser({var data}) async {
    try {
      Widgets.showLoader("creating_account".tr);

      var response = await ApiService.postData(Endpoints.signup, data);

      Widgets.hideLoader();

      if (response.status == true) {
        userID?.value = response.data['player']['player_id'].toString();
        Get.off(() => EmailVerificationView());
      } else {
        Widgets.showSnackBar("error".tr, response.message ?? "");
      }
    } catch (e) {
      Widgets.showSnackBar("error".tr, e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  //
  requestForgotPassword(String emailString) async {
    try {
      Widgets.showLoader("loading".tr);

      var payload = {"email": emailString};
      email?.value = emailString;
      var response =
          await ApiService.postData(Endpoints.requestForgotPassword, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar("success".tr, response.message ?? "");
        Get.to(() => ForgotVerificationView(
            emailString, response.data['player_id'].toString()));
      } else {
        Widgets.showSnackBar("error".tr, response.message ?? "");
      }
    } catch (e) {
      Widgets.hideLoader();
    } finally {
      Widgets.hideLoader();
    }
  }

  resendForgotOtp(String email) async {
    try {
      Widgets.showLoader("loading".tr);

      var payload = {"email": email};

      var response =
          await ApiService.postData(Endpoints.requestForgotPassword, payload);

      Widgets.hideLoader();

      if (response.status == true) {
        Widgets.showSnackBar("success".tr, response.message ?? "");
      } else {
        Widgets.showSnackBar("error".tr, response.message ?? "");
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, "something_went_wrong".tr);
    }
  }

  resendMailOtp() async {
    try {
      Widgets.showLoader("loading".tr);

      var payload = {"player_id": userID?.value};

      var response = await ApiService.postData(Endpoints.resendOtp, payload);

      Widgets.hideLoader();
      update();
      if (response.status == true) {
        Widgets.showSnackBar("success".tr, response.message ?? "");
      } else {
        Widgets.showSnackBar(
            "error".tr, response.message ?? "something_went_wrong".tr);
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, "something_went_wrong".tr);
    } finally {
      Widgets.hideLoader();
    }
  }

  //
  verifyOTP(String otpCode) async {
    Widgets.showLoader("verifying_otp".tr);
    try {
      var payload = {"player_id": userID?.value, "otp": otpCode};
      var response = await ApiService.postData(Endpoints.verifyOTP, payload);
      Widgets.hideLoader();
      if (response.status == true) {
        Get.off(() => PhoneVerificationView());
      } else {
        Widgets.showSnackBar("error".tr, response.message ?? "invalid_code".tr);
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, e.toString());
    }
  }
  verifyMobileOTP(String otpCode) async {
    Widgets.showLoader("verifying_otp".tr);
    try {
      var payload = {"player_id": userID?.value, "otp": otpCode};
      var response = await ApiService.postData(Endpoints.verifyMobileOTP, payload);
      Widgets.hideLoader();
      if (response.status == true) {
        UserModel userModel = UserModel.fromJson(response.data['player']);
        userController.saveUser(userModel, response.data['token']);
        userController.fetchUser();
        Get.offAll(() => DashboardView());
      } else {
        Widgets.showSnackBar("error".tr, response.message ?? "invalid_code".tr);
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, e.toString());
    }
  }
  resendMobileOtp() async {
    try {
      Widgets.showLoader("loading".tr);

      var payload = {"player_id": userID?.value};

      var response = await ApiService.postData(Endpoints.sendMobileOTP, payload);

      Widgets.hideLoader();
      update();
      if (response.status == true) {
        Widgets.showSnackBar("success".tr, response.message ?? "");
      } else {
        Widgets.showSnackBar(
            "error".tr, response.message ?? "something_went_wrong".tr);
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, "something_went_wrong".tr);
    } finally {
      Widgets.hideLoader();
    }
  }

  verifyForgotOTP(String otpCode, String email) async {
    Widgets.showLoader("verifying_otp".tr);

    try {
      var payload = {"player_id": email, "otp": otpCode};
      var response =
          await ApiService.postData(Endpoints.verifyForgotPassword, payload);

      Widgets.hideLoader();
      if (response.status == true) {
        Get.off(() => ResetPasswordView(
            response.data['player']['player_id'].toString(), email));
      } else {
        Widgets.showSnackBar("error".tr, response.message ?? "invalid_code".tr);
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, e.toString());
    }
  }

  //
  resetForgotPassword(String password, String email, String userId) async {
    Widgets.showLoader("loading".tr);

    try {
      var payload = {
        "password": password,
        "player_id": userId,
        "password_confirmation": password
      };
      var response =
          await ApiService.postData(Endpoints.resetPassword, payload);

      Widgets.hideLoader();
      if (response.status == true) {
        userID?.value = "";
        Widgets.showSnackBar("success".tr, response.message.toString());
        Get.offAll(() => LoginView());
      } else {
        Widgets.showSnackBar("error".tr, response.message.toString());
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, e.toString());
    }
  }

  pickCountryBottomSheet(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(
          searchTextStyle: TextStyle(color: Colors.black87),
          textStyle: const TextStyle(color: Colors.black),
          bottomSheetHeight: .60.sh,
          inputDecoration: InputDecoration(
              prefixIcon: Icon(CupertinoIcons.search),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              hintText: "search_country".tr)),
      showPhoneCode: false, // Set to true if you need phone codes
      onSelect: (Country country) {
        selectedCountry.value = country.name;
        selectedCountryUnit.value = country.countryCode;
      },
    );
  }

  pickCountryCodeBottomSheet(BuildContext context) {
    showCountryPicker(
      context: context,
      countryListTheme: CountryListThemeData(
          searchTextStyle: TextStyle(color: Colors.black87),
          textStyle: const TextStyle(color: Colors.black),
          bottomSheetHeight: .60.sh,
          inputDecoration: InputDecoration(
              prefixIcon: Icon(CupertinoIcons.search),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.primaryColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              enabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: ColorConstants.greyColor),
                borderRadius: const BorderRadius.all(Radius.circular(10)),
              ),
              hintText: "search_country".tr)),
      showPhoneCode: true, // Set to true if you need phone codes
      onSelect: (Country country) {
        print(country.phoneCode);
        selectedCountryCode.value = country.phoneCode;
      },
    );
  }

  void pickSexBottomSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'select_sex'.tr,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ColorConstants.blackColor,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              dense: true,
              onTap: () {
                selectedSex.value = "male".tr;
                Get.back();
              },
              title: Text('male'.tr),
            ),
            Divider(),
            ListTile(
              dense: true,
              onTap: () {
                selectedSex.value = "female".tr;
                Get.back();
              },
              title: Text('female'.tr),
            ),
          ],
        ),
      ),
      backgroundColor: Colors.transparent,
    );
  }
}
