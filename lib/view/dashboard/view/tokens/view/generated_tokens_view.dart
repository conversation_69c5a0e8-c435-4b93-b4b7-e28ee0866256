import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../../core/constants/assets_constants.dart';
import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/constants/constants_list.dart';
import '../../../../../core/utils/utils.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/entry_field.dart';
import '../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../core/widgets/text_widgets.dart';
import '../../../../../core/widgets/widgets.dart';
import '../../../controller/dashboard_controller.dart';
import '../controller/tokens_controller.dart';
import '../model/token_model.dart';

class GeneratedTokensView extends StatefulWidget {
  const GeneratedTokensView({super.key});

  @override
  State<GeneratedTokensView> createState() => _GeneratedTokensViewState();
}

class _GeneratedTokensViewState extends State<GeneratedTokensView>
    with SingleTickerProviderStateMixin {
  late TokensController controller;

  late TabController _tabController;
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _tabController.addListener(_handleTabChange);
    controller = Get.put(TokensController());
    controller.selectedTabIndex.value = 0;
    controller.selectedTabLabel.value = "All";
    controller.isTokensLoading.value = false;
    final now = DateTime.now();
    final sixtyDaysAgo = now.subtract(const Duration(days: 60));
    controller.startDate.value = controller.formatDate(sixtyDaysAgo);
    controller.endDate.value = controller.formatDate(now);
    controller.isTokensMoreLoading.value = false;
    controller.fetchTokens(page: 1, status: controller.selectedTabLabel.value);
    scrollController.addListener(scrollListener);
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      controller.tokens.clear();
      controller.currentTokensPage.value = 1;
      controller.totalTokens.value = 0;
      controller.fetchTokens(
        page: 1,
        status: controller.selectedTabLabel.value,
      );
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        controller.totalTokens.value > controller.tokens.length) {
      controller.fetchTokens(
        page: controller.currentTokensPage.value + 1,
        status: controller.selectedTabLabel.value,
      );
      controller.currentTokensPage.value++;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: ColorConstants.blackColor),
          onPressed: () => Get.back(),
        ),
        title: Texts.textBold(
          "all_tokens".tr,
          color: ColorConstants.blackColor,
          size: 18,
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: ColorConstants.blackColor),

            onPressed: () {
              controller.tokens.clear();
              controller.currentTokensPage.value = 1;
              controller.totalTokens.value = 0;
              controller.fetchTokens(
                page: 1,
                status: controller.selectedTabLabel.value,
              );
            },
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.0),
            child: EntrySearchField(
              controller: controller.searchController,
              prefixIcon: Assets.searchIcon,
              hint: "search_tokens".tr,
              suffixIcon: Assets.filterIcon,
              onChange: (value) {
                if (controller.searchDebounce?.isActive ?? false) {
                  controller.searchDebounce?.cancel();
                }
                controller.searchDebounce =
                    Timer(const Duration(milliseconds: 500), () {
                  controller.fetchTokens();
                });
              },
            ),
          ),
          Widgets.heightSpaceH1,

          // Date Range Selection
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 15.0),
            child: Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => controller.selectStartDate(context),
                    child: Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: ColorConstants.blackColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_month,
                            color: Colors.white,
                            size: 15,
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Obx(() => Text(
                                controller.startDate.value ?? 'Start Date',
                                style:
                                    TextStyle(color: ColorConstants.whiteColor),
                              )),
                        ],
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: InkWell(
                    onTap: () => controller.selectEndDate(context),
                    child: Container(
                      padding: EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: ColorConstants.blackColor,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.calendar_month,
                            color: Colors.white,
                            size: 15,
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Obx(() => Text(
                                controller.endDate.value ?? 'End Date',
                                style:
                                    TextStyle(color: ColorConstants.whiteColor),
                              )),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Widgets.heightSpaceH2,
          Container(
            height: 35,
            child: ListView(
              scrollDirection: Axis.horizontal,
              shrinkWrap: true,
              children: [
                Widgets.widthSpaceW4,
                buildTab("All", 0),
                Widgets.widthSpaceW1,
                Widgets.widthSpaceW1,
                buildTab("Requested", 1),
                Widgets.widthSpaceW1,
                buildTab("Generated", 2),
                Widgets.widthSpaceW1,
                buildTab("Redeemed", 3),
                Widgets.widthSpaceW1,
                buildTab("Used", 4),
              ],
            ),
          ),
          Widgets.heightSpaceH1,
          Expanded(
            child: TabBarView(
                physics: const NeverScrollableScrollPhysics(),
                controller: _tabController,
                children: [
                  buildAllBookingsList(),
                  buildAllBookingsList(),
                  buildAllBookingsList(),
                  buildAllBookingsList(),
                  buildAllBookingsList(),
                ]),
          ),
        ],
      ),
    );
  }

  Widget buildTab(String label, int index) {
    return Obx(() {
      final isSelected = controller.selectedTabIndex.value == index;
      return GestureDetector(
        onTap: () {
          controller.selectedTabIndex.value = index;
          controller.selectedTabLabel.value = label;
          _tabController.animateTo(index);
        },
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 15,
          ),
          decoration: BoxDecoration(
            color: isSelected
                ? ColorConstants.primaryColor
                : ColorConstants.silverColor,
            borderRadius: BorderRadius.circular(10.0),
          ),
          child: Center(
            child: Texts.textMedium(label,
                color: isSelected ? Colors.white : Colors.black, size: 12),
          ),
        ),
      );
    });
  }

  Widget buildAllBookingsList() {
    return Obx(
      () {
        return Scrollbar(
          child: ListView(
            controller: scrollController,
            padding: const EdgeInsets.symmetric(horizontal: 15),
            children: [
              Widgets.heightSpaceH2,
              controller.isTokensLoading.value
                  ? const ShimmerListSkeleton()
                  : controller.tokens.isNotEmpty
                      ? ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            Token? token = controller.tokens[index];
                            return buildTokenCard(token, context,index);
                          },
                          separatorBuilder: (context, index) {
                            return Widgets.heightSpaceH1;
                          },
                          itemCount: controller.tokens.length ?? 0)
                      : Widgets.noRecordsFound(title: "no_tokens".tr),
              if (controller.isTokensMoreLoading.value) Widgets.moreLoading(),
              Widgets.heightSpaceH2,
            ],
          ),
        );
      },
    );
  }

  buildTokenCard(Token token, BuildContext context,int index) {
    return Container(
                            padding: EdgeInsets.all(12),
                            decoration: Widgets.cardBoxDecoration,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Row(mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Texts.textBold(
                                            '${Data.getCurrencySymbol(token.club?.currency ?? "")}${token.tokenType?.value ?? ""} per ${token.tokenType?.minutes.toString() == "60" ? "Hour" : token.tokenType?.minutes.toString() == "30" ? "1/2 Hour" : "Hour" ?? ""}',

                                          color: ColorConstants.blackColor,
                                          size: 17,
                                        ),

                                      ],
                                    ),
                                    Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 10,
                                        vertical: 5,
                                      ),
                                      decoration: BoxDecoration(
                                        color: token.status == "Redeemed"
                                            ? Colors.red
                                            : token.status == "Requested"
                                                ? Colors.pink
                                                : token.status == "Used"
                                                    ? Colors.black
                                                    : token.status == "Paid"
                                                        ? Colors.green
                                                        : ColorConstants
                                                            .primaryColor,
                                        borderRadius:
                                            BorderRadius.circular(5),
                                      ),
                                      child: Texts.textNormal(
                                        token?.status ?? "Active",
                                        color: Colors.white,
                                        size: 10,
                                      ),
                                    ),
                                  ],
                                ),
                                Widgets.heightSpaceH05,
                                Texts.textNormal("Token id: ${token?.tokenId ?? ""}",
                                  color: Colors.black,
                                  size: 12,
                                ),
                                Widgets.heightSpaceH05,
                                Texts.textNormal(
                                  '${token.status ?? ""} on: ${Utils.formatDateTimeFromString(token.status == "Redeemed" ? token.redeemedAt ?? "" : token.status == "Requested" ? token.requestedAt ?? "" : token.status == "Used" ? token.usedAt ?? "" : token.status == "Paid" ? token.paidAt ?? "" : token.generatedAt ?? "")}',
                                  color: Colors.black,
                                  size: 12,
                                ), Widgets.heightSpaceH05,
                                Texts.textNormal(
                                  'Club Name: ${token.club?.clubName ?? ""}',
                                  color: Colors.black,
                                  size: 12,
                                ),
                                token?.status == "Generated"
                                    ? Column(
                                        children: [
                                          Widgets.heightSpaceH1,
                                          token.club?.sellToken ==false
                                              ? Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Expanded(
                                                      child: CustomButton(
                                                        label: "add_credit".tr,
                                                        textColor: ColorConstants.whiteColor,
                                                        backgroundColor: ColorConstants.greenColor,
                                                        padding: 8,
                                                        fontSize: 12,
                                                        onTap: () async {
                                                          controller.authorizeToken(token.tokenId ?? "",index);
                                                        }
                                                      )
                                                    ),
                                                    SizedBox(width: 10),
                                                    Expanded(
                                                      child: CustomButton(
                                                        label: "redeem".tr,
                                                        textColor: ColorConstants.whiteColor,
                                                        backgroundColor: ColorConstants.redColor,
                                                        padding: 8,
                                                        fontSize: 12,
                                                        onTap: () async {
                                                          bool confirm = await Widgets.confirmationDialogue(
                                                            context,
                                                            'redeem_token'.tr,
                                                            'redeem_confirmation'.tr,
                                                          );
                                                          if (confirm) {
                                                            controller.redeemToken(token?.tokenId ?? "");
                                                          }
                                                        }
                                                      )
                                                    ),
                                                  ],
                                                )
                                              : CustomButton(
                                                  label: "add_credit".tr,
                                                  textColor: ColorConstants.whiteColor,
                                                  backgroundColor: ColorConstants.greenColor,
                                                  padding: 8,
                                                  fontSize: 12,
                                                  onTap: () async {
                                                    controller.authorizeToken(token?.tokenId ?? "",index);
                                                  }
                                                ),
                                        ],
                                      )
                                    : SizedBox(),
                              ],
                            ),
                          );
  }
}
