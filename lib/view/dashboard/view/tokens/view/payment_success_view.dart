import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../../core/constants/assets_constants.dart';
import '../../../../../../core/constants/color_constants.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/text_widgets.dart';
import '../../../../../../core/widgets/widgets.dart';
import '../../dashboard_view.dart';

class PaymentSuccesView extends StatefulWidget {
  final String url;
  const PaymentSuccesView({required this.url, super.key});

  @override
  State<PaymentSuccesView> createState() => _PaymentSuccesViewState();
}

class _PaymentSuccesViewState extends State<PaymentSuccesView> {
  @override
  void initState() {
    super.initState();
    verifyPayment();
  }

  Future<void> verifyPayment() async {
    try {
      final userController = Get.find<UserController>();
      final response = await http.get(
        Uri.parse(widget.url),
        headers: {
          'Accept': 'application/json',
          'Authorization': 'Bearer ${userController.token}',
        },
      );

      final responseData = jsonDecode(response.body);
      if (response.statusCode == 200 && responseData['status'] == true) {

      } else {

      }
    } catch (e) {

    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Get.offAll(() => DashboardView());
        return false;
      },
      child: Scaffold(
        body: Padding(
          padding: const EdgeInsets.all(15.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Center(
                child: Image.asset(
                  Assets.success,
                  width: .8.sw,
                ),
              ),
              Texts.textBlock(
                "payment_completed".tr,
                size: 22,
                maxline: 2,
                align: TextAlign.center,
              ),
              Widgets.heightSpaceH2,
              Texts.textNormal(
                "payment_thank_you".tr,
                size: 14,
              ),
              Widgets.heightSpaceH4,
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 28.0),
                child: CustomButton(
                  label: "back".tr,
                  borderColor: Colors.transparent,
                  backgroundColor: ColorConstants.primaryColor,
                  textColor: ColorConstants.whiteColor,
                  radius: 50,
                  onTap: () {
                    Get.offAll(() => DashboardView());
                  },
                ),
              )

            ],
          ),
        ),
      ),
    );
  }
}