import 'package:padel_payer/view/dashboard/model/token_type_model.dart';

import '../../clubs/model/club_model.dart';

class Token {
  String? tokenId;
  String? clubId;TokenType ?tokenType;
  String? status;
  String? commercialId;
  String? purchasedAt;
  String? usedAt;
  String? redeemedAt;
  String? paidAt;
  String? invoice;Club? club;
  String? updatedAt;
  String? createdAt;String? generatedAt;
  String? id;
String? requestedAt;
  Token(
      {this.tokenId,this.club,
        this.clubId,
        this.tokenType,
        this.status,this.requestedAt,
        this.commercialId,
        this.purchasedAt,this.generatedAt,
        this.usedAt,
        this.redeemedAt,
        this.paidAt,
        this.invoice,
        this.updatedAt,
        this.createdAt,
        this.id});

  Token.fromJson(Map<String, dynamic> json) {
    tokenId = json['token_id'];
    clubId = json['club_id'].toString();
    club = json['club'] != null ? Club.fromJson(json['club']) : null;
    status = json['status'];
    commercialId = json['commercial_id'];
    purchasedAt = json['purchased_at'];generatedAt = json['generated_at'];
    usedAt = json['used_at'];
    tokenType = json['token_type'] != null ? TokenType.fromJson(json['token_type']) : null;
    redeemedAt = json['redeemed_at'];
    paidAt = json['paid_at'];
    invoice = json['invoice'];requestedAt = json['requested_at'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['token_id'] = this.tokenId;
    data['club_id'] = this.clubId;

    data['status'] = this.status;
    data['commercial_id'] = this.commercialId;
    data['purchased_at'] = this.purchasedAt;
    data['used_at'] = this.usedAt;
    data['redeemed_at'] = this.redeemedAt;
    data['paid_at'] = this.paidAt;
    data['invoice'] = this.invoice;
    data['updated_at'] = this.updatedAt;
    data['created_at'] = this.createdAt;
    data['id'] = this.id;
    return data;
  }
}
