import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../controller/user_controller.dart';
import '../../../../../core/constants/api_endpoints.dart';
import '../../../../../core/services/http_service.dart';
import '../../../../../core/widgets/widgets.dart';
import '../model/token_model.dart';
import '../../../../../core/utils/utils.dart';

class TokensController extends GetxController {
  final searchController = TextEditingController();
  Timer? searchDebounce;
  final tokens = <Token>[].obs;
  final startDate = Rx<String?>(null);
  final endDate = Rx<String?>(null);
  RxInt totalTokens = 0.obs; // Rx variable to check if there's more data
  RxInt currentTokensPage = 0.obs; // Rx variable to check if there's more data
  RxBool isTokensMoreLoading = false.obs;
  RxBool isTokensLoading = false.obs;
  RxInt selectedTabIndex = 0.obs;
  RxString selectedTabLabel = "All".obs;
  final requestedTokens = <Token>[].obs;

  @override
  void onInit() {
    super.onInit();
    // Set default dates to today
    final now = DateTime.now();
    startDate.value = formatDate(now);
    endDate.value = formatDate(now);
  }
  redeemToken(String token) async {
    try {
      Widgets.showLoader("loading".tr);
      var response = await ApiService.postData(
          "${Endpoints.redeemTokenRequest}?token_id=$token",{"type":"accept"}
      );
      Widgets.hideLoader();
      if (response.status == true) {

        fetchTokens(page: currentTokensPage.value,status:"All");selectedTabIndex.value=0;selectedTabLabel.value="All";

      }
    } catch (e) {
      print(e);
    } finally {
      isTokensLoading.value = false;
      isTokensMoreLoading.value = false;
    }
  }
  @override
  void onClose() {
    searchDebounce?.cancel();
    searchController.dispose();
    super.onClose();
  }

  authorizeToken(String token,int index) async {
    Widgets.showLoader("loading".tr);

    try {
      var payload = {
        "token_id": token,
      };
      var response = await ApiService.postData(Endpoints.useToken, payload);

      Widgets.hideLoader();
      if (response.status == true) {
        tokens[index].status="Used";
        tokens.refresh();
      } else {
        Widgets.showSnackBar("alert".tr, response.message.toString());
      }
    } catch (e) {
      Widgets.hideLoader();
      // Widgets.showSnackBar("error".tr, e.toString());
    }
  }
  fetchTokens({int page = 1, String status = ''}) async {
    try {
      if (isTokensLoading.value) return;
      if (page == 1) {
        isTokensLoading.value = true;
      } else {
        isTokensMoreLoading.value = true;
      }
      var response = await ApiService.getData(
        "${Endpoints.tokens}?page=$page&status=${status=="All"?"":status}&from_date=${startDate.value ?? ""}&to_date=${endDate.value ?? ""}&search=${searchController.text}",
      );

      if (response.status == true) {
        if (page == 1) {
          tokens.clear();
          totalTokens.value = 0;
          currentTokensPage.value = 1;
        }

        tokens.addAll(
          (response.data['data'] as List)
              .map((e) => Token.fromJson(e))
              .toList(),
        );
        totalTokens.value = response.data['pagination']['total'] ?? 0;

      }
    } catch (e) {
      print(e);
    } finally {
      isTokensLoading.value = false;
      isTokensMoreLoading.value = false;
    }
  }
  fetchRequestedTokens({int page = 1}) async {
    try {
      if (isTokensLoading.value) return;
      if (page == 1) {
        isTokensLoading.value = true;
      } else {
        isTokensMoreLoading.value = true;
      }
      var response = await ApiService.getData(
        "${Endpoints.requestedTokens}?page=$page",
      );

      if (response.status == true) {
        if (page == 1) {
         requestedTokens.clear();
          totalTokens.value = 0;
          currentTokensPage.value = 1;
        }

        requestedTokens.addAll(
          (response.data['data'] as List)
              .map((e) => Token.fromJson(e))
              .toList(),
        );
        totalTokens.value = response.data['pagination']['total'] ?? 0;

      }
    } catch (e) {
      print(e);
    } finally {
      isTokensLoading.value = false;
      isTokensMoreLoading.value = false;
    }
  }

  String formatDate(DateTime date) {
    return "${date.day.toString().padLeft(2, '0')}-${date.month.toString().padLeft(2, '0')}-${date.year}";
  }

  String formatDateForApi(DateTime date) {
    return "${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}";
  }

  void selectStartDate(BuildContext context) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _parseDate(startDate.value!) ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: _parseDate(endDate.value!) ?? DateTime.now(),
    );

    if (picked != null) {
      // If selected start date is after end date, update end date too
      DateTime endDateTime = _parseDate(endDate.value!) ?? DateTime.now();
      if (picked.isAfter(endDateTime)) {
        endDate.value = formatDate(picked);
      }
      startDate.value = formatDate(picked);
      fetchTokens(page: 1, status: selectedTabLabel.value);
    }
  }

  void selectEndDate(BuildContext context) async {
    DateTime startDateTime = _parseDate(startDate.value!) ?? DateTime.now();

    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _parseDate(endDate.value!) ?? DateTime.now(),
      firstDate: startDateTime,
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      // If selected end date is before start date, update start date too
      if (picked.isBefore(startDateTime)) {
        startDate.value = formatDate(picked);
      }
      endDate.value = formatDate(picked);
      fetchTokens(page: 1, status: selectedTabLabel.value);
    }
  }

  // Helper method to parse date string in DD-MM-YYYY format
  DateTime _parseDate(String date) {
    List<String> parts = date.split('-');
    return DateTime(
      int.parse(parts[2]), // year
      int.parse(parts[1]), // month
      int.parse(parts[0]), // day
    );
  }
}