import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/constants/padding_constants.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/entry_field.dart';
import '../../../../../core/widgets/text_widgets.dart';
import '../../../../../core/widgets/widgets.dart';
import '../../../controller/dashboard_controller.dart';

class ChangePasswordView extends StatelessWidget {
  TextEditingController passwordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController oldPasswordController = TextEditingController();

  DashboardController profileController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: ColorConstants.blackColor),
          onPressed: () => Get.back(),
        ),
        title: Texts.textBold(
          "change_password".tr,
          color: ColorConstants.blackColor,
          size: 18,
        ),
      ),
      body: Padding(
        padding: PaddingConstants.screenPaddingLess,
        child: Column(
          children: [
            Widgets.heightSpaceH3,
            EntryField(
              controller: oldPasswordController,
              label: "current_password".tr,
              hint: "write_here".tr,
            ),
            EntryField(
              controller: passwordController,
              label: "new_password".tr,
              hint: "write_here".tr,
            ),
            EntryField(
              controller: confirmPasswordController,
              label: "confirm_new_password".tr,
              hint: "write_here".tr,
            ),
            Widgets.heightSpaceH3,
            CustomButton(
              label: "update_password".tr,
              textColor: ColorConstants.whiteColor,
              backgroundColor: ColorConstants.primaryColor,
              onTap: () {
                if (oldPasswordController.text.length < 6) {
                  Widgets.showSnackBar(
                    "incomplete_form".tr,
                    "enter_old_password".tr,
                  );
                } else if (passwordController.text.length < 6) {
                  Widgets.showSnackBar(
                    "incomplete_form".tr,
                    "enter_new_password".tr,
                  );
                } else if (passwordController.text != confirmPasswordController.text) {
                  Widgets.showSnackBar(
                    "incomplete_form".tr,
                    "passwords_not_matching".tr,
                  );
                } else {
                  profileController.changePassword(
                    passwordController.text,
                    oldPasswordController.text,
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}