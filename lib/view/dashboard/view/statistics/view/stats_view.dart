import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:padel_payer/core/widgets/text_widgets.dart';
import 'package:padel_payer/core/widgets/widgets.dart';

import '../../../../../controller/user_controller.dart';
import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/shimmer_effect.dart';
import '../controller/stats_controller.dart';
import '../model/comments_model.dart';

class PlayerStatsView extends StatelessWidget {
  final controller = Get.put(PlayerStatsController());

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        floatingActionButton: FloatingActionButton(
          backgroundColor: Colors.black,
          child: Icon(Icons.comment_outlined, color: Colors.white),
          onPressed: () {
            // Fetch comments when button is pressed

            controller.getPlayerComments();

            Get.bottomSheet(
              Container(height: 0.5.sh,
                padding: EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Texts.textBold("Coach Comments", size: 18),
                        IconButton(
                          icon: Icon(Icons.close),
                          onPressed: () => Get.back(),
                        )
                      ],
                    ),
                    Widgets.heightSpaceH2,
                    Obx(() {
                      return controller.isLoadingComments.value
                          ? Center(
                        child: Column(
                          children: [
                            Widgets.heightSpaceH5,
                            Center(child: CircularProgressIndicator()),
                          ],
                        ),
                      )
                          : controller.comments.isEmpty
                              ? Center(
                                child: Column(
                                    children: [
                                      Widgets.heightSpaceH5,
                                      Texts.textMedium("No comments so far", size: 12),
                                    ],
                                  ),
                              )
                              : Expanded(
                          child: Scrollbar(
                            child: ListView.separated(
                                shrinkWrap: true,
                                physics: BouncingScrollPhysics(),
                                itemCount: controller.comments.length,
                                itemBuilder: (context, index) {
                                  final comment = controller.comments[index];
                                  return _buildCommentItem(comment);
                                },
                                separatorBuilder:
                                    (BuildContext context, int index) {
                                  return Widgets.heightSpaceH1;
                                }),
                          ));}),
                    Widgets.heightSpaceH2,
                  ],
                ),
              ),
              isScrollControlled: true,
            );
          },
        ),
        appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            centerTitle: true,
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: ColorConstants.blackColor),
              onPressed: () => Get.back(),
            ),
            title: Texts.textBold(
              "statistics".tr,
              color: ColorConstants.blackColor,
              size: 18,
            ),


        actions: [


          IconButton(onPressed: (){
            if(controller.selectedTabIndex==0){
              controller.getPlayerStats();
            }else{}
            controller.getGameStats();

          }, icon: Icon(Icons.refresh,))
        ],
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 15, vertical: 0),
          // padding: EdgeInsets.all(15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(10),
                child: Container(
                  decoration: BoxDecoration(
                    color: ColorConstants.greyColor,
                  ),
                  child: Obx(
                    () => Row(
                      children: [
                        Expanded(
                            child: InkWell(
                          onTap: () {
                            controller.selectedTabIndex.value = 0;
                            controller.getPlayerStats();
                          },
                          child: Container(
                            color: controller.selectedTabIndex.value == 0
                                ? ColorConstants.primaryColor
                                : Colors.transparent,
                            padding: EdgeInsets.symmetric(vertical: 12),
                            child: Texts.textBold("player_stats".tr,
                                size: 12,
                                color: controller.selectedTabIndex.value == 0
                                    ? Colors.white
                                    : ColorConstants.blackColor),
                          ),
                        )),
                        SizedBox(
                          width: 10,
                        ),
                        Expanded(
                            child: InkWell(
                          onTap: () {
                            controller.selectedTabIndex.value = 1;
                            controller.getGameStats();
                          },
                          child: Container(
                            color: controller.selectedTabIndex.value == 1
                                ? ColorConstants.primaryColor
                                : Colors.transparent,
                            padding: EdgeInsets.symmetric(vertical: 12),
                            child: Texts.textBold("game_stats".tr,
                                size: 12,
                                color: controller.selectedTabIndex.value == 1
                                    ? Colors.white
                                    : ColorConstants.blackColor),
                          ),
                        )),
                      ],
                    ),
                  ),
                ),
              ),
              Obx(() => controller.isDummyData.value == false
                  ?  SizedBox():Column(
                      children: [
                        Widgets.heightSpaceH3,
                        Center(
                            child: Texts.textBold(
                          "This is a Demo Stats to show you how it show after you play games".tr,
                          size: 20,
                          color: Colors.red,
                        )),
                      ],
                    )
                  ),
              Widgets.heightSpaceH2,
              Obx(() => controller.selectedTabIndex.value == 0
                  ? Column(
                      children: [
                        Container(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Texts.textBold(
                                          "rating_progress".tr + "",
                                          size: 15), Texts.textMedium(
                                          "in ${controller.ratingSpots.length} ${"matches".tr}",
                                          size: 12),

                                    ],
                                  ),
                                  Obx(() {
                                    final userController =
                                        Get.find<UserController>();
                                    final ratingType =
                                        userController.userModel?.ratingType ??
                                            'Playtomic';

                                    return Row(
                                      children: [
                                        InkWell(
                                          onTap: () {
                                            controller.ratingSystem.value =
                                                'Playtomic';
                                            userController.updateUserRatingType(
                                                'Playtomic');
                                          },
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 10, vertical: 5),
                                            decoration: BoxDecoration(
                                              color: controller
                                                          .ratingSystem.value ==
                                                      'Playtomic'
                                                  ? ColorConstants.blackColor
                                                  : Colors.grey.shade200,
                                              borderRadius:
                                                  BorderRadius.horizontal(
                                                      left:
                                                          Radius.circular(15)),
                                            ),
                                            child: Texts.textMedium(
                                              "Playtomic",
                                              size: 12,
                                              color: controller
                                                          .ratingSystem.value ==
                                                      'Playtomic'
                                                  ? Colors.white
                                                  : Colors.black,
                                            ),
                                          ),
                                        ),
                                        InkWell(
                                          onTap: () {
                                            controller.ratingSystem.value =
                                                'Matchi';
                                            userController
                                                .updateUserRatingType('Matchi');
                                          },
                                          child: Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 10, vertical: 5),
                                            decoration: BoxDecoration(
                                              color: controller
                                                          .ratingSystem.value ==
                                                      'Matchi'
                                                  ? ColorConstants.blackColor
                                                  : Colors.grey.shade200,
                                              borderRadius:
                                                  BorderRadius.horizontal(
                                                      right:
                                                          Radius.circular(15)),
                                            ),
                                            child: Texts.textMedium(
                                              "MATCHi",
                                              size: 12,
                                              color: controller
                                                          .ratingSystem.value ==
                                                      'Matchi'
                                                  ? Colors.white
                                                  : Colors.black,
                                            ),
                                          ),
                                        ),
                                      ],
                                    );
                                  }),
                                ],
                              ),
                              Widgets.heightSpaceH05,
                              SizedBox(
                                height: 200,
                                width: 1.sw,
                                child: Obx(() {
                                  final isMatchi =
                                      controller.ratingSystem.value == 'Matchi';

                                  // Convert spots based on selected rating system
                                  final spots =
                                      controller.ratingSpots.map((spot) {
                                    if (isMatchi && spot.y > 0) {

                                      return FlSpot(spot.x, (spot.y * 10) / 7);
                                    } else {
                                      // Keep original Playtomic rating
                                      return spot;
                                    }
                                  }).toList();

                                  return LineChart(
                                    LineChartData(
                                      gridData: FlGridData(show: true),
                                      titlesData: FlTitlesData(
                                        bottomTitles: AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: true,
                                            reservedSize: 30,
                                            getTitlesWidget: (value, meta) {
                                              // convert x to labels (e.g., match # or date)
                                              return Texts.textMedium(
                                                  "${value.toInt() + 1}",
                                                  size: 10);
                                            },
                                          ),
                                        ),
                                        leftTitles: AxisTitles(
                                          sideTitles: SideTitles(
                                            showTitles: true,
                                            reservedSize: 40,
                                            getTitlesWidget: (value, meta) {
                                              // Display the value as is, since we've already converted the spots
                                              final displayValue =
                                                  value.toStringAsFixed(1);
                                              return Padding(
                                                padding: const EdgeInsets.only(
                                                    right: 8),
                                                child: Texts.textMedium(
                                                  displayValue,
                                                  size: 9,
                                                  textAlign: TextAlign.right,
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                      borderData: FlBorderData(
                                        show: true,
                                        border: const Border(
                                          left: BorderSide(),
                                          bottom: BorderSide(),
                                        ),
                                      ),
                                      lineBarsData: [
                                        LineChartBarData(
                                          isCurved: true,
                                          spots: spots,
                                          barWidth: 3,
                                          dotData: FlDotData(show: true),
                                          color: ColorConstants.primaryColor,
                                        ),
                                      ],
                                    ),
                                  );
                                }),
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Texts.textBold(
                                      "${"your_overall_rating".tr} ${controller.ratingSystem.value == 'Matchi' ? "${ ((controller.overallRating.value * 10 / controller.ratingSpots.length).toStringAsFixed(2))}" : "${controller.isDummyData.value ? 5.78:controller.overallRating.value}"}",
                                      size: 13),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Widgets.heightSpaceH3,
                        Obx(() {
                          return controller.isLoading.value
                              ? buildStatsShimmer()
                              : ListView.separated(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  itemCount: controller.statsList.length,
                                  itemBuilder: (context, index) {
                                    final stat = controller.statsList[index];
                                    if(stat.label?.toUpperCase() == "RATING".toUpperCase()){
                                      return SizedBox();
                                    }else{
                                      return Widgets.buildStatsCard(stat);}},

                            separatorBuilder:
                                      (BuildContext context, int index) {
                                    return Widgets.heightSpaceH1;
                                  },
                                );
                        }),
                        Widgets.heightSpaceH3,
                        Widgets.heightSpaceH3,
                        Widgets.heightSpaceH3,
                        Widgets.heightSpaceH3,
                      ],
                    )
                  : Column(
                      children: [
                        Obx(() {
                          return controller.isLoading.value
                              ? buildStatsShimmer()
                              : ListView.separated(
                                  shrinkWrap: true,
                                  physics: NeverScrollableScrollPhysics(),
                                  itemCount: controller.statsList.length,
                                  itemBuilder: (context, index) {
                                    final stat = controller.statsList[index];
                                   if(stat.label?.toUpperCase() == "RATING".toUpperCase()){
                                      return SizedBox();
                                    }else{
                                      return Widgets.buildStatsCard(stat);}},

                                  separatorBuilder:
                                      (BuildContext context, int index) {
                                    return Widgets.heightSpaceH1;
                                  },
                                );
                        }),
                        Widgets.heightSpaceH3,
                        Widgets.heightSpaceH3,
                        Widgets.heightSpaceH3,
                      ],
                    ))
            ],
          ),
        ),
      ),
    );
  }
}

Widget _buildCommentItem(Comment comment) {
  return Container(
    margin: EdgeInsets.symmetric(vertical: 8),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [

        Texts.textMedium(comment.message??"", size: 13,color: ColorConstants.blackColor),SizedBox(height: 3),
      Align(
        alignment: Alignment.centerLeft,
        child: Texts.textMedium("${comment.date ?? ""} ${comment.time ?? ""}", size: 10,color: Colors.black45),
      ),
      ],
    ),
  );
}
