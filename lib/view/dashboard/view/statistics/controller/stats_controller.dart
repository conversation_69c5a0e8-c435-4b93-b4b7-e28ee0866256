import 'package:fl_chart/fl_chart.dart';
import 'package:get/get.dart';
import 'package:padel_payer/core/constants/api_endpoints.dart';
import 'package:padel_payer/core/services/http_service.dart';
import 'package:padel_payer/view/dashboard/view/statistics/model/comments_model.dart';

import '../../../../../controller/user_controller.dart';
import '../model/stats_model.dart';

class PlayerStatsController extends GetxController {
  final selectedTabIndex = 0.obs;
  final statsList = <Stats>[].obs;
 var overallRating=5.78.obs;
  // Add rating system toggle
  final ratingSystem = 'Playtomic'.obs;
  var ratingSpots = <FlSpot>[].obs;
  RxBool isDummyData = false.obs;
  RxBool isLoading = false.obs;

  // Add comments-related properties
  final comments = <Comment>[].obs;
  RxBool isLoadingComments = false.obs;
var fileId="".obs;
  var ratingSpotsDummy = <FlSpot>[
    FlSpot(0, 5.0),
    FlSpot(1, 5.2),
    FlSpot(2, 6.0),
    FlSpot(3, 5.8),
    FlSpot(4, 6.1),
    FlSpot(5, 6.3),
    FlSpot(6, 6.07),
  ].obs;
  @override
  void onInit() {
    super.onInit();
    isDummyData.value = false;
    final userController = Get.find<UserController>();
    ratingSystem.value = userController.userModel?.ratingType ?? 'Playtomic';

    getPlayerStats();

    // Load dummy data
    // loadDummyData();
  }

  void loadDummyData() {
    isDummyData.value = true;overallRating.value=5.78;
    statsList.value = [
      Stats(
          label: "HITS".tr,
          total: 60,
          success: 40,
          winner: 10,
          loss: 20,
          successRate: 0.67),
      Stats(
          label: "FOREHAND".tr,
          total: 45,
          success: 30,
          winner: 12,
          loss: 15,
          successRate: 0.67),
      Stats(
          label: "BACKHAND".tr,
          total: 22,
          success: 8,
          winner: 3,
          loss: 14,
          successRate: 0.36),
      Stats(
          label: "CROSS COURT".tr,
          total: 55,
          success: 42,
          winner: 15,
          loss: 13,
          successRate: 0.76),
      Stats(
          label: "DOWN THE LINE".tr,
          total: 48,
          success: 18,
          winner: 6,
          loss: 30,
          successRate: 0.38),
      Stats(
          label: "FROM BASE AREA".tr,
          total: 30,
          success: 20,
          winner: 5,
          loss: 10,
          successRate: 0.67),
      Stats(
          label: "FROM NET AREA".tr,
          total: 72,
          success: 56,
          winner: 20,
          loss: 16,
          successRate: 0.78),
      Stats(
          label: "FROM TRANSITION AREA".tr,
          total: 35,
          success: 16,
          winner: 6,
          loss: 19,
          successRate: 0.46),
      Stats(
          label: "SMACH".tr,
          total: 20,
          success: 18,
          winner: 14,
          loss: 2,
          successRate: 0.90),
      Stats(
          label: "VOLLEY".tr,
          total: 58,
          success: 42,
          winner: 20,
          loss: 16,
          successRate: 0.72),
      Stats(
          label: "GROUND STROKE".tr,
          total: 50,
          success: 44,
          winner: 18,
          loss: 6,
          successRate: 0.88),
      Stats(
          label: "LOB".tr,
          total: 25,
          success: 22,
          winner: 17,
          loss: 3,
          successRate: 0.88),
      Stats(
          label: "VIBORA".tr,
          total: 32,
          success: 25,
          winner: 15,
          loss: 7,
          successRate: 0.78),
      Stats(
          label: "BANDEJA".tr,
          total: 28,
          success: 20,
          winner: 8,
          loss: 8,
          successRate: 0.71),
      Stats(
          label: "SERVE".tr,
          total: 40,
          success: 24,
          winner: 14,
          loss: 16,
          successRate: 0.60),
      Stats(
          label: "CHIQUITA".tr,
          total: 18,
          success: 12,
          winner: 5,
          loss: 6,
          successRate: 0.67),
      Stats(
          label: "WALL SHOT".tr,
          total: 30,
          success: 22,
          winner: 10,
          loss: 8,
          successRate: 0.73),
      Stats(
          label: "DROP SHOT".tr,
          total: 15,
          success: 12,
          winner: 8,
          loss: 3,
          successRate: 0.80),
      Stats(
          label: "DISTANCE".tr,
          total: 45,
          success: 30,
          winner: 12,
          loss: 15,
          successRate: 0.67),
      Stats(
          label: "BASE AREA %".tr,
          total: 20,
          success: 20,
          winner: 2,
          loss: 0,
          successRate: 0.90),
      Stats(
          label: "TRANSITION AREA %".tr,
          total: 24,
          success: 16,
          winner: 4,
          loss: 8,
          successRate: 0.67),
      Stats(
          label: "NET AREA %".tr,
          total: 62,
          success: 22,
          winner: 40,
          loss: 40,
          successRate: 0.35),
    ];ratingSpots.value = ratingSpotsDummy;
  } // Example rating progress: each FlSpot(x, y) where x can be match index or timestamp

  getPlayerStats() async {
    try {
      isLoading.value = true;
      final response = await ApiService.getData(Endpoints.getPlayerStats);
      isLoading.value = false;
      if (response.status == true) {
        statsList.clear();
        statsList.value = (response.data['stats'] as List)
            .map((item) => Stats.fromJson(item))
            .toList();
        if (statsList.isEmpty) {
          loadDummyData();
        } else {
          isDummyData.value = false;
          // Extract overall rating
          overallRating.value = double.parse(response.data['overall_rating']!=null?response.data['overall_rating'].toString():"0.0");

          if (response.data['daily_ratings'] != null) {
            final dailyRatings = response.data['daily_ratings'] as List;

            ratingSpots.value = dailyRatings.asMap().entries.map((entry) {
              final index = entry.key ?? 0;
              final rating = entry.value['rating'] ?? 0;
              return FlSpot(double.parse(index.toString()), double.parse(rating.toString()));
            }).toList();



      }}} else {
        // Widgets.showSnackBar("alert".tr, response.message.toString());
      }
    } catch (e) {print(e);
      isLoading.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  getGameStats() async {
    try {
      isLoading.value = true;
      final response = await ApiService.getData(Endpoints.getLastGameStats);
      isLoading.value = false;
      if (response.status == true) {
        statsList.clear();
        statsList.value = (response.data['stats'] as List)
            .map((item) => Stats.fromJson(item))
            .toList();
        if (statsList.isEmpty) {
          loadDummyData();fileId.value="";
        } else {
          isDummyData.value = false;
          fileId.value=response.data['game_id'].toString();
        }

      } else {
        // Widgets.showSnackBar("alert".tr, response.message.toString());
      }
    } catch (e) {
      // Widgets.hideLoader();
      // Widgets.showSnackBar("error".tr, e.toString());
    } finally {
      isLoading.value = false;
    }
  }



  // Add method to fetch player comments
  Future<void> getPlayerComments() async {
    try {
      isLoadingComments.value = true;
      final response = await ApiService.getData(selectedTabIndex==0?Endpoints.getPlayerComments:Endpoints.getPlayerComments+"/$fileId");

      if (response.status == true) {
        comments.clear();
        if (response.data['comments'] != null) {
          comments.value = (response.data['comments'] as List)
              .map((item) => Comment.fromJson(item))
              .toList();
        }

        if (comments.isEmpty) {
          loadDummyComments();
        }
      } else {
        // Handle error
        // Widgets.showSnackBar("alert".tr, response.message.toString());
      }
    } catch (e) {
      // Handle exception
      // Widgets.showSnackBar("error".tr, e.toString());
    } finally {
      isLoadingComments.value = false;
    }
  }

  // Add method to load dummy comments if needed
  void loadDummyComments() {
    comments.value = [
      // Comment(
      //   id: 1,
      //   date: "Coach Mike",
      //   m: "Your backhand technique has improved significantly. Keep working on your footwork.",
      //   createdAt: "2 days ago"
      // ),
      // Comment(
      //   id: 2,
      //   author: "Sarah",
      //   content: "Great game yesterday! Your serve was on point.",
      //   createdAt: "1 week ago"
      // ),
      // Comment(
      //   id: 3,
      //   author: "John",
      //   content: "Need to work on your volleys at the net. Let's practice on Thursday.",
      //   createdAt: "2 weeks ago"
      // ),
    ];
  }
}
