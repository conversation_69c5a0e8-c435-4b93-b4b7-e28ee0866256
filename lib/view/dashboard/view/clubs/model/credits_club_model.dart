class CreditsClub {
  String? playerId;
  String? clubId;
 String? credits;
  String? updatedAt;
  String? createdAt;
  String? id;
  ClubX? club;String? ip;int? portNo;

  CreditsClub(
      {this.playerId,
        this.clubId,this.ip,this.portNo,
        this.credits,
        this.updatedAt,
        this.createdAt,
        this.id,
        this.club});

  CreditsClub.fromJson(Map<String, dynamic> json) {
    playerId = json['player_id'];
    clubId = json['club_id'];
    ip = json['ip'];
    portNo = json['port'];
    credits = json['credits'].toString();
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id'];
    club = json['club'] != null ? new ClubX.fromJson(json['club']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['player_id'] = this.playerId;
    data['club_id'] = this.clubId;data['ip'] = this.ip;data['port'] = this.portNo;
    data['credits'] = this.credits;
    data['updated_at'] = this.updatedAt;
    data['created_at'] = this.createdAt;
    data['id'] = this.id;
    if (this.club != null) {
      data['club'] = this.club!.toJson();
    }
    return data;
  }
}

class ClubX {
  String? clubId;
  String? clubName;
  String? country;String? city;
  String? id;

  ClubX({this.clubId, this.clubName, this.country, this.id,this.city});

  ClubX.fromJson(Map<String, dynamic> json) {
    clubId = json['club_id'];
    clubName = json['club_name'];
    country = json['country'];city = json['city'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['club_id'] = this.clubId;
    data['club_name'] = this.clubName;
    data['country'] = this.country;
    data['id'] = this.id;
    return data;
  }
}
