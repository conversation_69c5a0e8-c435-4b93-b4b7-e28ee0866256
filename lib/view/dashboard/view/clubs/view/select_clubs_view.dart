import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:padel_payer/view/dashboard/view/clubs/model/club_model.dart';

import '../controller/clubs_controller.dart';
import '../../../../../core/constants/assets_constants.dart';
import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/utils/utils.dart';
import '../../../../../core/widgets/entry_field.dart';
import '../../../../../core/widgets/shimmer_effect.dart';
import '../../../../../core/widgets/text_widgets.dart';
import '../../../../../core/widgets/widgets.dart';
import '../../tokens/controller/tokens_controller.dart';

class SelectClubsVIew extends StatefulWidget {
  const SelectClubsVIew({super.key});

  @override
  State<SelectClubsVIew> createState() => _SelectClubsVIewState();
}

class _SelectClubsVIewState extends State<SelectClubsVIew>
    with SingleTickerProviderStateMixin {
  late ClubsController controller;

  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    controller = Get.put(ClubsController());

    controller.isClubsLoading.value =
        controller.isClubsMoreLoading.value = false;
    controller.fetchClubs(page: 1);
    scrollController.addListener(scrollListener);
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  scrollListener() {
    if (scrollController.position.pixels ==
            scrollController.position.maxScrollExtent &&
        controller.totalClubs.value > controller.clubs.length) {
      controller.fetchClubs(page: controller.currentClubsPage.value + 1);
      controller.currentClubsPage.value++;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: ColorConstants.blackColor),
          onPressed: () => Get.back(),
        ),
        title: Texts.textBold(
          "select_club".tr,
          color: ColorConstants.blackColor,
          size: 18,
        ),
      ),
      body: buildClubsList(),
    );
  }

  Widget buildClubsList() {
    return Obx(
      () {
        return Scrollbar(
          child: ListView(
            controller: scrollController,
            padding: const EdgeInsets.symmetric(horizontal: 15),
            children: [
              EntrySearchField(
                controller: controller.searchController,
                prefixIcon: Assets.searchIcon,
                hint: "search_clubs".tr,
                suffixIcon: Assets.filterIcon,
                onChange: (value) {
                  if (controller.searchDebounce?.isActive ?? false) {
                    controller.searchDebounce?.cancel();
                  }
                  controller.searchDebounce =
                      Timer(const Duration(milliseconds: 500), () {
                    controller.fetchClubs();
                  });
                },
              ),
              Widgets.heightSpaceH2,
              controller.isClubsLoading.value
                  ? const ShimmerListSkeleton()
                  : controller.clubs.isNotEmpty
                      ? ListView.separated(
                          physics: const NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            Club? club = controller.clubs[index];
                            return InkWell(
                              onTap: () {
                                Get.back(result: club);
                              },
                              child: Container(
                                padding: EdgeInsets.all(12),
                                decoration: Widgets.cardBoxDecoration,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Texts.textBold(
                                            club?.clubName ?? "",
                                            color: ColorConstants.blackColor,
                                            size: 16,
                                          ),
                                          Widgets.heightSpaceH05,
                                          Texts.textNormal(
                                           "${club.city ?? ""}, ${club?.country ?? ""}",
                                            color: Colors.black,
                                            size: 10,
                                          ),
                                        ],
                                      ),
                                    ),
                                    Column(
                                      children: [
                                        InkWell(
                                            onTap: () {
                                              controller.toggleFavorite(club);
                                            },
                                            child: Icon(size: 30,
                                              club.isFavorited==true
                                                ? Icons.star
                                                : Icons.star_border,
                                              color: ColorConstants.primaryColor,
                                            )),

                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          separatorBuilder: (context, index) {
                            return Widgets.heightSpaceH1;
                          },
                          itemCount: controller.clubs.length ?? 0,
                        )
                      : Widgets.noRecordsFound(title: "no_clubs".tr),
              if (controller.isClubsMoreLoading.value) Widgets.moreLoading(),
            ],
          ),
        );
      },
    );
  }
}
