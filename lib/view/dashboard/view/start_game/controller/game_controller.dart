import 'dart:async';

import 'package:barcode_scan2/platform_wrapper.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:padel_payer/view/dashboard/view/clubs/model/credits_club_model.dart';
import 'package:padel_payer/view/dashboard/view/start_game/view/credits_selection_view.dart';

import '../../../../../controller/user_controller.dart';
import '../../../../../core/constants/api_endpoints.dart';
import '../../../../../core/services/http_service.dart';
import '../../../../../core/widgets/widgets.dart';
import '../view/progress_view.dart';

class GameController extends GetxController {
  TextEditingController cameraIDController = TextEditingController();
  RxInt selectedCourt = RxInt(-1); // -1 means no selection
  RxInt selectedCredits = 60.obs;
  final int availableCr =
      100; // Replace with actual available credits from your system
  var selectedCamera = CreditsClub().obs;
  void incrementCredits() {
    int availableCredits = (int.parse(selectedCamera.value.credits ?? "0") +
        int.parse(Get.find<UserController>().userModel?.trialCredits ?? "0"));
    print(availableCredits);
    if (selectedCredits.value + 30 <= availableCredits) {
      selectedCredits.value += 30;
    } else {
      Widgets.showSnackBar("alert".tr, "You don't have enough credits".tr);
    }
  }

  void decrementCredits() {
    if (selectedCredits.value > 60) {
      selectedCredits.value -= 30;
    }
  }

  void selectCourt(int courtNumber) {
    selectedCourt.value = courtNumber;
  }

  void startGame() {
    if (selectedCourt.value != -1) {
      // Add your game start logic here
      // For example, navigate to next screen
    }
  }

  scanProduct() async {
    try {
      var result = await BarcodeScanner.scan();

      if (result.rawContent.isNotEmpty) {
        cameraIDController.text = result.rawContent;
        update();
      }
    } catch (e) {
      print(e);
    }
  }

  validateCamera() async {
    if (cameraIDController.text.isEmpty) {
      Widgets.showSnackBar("alert".tr, "Enter Camera ID or scan ".tr);
      return;
    }
    Widgets.showLoader("loading".tr);

    try {
      var payload = {"camera_id": cameraIDController.text};
      var response = await ApiService.postData(Endpoints.checkCamera, payload);

      if (response.status == true) {
        selectedCamera.value = CreditsClub.fromJson(response.data['credits']);
        selectedCamera.value.ip = response.data['camera']['ip'];
        selectedCamera.value.portNo = response.data['camera']['port'];
        var result = await ApiService.postData2(
            Endpoints.checkCameraReachable, {
          "port": selectedCamera.value.portNo,
          "ip": selectedCamera.value.ip
        });
        Widgets.hideLoader();
        if (result.status == true) {
          if (result.data['reachable'] == true) {
            Widgets.showSnackBar("alert".tr, "Camera is not reachable".tr);
            return;
          }
          Get.to(() => const CreditsSelectionView());
        } else {
          Widgets.showSnackBar("alert".tr, "Something went wrong".tr);
        }
      } else {
        Widgets.showSnackBar("alert".tr, response.message.toString());
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, e.toString());
    } finally {
      Widgets.hideLoader();
    }
  }

  submitRecording() async {
    if (cameraIDController.text.isEmpty) {
      Widgets.showSnackBar("alert".tr, "Enter Camera ID or scan ".tr);
      return;
    }
    Widgets.showLoader("loading".tr);

    try {
      var payload = {
        "camera_id": cameraIDController.text,
        "warm_area": selectedCourt.value,
        "club_id": selectedCamera.value.club?.clubId,
        "credits": selectedCredits.value
      };
      var response =
          await ApiService.postData(Endpoints.saveRecording, payload);

      Widgets.hideLoader();
      if (response.status == true) {
     await handshakeAi(response.data['recording']['start_time']??"",response.data['recording']['end_time']??"",response.data['recording']['date']??"",response.data['is_first_recording']??false,response.data['recording']['id'].toString());
      } else {
        Widgets.showSnackBar("alert".tr, response.message.toString());
      }
    } catch (e) {
      Widgets.hideLoader();
      Widgets.showSnackBar("error".tr, e.toString());
    }
  }
  deleteRecording(String recordingId) async {

    Widgets.showLoader("loading".tr);

    try {
      var payload = {
        "recording_id": recordingId
      };
      var response =
      await ApiService.postData(Endpoints.deleteRecording, payload);

      Widgets.hideLoader();
      if (response.status == true) {Get.back();Widgets.showSnackBar("alert".tr, "Something went wrong");
      } else {Get.back();
        Widgets.showSnackBar("alert".tr, response.message.toString());
      }
    } catch (e) {
      Widgets.hideLoader();Get.back();
      Widgets.showSnackBar("error".tr, e.toString());
    }
  }
  handshakeAi(String startTime, String endTime,String date,bool action,String? recordingId) async {

    Widgets.showLoader("loading".tr);

    try {
      var payload = {"input": {

        "action": action==true?"start_game":"join_game","date": date,
        "user_id":Get.find<UserController>().userModel?.playerId,
        "start_time":startTime,
        "end_time":endTime,
        "sex":Get.find<UserController>().userModel?.sex=="Male"?"M":"F",
        "warm_up_area":selectedCourt.value,
        "camera_id": cameraIDController.text
      }

      };
      var result = await ApiService.postData3(
           payload);
      Widgets.hideLoader();
      if (result.status == true) {
        Get.to(() => ProgressScreen());
      } else {deleteRecording(recordingId!);
      }
    } catch (e) {
      Widgets.hideLoader();deleteRecording(recordingId!);
      Widgets.showSnackBar("error".tr, e.toString());
    }
  }
  void onButtonTap(int buttonNumber) {
    print('Button $buttonNumber tapped');
    // You can perform any action based on buttonNumber
  }

  void onCameraTap() {
    print('Camera button tapped');
    // Handle camera button action
  }

  var minutesCompleted = 0.obs; // observable
  var elapsedSeconds = 0.obs; // track in seconds
  Timer? _timer;

  double get progressPercentage =>
        (elapsedSeconds.value / (selectedCredits.value * 60)) * 100;

  double get progressValue => elapsedSeconds.value / (selectedCredits.value * 60);

  void startTimer() {
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (elapsedSeconds.value >= selectedCredits.value  * 60) {
        timer.cancel();
      } else {
        elapsedSeconds.value++;
      }
    });
  }

  @override
  void onClose() {
    _timer?.cancel();
    super.onClose();
  }
}
