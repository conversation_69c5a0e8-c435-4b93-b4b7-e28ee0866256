import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:padel_payer/core/widgets/text_widgets.dart';
import 'package:padel_payer/view/dashboard/view/start_game/view/warming_area_selection_view.dart';

import '../../../../../core/constants/assets_constants.dart';
import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/widgets.dart';
import '../controller/game_controller.dart';

class GameIntructionsView extends StatelessWidget {

GameController controller = Get.find();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(15.0),
          child:  CustomButton(
            backgroundColor: ColorConstants.primaryColor,

            label: "continue".tr,
            onTap:(){
              controller.submitRecording();
            }


          )),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: ColorConstants.blackColor),
          onPressed: () => Get.back(),
        ),
        title: Texts.textBold(
          "game_instructions".tr,
          color: ColorConstants.blackColor,
          size: 18,
        ),
      ),body: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Column(
          children: [
            Center(child: Image.asset(Assets.instructionIcon, height: .50.sh)),
            Widgets.heightSpaceH2,
            Texts.textNormal(
              "warming_instructions".tr,
              size: 14,
            ),
          ],
        ),
      )
    );
  }
}
