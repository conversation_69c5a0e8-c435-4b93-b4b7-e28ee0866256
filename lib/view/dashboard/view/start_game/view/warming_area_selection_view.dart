import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:padel_payer/view/dashboard/view/start_game/view/instructions_view.dart';

import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../../../core/widgets/entry_field.dart';
import '../../../../../core/widgets/text_widgets.dart';
import '../../../../../core/widgets/widgets.dart';
import '../../../controller/dashboard_controller.dart';
import '../controller/game_controller.dart';

class WarmupSelectionView extends StatelessWidget {
  WarmupSelectionView({super.key});

  final GameController controller = Get.find<GameController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(15.0),
        child: Obx(() => CustomButton(
          backgroundColor: controller.selectedCourt.value != -1 ? ColorConstants.primaryColor : Colors.grey,
          label: "start_game".tr,
          onTap: controller.selectedCourt.value != -1
              ? () {
                Get.to(() => GameIntructionsView());
              }
              : null,
        )),
      ),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: ColorConstants.blackColor),
          onPressed: () => Get.back(),
        ),
        title: Texts.textBold(
          "court_selection".tr,
          color: ColorConstants.blackColor,
          size: 18,
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          children: [
            GridView.count(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
              crossAxisCount: 2,physics: const NeverScrollableScrollPhysics(),
              crossAxisSpacing: 15,shrinkWrap: true,
              mainAxisSpacing: 15,
              childAspectRatio: .8,
              children: List.generate(4, (index) {
                final courtNumber = index + 1;

                return Obx(() {
                  final isSelected = controller.selectedCourt.value == courtNumber;

                  return GestureDetector(
                    onTap: () => controller.selectCourt(courtNumber),
                    child: Container(
                      padding: const EdgeInsets.all(30),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: isSelected
                              ? ColorConstants.primaryColor
                              : Colors.grey.shade200,
                          width: isSelected ? 2 : 0.7,
                        ),
                        color: isSelected
                            ? Colors.blue.shade100
                            : Colors.blue.shade50,
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Colors.green.shade400
                              : Colors.green.shade200,
                          shape: BoxShape.circle,
                        ),
                        child: Text(
                          '$courtNumber',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: isSelected
                                ? Colors.white
                                : Colors.black,
                          ),
                        ),
                      ),
                    ),
                  );
                });
              }),
            ),Widgets.heightSpaceH2,
            GestureDetector(
              onTap: controller.onCameraTap,
              child: Container(
                margin: const EdgeInsets.only(bottom: 20),
                width: 80,
                height: 80,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                alignment: Alignment.center,
                child: Text(
                  'camera'.tr,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
