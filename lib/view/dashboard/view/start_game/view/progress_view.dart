import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controller/game_controller.dart';
import '../../../../../core/constants/color_constants.dart';
import '../../../../../core/widgets/custom_button.dart';
import '../../../view/dashboard_view.dart';

class ProgressScreen extends StatelessWidget {
  final GameController controller = Get.find(); // for 30 mins

  @override
  Widget build(BuildContext context) {
    controller.startTimer(); // Start counting automatically

    return WillPopScope(
      onWillPop: () async {
        Get.offAll(() => DashboardView());
        return false;
      },
      child: Scaffold(
        appBar: AppBar(backgroundColor: Colors.white,
          title: Text(""),
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              Get.offAll(() => DashboardView());
            },
          ),
        ),
        body: Obx(() {if (controller.progressPercentage >= 100) {
          Future.microtask(() => Get.offAll(() => DashboardView()));
        }
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(

              children: [ SizedBox(height: 20),
                Image.asset("assets/images/padel.gif", height: .35.sh),
                SizedBox(height: 20),
                Text(
                  "${(controller.elapsedSeconds.value / 60).toStringAsFixed(1)} / ${controller.selectedCredits.value} minutes",
                  style: TextStyle(fontSize: 22, color: Colors.black),
                ),
                SizedBox(height: 30),

                LinearProgressIndicator(
                  value: controller.progressValue,
                  minHeight: 20,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blueAccent),
                  backgroundColor: Colors.blue.withOpacity(0.2),
                ),
                SizedBox(height: 12),

                /// 🔵 Progress Percentage
                Text(
                  "${controller.progressPercentage.toStringAsFixed(1)}%",
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600,color: ColorConstants.blackColor),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}
