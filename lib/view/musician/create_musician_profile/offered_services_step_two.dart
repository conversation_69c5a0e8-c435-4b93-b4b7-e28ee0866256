import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/widgets/choice_chip_widget.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';

import '../../../core/constants/constants_list.dart';

class OfferedServicesController extends GetxController {
  RxList<String> selectedServices = <String>[].obs;

  void toggleService(String service) {
    if (selectedServices.contains(service)) {
      selectedServices.remove(service);
    } else {
      selectedServices.add(service);
    }
  }

  bool isSelected(String service) {
    return selectedServices.contains(service);
  }
}

class OfferedServices extends StatelessWidget {
  final Function(List<String>) onServicesSelected;

  OfferedServices({Key? key, required this.onServicesSelected}) : super(key: key);

  final OfferedServicesController controller = Get.put(OfferedServicesController());

  @override
  Widget build(BuildContext context) {
    // Update parent controller whenever selection changes
    ever(controller.selectedServices, (services) {
      onServicesSelected(services);
    });

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Texts.textBold("What Services Do You Offer?", size: 20),

          Texts.textNormal(
            "Select all that apply",
            color: ColorConstants.greyTextColor,size: 12,
          ),
          Widgets.heightSpaceH3,
          Expanded(
            child: ListView.builder(
              itemCount: Data.offeredServices.length,
              itemBuilder: (context, index) {
                final service = Data.offeredServices[index];
                return Obx(() => _buildServiceTile(service));
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceTile(String service) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: InkWell(
        onTap: () => controller.toggleService(service),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: controller.isSelected(service)
                ? ColorConstants.redColor
                : Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: controller.isSelected(service)
                  ? ColorConstants.redColor
                  : ColorConstants.grayBorderColor,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(child:Texts.textNormal(textAlign: TextAlign.start,
                "$service",size: 14,
                color: controller.isSelected(service)
                    ? ColorConstants.whiteColor
                    : ColorConstants.blackColor,
              )),
              if (controller.isSelected(service))
                Icon(
                  Icons.check_circle,
                  color: ColorConstants.whiteColor,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }
}


