import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/utils/extensions.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/core/widgets/entry_field.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/view/musician/create_musician_profile/location_selection/location_selection_controller.dart';

class LocationSelectionView extends StatelessWidget {
  final Function(String, double, double) onLocationSelected;

  LocationSelectionView({Key? key, required this.onLocationSelected})
      : super(key: key);

  final LocationSelectionController controller =
      Get.put(LocationSelectionController());

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        context.hideKeyboard();
      },
      child: Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Texts.textBold("Select Location", size: 18),
          backgroundColor: ColorConstants.whiteColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back, color: ColorConstants.blackColor),
            onPressed: () => Get.back(),
          ),
        ),
        body: Column(
          children: [
            // Search bar
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 0),
              color: ColorConstants.whiteColor,
              child: EntrySearchField(
                controller: controller.searchController,
                prefixIcon: Assets.redSearchIcon,
                color: ColorConstants.redColor,
                suffixIcon: Assets.clearIcon,
                hint: "Search location",
                onTrailingTap: () {
                  controller.placeList.clear();
                },
                onChange: (value) {
                  if (value != null && value.isNotEmpty) {
                    controller.searchPlaces(value);
                  }
                },
              ),
            ),

            // Search results or map
            Expanded(
              child: Obx(() => controller.placeList.isNotEmpty
                  ? _buildSearchResults()
                  : _buildMap()),
            ),

            // Bottom bar with selected location
            Obx(() => controller.selectedLocation.value.isNotEmpty
                ? Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: ColorConstants.whiteColor,
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20),
                          topRight: Radius.circular(20)),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Widgets.heightSpaceH1,
                        Row(
                          children: [
                            Expanded(
                              child: EntryField(
                                controller: TextEditingController(
                                  text: controller.selectedLocation.value
                                ),
                                hint: "Edit location",
                                label: "Location",
                                onChange: (value) {
                                  if (value != null) {
                                    controller.selectedLocation.value = value;
                                  }
                                },
                              ),
                            ),
                          ],
                        ),
                        Widgets.heightSpaceH2,
                        CustomButton(
                          label: "Confirm Location",
                          onTap: () {
                            onLocationSelected(
                              controller.selectedLocation.value,
                              controller.selectedLatitude.value,
                              controller.selectedLongitude.value,
                            );
                            Get.back();
                          },
                          width: double.infinity,
                        ),
                      ],
                    ),
                  )
                : SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  Widget _buildMap() {
    return Stack(
      children: [
        Obx(() => GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(
                  controller.selectedLatitude.value != 0
                      ? controller.selectedLatitude.value
                      : 37.42796133580664,
                  controller.selectedLongitude.value != 0
                      ? controller.selectedLongitude.value
                      : -122.085749655962,
                ),
                zoom: 14,
              ),
              onMapCreated: controller.onMapCreated,
              markers: controller.markers,
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
              onTap: controller.onMapTap,
              onCameraMove: controller.onCameraMove,
              onCameraIdle: controller.onCameraIdle,
            )),

        // Center pin that stays fixed
        Center(
          child: Padding(
            padding:
                EdgeInsets.only(bottom: 24), // Offset for marker anchor point
            child: Icon(
              Icons.location_pin,
              color: ColorConstants.redColor,
              size: 40,
            ),
          ),
        ),

        // "Drag to set location" hint
        Positioned(
          top: 16,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: ColorConstants.blackColor.withOpacity(0.7),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Texts.textNormal(
                "Drag the map to set location",
                color: ColorConstants.whiteColor,
                size: 14,
              ),
            ),
          ),
        ),

        // Use current location button
        Positioned(
          bottom: 16,
          right: 16,
          child: FloatingActionButton(
            heroTag: "currentLocationBtn",
            backgroundColor: ColorConstants.whiteColor,
            onPressed: controller.getCurrentLocation,
            child: Icon(
              Icons.my_location,
              color: ColorConstants.blackColor,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSearchResults() {
    return Container(
      color: ColorConstants.whiteColor,
      child: Obx(
        () => ListView.separated(
          itemCount: controller.placeList.length,
          separatorBuilder: (context, index) =>
              Divider(height: .2, thickness: .2),
          itemBuilder: (context, index) {
            debugPrint("placeList.length${controller.placeList.length}");
            final place = controller.placeList[index];
            return ListTile(
              dense: true,
              horizontalTitleGap: 2,
              leading: Icon(Icons.location_on, color: ColorConstants.redColor),
              title: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Texts.textNormal(
                        controller.placeList[index]["description"] ?? "",
                        size: 12,
                        textAlign: TextAlign.start),
                    SizedBox(height: 2),
                  ]),
              onTap: () {
                context.hideKeyboard();
                controller.getLatLongFromAutocompletePrediction(
                    controller.placeList[index]['place_id'],
                    controller.placeList[index]["description"]);
              },
            );
          },
        ),
      ),
    );
  }
}
