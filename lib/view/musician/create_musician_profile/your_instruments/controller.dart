import 'package:get/get.dart';

class InstrumentsController extends GetxController {
  // Legacy support for index-based selection if needed
  RxList<bool> buttonStates = List.generate(50, (index) => false).obs;

  // New approach using instrument names directly
  RxList<String> selectedInstruments = <String>[].obs;

  void toggleButton(int index) {
    buttonStates[index] = !buttonStates[index];
    update();
  }

  void toggleInstrument(String instrument) {
    if (selectedInstruments.contains(instrument)) {
      selectedInstruments.remove(instrument);
    } else {
      selectedInstruments.add(instrument);
    }
  }

  bool isSelected(String instrument) {
    return selectedInstruments.contains(instrument);
  }

  // Get all selected instruments as a list
  List<String> getSelectedInstruments() {
    return selectedInstruments.toList();
  }
}
