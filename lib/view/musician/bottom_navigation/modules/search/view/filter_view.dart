import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/constants/padding_constants.dart';
import 'package:music_app/core/utils/extensions.dart';
import 'package:music_app/core/widgets/choice_chip_widget.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/core/widgets/entry_field.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';

import '../../../../../../core/constants/constants_list.dart';
import '../../../../create_musician_profile/location_selection/location_selection_screen.dart';
import '../controller/search_controller.dart';
import 'location_filter_view.dart';

class FilterView extends StatelessWidget {
  FilterView({super.key});
  final SearchMemberController controller = Get.find<SearchMemberController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 30,
        centerTitle: true,
        scrolledUnderElevation: 0,
        title: Texts.textBold("Filters", size: 22),
        leading: const BackButton(),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Padding(
        padding: PaddingConstants.screenPaddingHalf.copyWith(bottom: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Scrollbar(
                child: ListView(
                  children: [
                    Texts.textBold("Location",
                        size: 15, textAlign: TextAlign.start),
                    Widgets.heightSpaceH05,
                    EntryField(
                        onTrailingTap: () { context.hideKeyboard();
                        Get.to(() => LocationFilterView(
                          onLocationSelected: (location, lat, lng, radius) {
                            controller.locationController.text = location;
                            controller.selectedLocationLat.value = lat.toString();
                            controller.selectedLocationLong.value = lng.toString();
                            controller.selectedRadius.value = radius;
                          },
                        ));},
                        readOnly: true,
                        controller: controller.locationController,
                        onTap: () {
                          context.hideKeyboard();
                          Get.to(() => LocationFilterView(
                            onLocationSelected: (location, lat, lng, radius) {
                              controller.locationController.text = location;
                              controller.selectedLocationLat.value = lat.toString();
                              controller.selectedLocationLong.value = lng.toString();
                              controller.selectedRadius.value = radius;
                            },
                          ));
                        },
                        hint: "Enter your location",
                        suffixIcon: Icons.gps_fixed),
                    Widgets.heightSpaceH2,


          Texts.textBold("Roles",
                        size: 15, textAlign: TextAlign.start),
                    Widgets.heightSpaceH1,
                    Obx(() => Wrap(
                          alignment: WrapAlignment.start,
                          spacing: 8,
                          children: Data.roles.map((interest) {
                            bool isSelected =
                                controller.selectedRoles.contains(interest);

                            return ChoiceChip(
                              label: Text(interest,
                                  style: TextStyle(
                                      fontFamily: "PoppinsRegular",
                                      fontSize: 12,
                                      color: isSelected
                                          ? ColorConstants.whiteColor
                                          : Colors.black87,
                                      fontWeight: FontWeight.w500)),
                              selected:
                                  controller.selectedRoles.contains(interest),
                              onSelected: (selected) {
                                controller.toggleRoles(interest);
                              },
                              showCheckmark: false,
                              selectedColor: ColorConstants.redColor,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(10), // Rounded edges
                                side: isSelected
                                    ? BorderSide(
                                        color: ColorConstants.redColor,
                                        width: 1) // Highlight selected
                                    : BorderSide(
                                        color: ColorConstants.grayBorderColor),
                              ),
                              backgroundColor: ColorConstants.whiteColor,
                            );
                          }).toList(),
                        )),

                    Widgets.heightSpaceH2,
                    Texts.textBold("Instruments",
                        size: 15, textAlign: TextAlign.start),
                    Widgets.heightSpaceH1,
                    Obx(() => Wrap(
                          alignment: WrapAlignment.start,
                          spacing: 8,
                          children: Data.instruments.map((interest) {
                            bool isSelected = controller.selectedInstruments
                                .contains(interest);

                            return ChoiceChip(
                              label: Text(interest,
                                  style: TextStyle(
                                      fontFamily: "PoppinsRegular",
                                      fontSize: 12,
                                      color: isSelected
                                          ? ColorConstants.whiteColor
                                          : Colors.black87,
                                      fontWeight: FontWeight.w500)),
                              selected: controller.selectedInstruments
                                  .contains(interest),
                              onSelected: (selected) {
                                controller.toggleInstruments(interest);
                              },
                              showCheckmark: false,
                              selectedColor: ColorConstants.redColor,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(10), // Rounded edges
                                side: isSelected
                                    ? BorderSide(
                                        color: ColorConstants.redColor,
                                        width: 1) // Highlight selected
                                    : BorderSide(
                                        color: ColorConstants.grayBorderColor),
                              ),
                              backgroundColor: ColorConstants.whiteColor,
                            );
                          }).toList(),
                        )),
                    Widgets.heightSpaceH2,
                    Texts.textBold("Music Types",
                        size: 15, textAlign: TextAlign.start),
                    Widgets.heightSpaceH1,
                    Obx(() => Wrap(
                          alignment: WrapAlignment.start,
                          spacing: 8,
                          children: Data.musicTypes.map((interest) {
                            bool isSelected = controller.selectedMusicTypes
                                .contains(interest);

                            return ChoiceChip(
                              label: Text(interest,
                                  style: TextStyle(
                                      fontFamily: "PoppinsRegular",
                                      fontSize: 12,
                                      color: isSelected
                                          ? ColorConstants.whiteColor
                                          : Colors.black87,
                                      fontWeight: FontWeight.w500)),
                              selected: controller.selectedMusicTypes
                                  .contains(interest),
                              onSelected: (selected) {
                                controller.toggleMusicTypes(interest);
                              },
                              showCheckmark: false,
                              selectedColor: ColorConstants.redColor,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(10), // Rounded edges
                                side: isSelected
                                    ? BorderSide(
                                        color: ColorConstants.redColor,
                                        width: 1) // Highlight selected
                                    : BorderSide(
                                        color: ColorConstants.grayBorderColor),
                              ),
                              backgroundColor: ColorConstants.whiteColor,
                            );
                          }).toList(),
                        )),
                    Widgets.heightSpaceH2,
                    Texts.textBold("Offered Services",
                        size: 15, textAlign: TextAlign.start),
                    Widgets.heightSpaceH1,
                    Obx(() => Wrap(
                          alignment: WrapAlignment.start,
                          spacing: 8,
                          children: Data.offeredServices.map((interest) {
                            bool isSelected =
                                controller.selectedServices.contains(interest);

                            return ChoiceChip(
                              label: Text(interest,
                                  style: TextStyle(
                                      fontFamily: "PoppinsRegular",
                                      fontSize: 12,
                                      color: isSelected
                                          ? ColorConstants.whiteColor
                                          : Colors.black87,
                                      fontWeight: FontWeight.w500)),
                              selected: controller.selectedServices
                                  .contains(interest),
                              onSelected: (selected) {
                                controller.toggleServices(interest);
                              },
                              showCheckmark: false,
                              selectedColor: ColorConstants.redColor,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(10), // Rounded edges
                                side: isSelected
                                    ? BorderSide(
                                        color: ColorConstants.redColor,
                                        width: 1) // Highlight selected
                                    : BorderSide(
                                        color: ColorConstants.grayBorderColor),
                              ),
                              backgroundColor: ColorConstants.whiteColor,
                            );
                          }).toList(),
                        )),
                    Widgets.heightSpaceH2,

                    Texts.textBold("Spoken Languages",
                        size: 15, textAlign: TextAlign.start),
                    Widgets.heightSpaceH1,
                    Obx(() => Wrap(
                          alignment: WrapAlignment.start,
                          spacing: 8,
                          children: Data.languages.map((interest) {
                            bool isSelected =
                                controller.selectedLanguages.contains(interest);

                            return ChoiceChip(
                              label: Text(interest,
                                  style: TextStyle(
                                      fontFamily: "PoppinsRegular",
                                      fontSize: 12,
                                      color: isSelected
                                          ? ColorConstants.whiteColor
                                          : Colors.black87,
                                      fontWeight: FontWeight.w500)),
                              selected: controller.selectedLanguages
                                  .contains(interest),
                              onSelected: (selected) {
                                controller.toggleLanguages(interest);
                              },
                              showCheckmark: false,
                              selectedColor: ColorConstants.redColor,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(10), // Rounded edges
                                side: isSelected
                                    ? BorderSide(
                                        color: ColorConstants.redColor,
                                        width: 1) // Highlight selected
                                    : BorderSide(
                                        color: ColorConstants.grayBorderColor),
                              ),
                              backgroundColor: ColorConstants.whiteColor,
                            );
                          }).toList(),
                        )),
                    Widgets.heightSpaceH2,
                    Texts.textBold("Ratings",
                        size: 15, textAlign: TextAlign.start),
                    Widgets.heightSpaceH1,
                    Obx(() => Wrap(
                          alignment: WrapAlignment.start,
                          spacing: 8,
                          children: Data.ratings.map((interest) {
                            bool isSelected =
                                controller.selectedRatings.contains(interest);

                            return ChoiceChip(
                              label: Text(interest,
                                  style: TextStyle(
                                      fontFamily: "PoppinsRegular",
                                      fontSize: 12,
                                      color: isSelected
                                          ? ColorConstants.whiteColor
                                          : Colors.black87,
                                      fontWeight: FontWeight.w500)),
                              selected:
                                  controller.selectedRatings.contains(interest),
                              onSelected: (selected) {
                                controller.toggleRatings(interest);
                              },
                              showCheckmark: false,
                              selectedColor: ColorConstants.redColor,
                              shape: RoundedRectangleBorder(
                                borderRadius:
                                    BorderRadius.circular(10), // Rounded edges
                                side: isSelected
                                    ? BorderSide(
                                        color: ColorConstants.redColor,
                                        width: 1) // Highlight selected
                                    : BorderSide(
                                        color: ColorConstants.grayBorderColor),
                              ),
                              backgroundColor: ColorConstants.whiteColor,
                            );
                          }).toList(),
                        )),

                    //
                    // buildSection(
                    //     "Ratings",
                    //     ["1 Star", "2 Star", "3 Star", "4 Star", "5 Star"],
                    //     controller.ratingsStates,
                    //     controller.toggleRating),
                    Widgets.heightSpaceH2,
                  ],
                ),
              ),
            ),
            Row(
              children: [
                Expanded(
                  child: CustomButton(
                    label: "Clear Filters",
                    backgroundColor: ColorConstants.whiteColor,
                    borderColor: ColorConstants.blackColor,
                    textColor: ColorConstants.blackColor,
                    onTap: () {
                      controller.clearFilters();
                      Get.back();
                      controller.fetchMembers(
                          controller.searchFieldController.text,
                          page: 1);
                    },
                  ),
                ),
                Widgets.widthSpaceW3,
                Expanded(
                  child: CustomButton(
                    onTap: () {
                      controller.fetchMembers(
                          controller.searchFieldController.text,
                          page: 1);
                      Get.back();
                    },
                    label: "Search",
                    backgroundColor: ColorConstants.primaryColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
