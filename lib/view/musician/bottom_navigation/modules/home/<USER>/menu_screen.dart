import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/profile/view/change_password.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/profile/view/client/edit_profile_client_view.dart';
import 'package:music_app/view/musician/subscription/view/subscribe_view.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/constants/api_endpoints.dart';
import '../../../../../authentication/view/login_view.dart';
import '../../profile/view/profile_webview.dart';

class MenuScreen extends StatelessWidget {
  const MenuScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        scrolledUnderElevation: 0,
        title: Texts.textBold("Menu", size: 22),
        leadingWidth: 30,
        backgroundColor: Colors.white,
      ),
      body: GetBuilder<UserController>(
          init: UserController(),
          builder: (controller) {
            return SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  controller.token != null
                      ? controller.userModel?.role == "client"
                          ? GestureDetector(
                              onTap: () {
                                Get.to(() => ClientEditProfile());
                              },
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 8.0),
                                child: GetBuilder<UserController>(
                                    builder: (controller) {
                                  return Row(
                                    children: [
                                      AdvancedAvatar(
                                        animated: true,
                                        size: 55,
                                        foregroundDecoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          border: Border.all(
                                            color: Colors.grey,
                                            width: 0.0,
                                          ),
                                        ),
                                        child: controller.userModel?.imageUrl ==
                                                null
                                            ? Text(controller.userModel?.name
                                                    ?.substring(0, 1) ??
                                                "")
                                            : Widgets.networkImage(
                                                controller
                                                        .userModel?.imageUrl ??
                                                    "",
                                                width: 100,
                                                height: 100),
                                      ),
                                      Widgets.widthSpaceW2,
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Texts.textBold(
                                              controller.userModel?.name ?? "",
                                              size: 18,
                                              textAlign: TextAlign.start),
                                          SizedBox(
                                            height: 4,
                                          ),
                                          Texts.textUnderlineBlock(
                                              "Edit Profile",
                                              size: 12)
                                        ],
                                      ),
                                      Expanded(
                                        child: Widgets.widthSpaceW1,
                                      ),
                                      Icon(Icons.keyboard_arrow_right_rounded)
                                    ],
                                  );
                                }),
                              ),
                            )
                          : SizedBox()
                      : SizedBox(),
                  Widgets.heightSpaceH1,

                  // Subscription information card
                  controller.token != null &&
                          controller.userModel?.hasSubscription == true
                      ? _buildSubscriptionCard(controller)
                      : SizedBox(),


                  Widgets.heightSpaceH1,

                  // // Subscription management option
                  // controller.token != null &&
                  //         controller.userModel?.subscription != null
                  //     ? menuTile(Assets.debitCardIcon, "Manage Subscription",
                  //         () {
                  //         _showSubscriptionManagementDialog(
                  //             context, controller);
                  //       })
                  //     : controller.token != null
                  //         ? menuTile(Assets.debitCardIcon, "Subscribe", () {
                  //             Get.to(() => SubscribeView());
                  //           })
                  //         : SizedBox(),

                  controller.token != null
                      ? menuTile(Assets.lockIcon, "Change Password", () {
                          Get.to(() => ChangePasswordView());
                        })
                      : SizedBox(),
                  menuTile(Assets.faqsIcon, "FAQs", () {
                    Get.to(() =>
                        ProfileWebView(url: Endpoints.faqs, title: "FAQs"));
                  }),
                  menuTile(Assets.privacyPolicy, "Privacy Policy", () {
                    Get.to(() => ProfileWebView(
                        url: Endpoints.privacyPolicy, title: "Privacy Policy"));
                  }),
                  menuTile(Assets.termsAndConditionIcon, "Terms & Conditions",
                      () {
                    Get.to(() => ProfileWebView(
                        url: Endpoints.termsCondition,
                        title: "Terms And Conditions"));
                  }),
                  menuTile(Assets.contactUsIcon, "Contact us", () {
                    controller.launchEmail('<EMAIL>');
                  }),
                  menuTile(Assets.techSupportIcon, "About us", () {
                    Get.to(() => ProfileWebView(
                        url: Endpoints.aboutUs, title: "About Us"));
                  }),
                  controller.token != null
                      ? menuTile(Assets.logouticon, "Logout", () {
                          AwesomeDialog(
                            titleTextStyle: TextStyle(
                                fontFamily: "PoppinsBlack",
                                color: Colors.black),
                            context: context,
                            descTextStyle: TextStyle(
                                fontFamily: "PoppinsRegular",
                                color: Colors.black54),
                            dialogType: DialogType.question,
                            animType: AnimType.rightSlide,
                            title: 'Logout',
                            btnOkColor: ColorConstants.primaryColor,
                            desc: 'Do you really want to logout?',
                            btnOkText: "Confirm",
                            btnCancelOnPress: () {},
                            btnOkOnPress: () {
                              Get.find<UserController>().requestLogoutAccount();
                            },
                          )..show();
                        })
                      : SizedBox(),
                  Widgets.heightSpaceH4,
                  controller.token != null
                      ? Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 5),
                          child: CustomButton(
                            onTap: () {
                              AwesomeDialog(
                                titleTextStyle: TextStyle(
                                    fontFamily: "PoppinsBlack",
                                    color: Colors.black),
                                context: context,
                                descTextStyle: TextStyle(
                                    fontFamily: "PoppinsRegular",
                                    color: Colors.black54),
                                dialogType: DialogType.question,
                                animType: AnimType.rightSlide,
                                title: 'Delete Account',
                                btnOkColor: ColorConstants.primaryColor,
                                desc: 'Do you really want to delete?',
                                btnOkText: "Confirm",
                                btnCancelOnPress: () {},
                                btnOkOnPress: () {
                                  Get.find<UserController>()
                                      .requestDeletionAccount();
                                },
                              )..show();
                            },
                            icon: Icon(
                              CupertinoIcons.delete,
                              color: ColorConstants.whiteColor,
                              size: 16,
                            ),
                            label: "Delete Account",
                            borderColor: ColorConstants.redColor,
                            textColor: ColorConstants.whiteColor,
                            backgroundColor: ColorConstants.redColor,
                            padding: 10,
                          ),
                        )
                      : Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 5),
                          child: CustomButton(
                            onTap: () {
                              Get.to(() => LoginView());
                            },
                            icon: Icon(
                              Icons.login,
                              color: ColorConstants.whiteColor,
                              size: 16,
                            ),
                            label: "Login",
                            borderColor: ColorConstants.redColor,
                            textColor: ColorConstants.whiteColor,
                            backgroundColor: ColorConstants.redColor,
                            padding: 10,
                          ),
                        ),
                  Widgets.heightSpaceH5,
                  Widgets.heightSpaceH5,
                ],
              ),
            );
          }),
    );
  }

  Widget _buildSubscriptionCard(UserController controller) {
    final subscription = controller.userModel?.subscription;
    if (subscription == null) return SizedBox();


    final bool isTrial = subscription.trialEndsAt != null;
    final String endDate = isTrial
        ? subscription.formattedTrialEndsAt ?? 'Unknown'
        : subscription.formattedEndsAt ?? 'Unknown';
    final String nextBillingDate =
        subscription.formattedNextBillingDate ?? 'Unknown';

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 8, vertical: 5),
      padding: EdgeInsets.all(15),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Color(0xff014BC4),
            Color(0xff8509AA),
            Color(0xffF90274),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.verified,
                color: Colors.white,
                size: 15,
              ),
              SizedBox(width: 5),
             Expanded(
                child: Texts.textBold(
                isTrial ? "Trial Subscription" : "Active Subscription",
                size: 14,
                color: Colors.white,textAlign: TextAlign.start,
              )),

            ],
          ),
          SizedBox(height: 12),
          isTrial
              ? Texts.textNormal(
                  "Your trial ends on $endDate",
                  size: 10,
                  color: Colors.white,
                )
              : Texts.textNormal(
                  "Current period ends on $endDate",
                  size: 10,
                  color: Colors.white,
                ),
          SizedBox(height: 5),
          subscription.cancelAtPeriodEnd == true
              ? Texts.textNormal(
                  "Your subscription will not renew automatically",
                  size: 10,
                  color: Colors.white,
                )
              : Texts.textNormal(
                  "Next billing on $nextBillingDate",
                  size: 10,
                  color: Colors.white,
                ),
        ],
      ),
    );
  }

  void _confirmSubscriptionAction(
      BuildContext context, UserController controller, String action) {
    String title, desc, confirmText;
    Color confirmColor;

    switch (action) {
      case 'cancel':
        title = 'Cancel Auto-Renewal';
        desc =
            'Your subscription will continue until the end of the current billing period, but will not renew automatically.';
        confirmText = 'Cancel Auto-Renewal';
        confirmColor = Colors.orange;
        break;
      case 'reactivate':
        title = 'Reactivate Subscription';
        desc =
            'Your subscription will be reactivated and will renew automatically at the end of the current period.';
        confirmText = 'Reactivate';
        confirmColor = Colors.green;
        break;
      case 'cancel_now':
        title = 'Cancel Subscription Now';
        desc =
            'Your subscription will be cancelled immediately. You will lose access to premium features.';
        confirmText = 'Cancel Now';
        confirmColor = Colors.red;
        break;
      default:
        return;
    }

    AwesomeDialog(
      context: context,
      dialogType: DialogType.warning,
      animType: AnimType.scale,
      title: title,
      desc: desc,
      btnCancelText: "No, Go Back",
      btnOkText: confirmText,
      btnOkColor: confirmColor,
      btnCancelOnPress: () {},
      btnOkOnPress: () {
        // Call the appropriate API based on the action
        if (action == 'cancel') {
          controller.cancelSubscriptionAutoRenewal();
        } else if (action == 'reactivate') {
          controller.reactivateSubscription();
        } else if (action == 'cancel_now') {
          controller.cancelSubscriptionImmediately();
        }
      },
    )..show();
  }

  Widget menuTile(String icon, String title, Callback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
                border: Border(
                    top: BorderSide(color: ColorConstants.grayBorderColor))),
            child: Padding(
              padding: const EdgeInsets.all(15.0),
              child: Row(
                children: [
                  SizedBox(
                      height: 22,
                      width: 20,
                      child: Image.asset(
                        icon,
                        color: ColorConstants.blackColor,
                      )),
                  Widgets.widthSpaceW3,
                  Texts.textNormal(title,
                      color: ColorConstants.blackColor, size: 14),
                  Expanded(child: Widgets.widthSpaceW1),
                  Icon(Icons.keyboard_arrow_right_rounded)
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
