import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:music_app/controller/user_controller.dart';
import 'package:music_app/core/constants/api_endpoints.dart';
import 'package:music_app/core/services/http_service.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/member_detail_model.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/members_model.dart';

import '../model/notifications_model.dart';

class HomeController extends GetxController {
  RxDouble latitude = 0.0.obs;
  RxDouble longitude = 0.0.obs;
  RxBool hasLocation = false.obs;
  RxString country = "".obs;
  RxString city = "".obs;
  RxList notifications = <Notifications>[].obs;
  RxBool isNotificationLoading = false.obs;
  RxBool isNotificationMoreLoading = false.obs;
  RxInt totalNotifications = 0.obs;
  RxInt currentNotificationPage = 0.obs;
  RxList nearbyTopMusicians = <Members>[].obs;

  RxBool isLoading = false.obs;
  var selectedCategories = <String>{}.obs; // New set for multi-select
  var selectedMemberCard = Rx<Members?>(null);
  Rx<MemberDetail?> memberDetails = Rx<MemberDetail?>(null);
  RxList nearbyCategoryMusicians = <Members>[].obs;
  RxBool isMembersCategoryLoading = false.obs;
  RxBool isMembersCategoryMoreLoading = false.obs;
  RxInt totalMembersCategoryCount = 0.obs;
  RxInt currentMembersCategoryPage = 0.obs;
  RxBool isProfileLoading = false.obs;
  RxList reviews = <Ratings>[].obs;
  RxBool isReviewLoading = false.obs;
  RxBool isReviewMoreLoading = false.obs;
  RxInt totalReviews = 0.obs;
  RxInt currentReviewPage = 0.obs;
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController subjectController = TextEditingController();
  TextEditingController messageController = TextEditingController();
  Future<void> initCalls() async {
    Get.find<UserController>().fetchUserLocation();
    double lat = double.parse(Get.find<UserController>().lat.toString());
    double lng = double.parse(Get.find<UserController>().lon.toString());

    latitude.value = lat;
    longitude.value = lng;

    country.value = Get.find<UserController>().country ?? "";
    city.value = Get.find<UserController>().city ?? "";
    isMembersCategoryLoading.value = true;
    await fetchNearbyTopMusicians();
    fetchCategoryMusicians();
    if (Get.find<UserController>().token != null) {
      Get.find<UserController>().updateFcmToken();
    }
  }

  @override
  onInit() {
    super.onInit();

    initCalls();
  }

  Future<void> fetchNearbyTopMusicians() async {
    try {
      isLoading.value = true;

      final payload = {"country": country.value, "city": city.value,"latitude": "${latitude.value}","longitude": "${longitude.value}"};

      final response =
          await ApiService.postData(Endpoints.musiciansTop, payload);

      if (response.status == true) {
        nearbyTopMusicians.clear();
        nearbyTopMusicians.addAll(
          (response.data['musicians'] as List)
              .map((e) => Members.fromJson(e))
              .toList(),
        );
      } else {}
    } catch (e) {
    } finally {
      isLoading.value = false;
    }
  }
  Future<void> fetchNearbyTopMusiciansBackground() async {
    try {


      final payload = {"country": country.value, "city": city.value,"latitude": "${latitude.value}","longitude": "${longitude.value}"};

      final response =
      await ApiService.postData(Endpoints.musiciansTop, payload);

      if (response.status == true) {
        nearbyTopMusicians.clear();
        nearbyTopMusicians.addAll(
          (response.data['musicians'] as List)
              .map((e) => Members.fromJson(e))
              .toList(),
        );
      } else {}
    } catch (e) {
    } finally {
      isLoading.value = false;
    }
  }

  fetchMemberProfileView() async {
    try {
      isProfileLoading.value = true;

      final response = await ApiService.getData(
          "${Endpoints.musiciansProfile}/${selectedMemberCard.value?.id}");
      isProfileLoading.value = false;
      if (response.status == true) {
        memberDetails.value = MemberDetail.fromJson(response.data['musician']);
        nameController.text = Get.find<UserController>().userModel?.name ?? "";
        emailController.text =
            Get.find<UserController>().userModel?.email ?? "";
      } else {}
    } catch (e) {
      print(e);
    } finally {
      isProfileLoading.value = false;
    }
  }

  Future<void> refreshNearbyMusicians() async {
    await fetchNearbyTopMusicians();
    await fetchCategoryMusicians();
  }

  fetchCategoryMusicians({int page = 1}) async {
    try {
      if (page == 1) {
        isMembersCategoryLoading.value = true;
      } else {
        isMembersCategoryMoreLoading.value = true;
      }

      var roleParam =
          selectedCategories.contains('All') || selectedCategories.isEmpty
              ? null
              : selectedCategories.toList();

      var response = await ApiService.postData(Endpoints.musiciansCategory, {
        "page": page,
        "roles": roleParam,
        "latitude": "${latitude.value}",
        "longitude": "${longitude.value}"
      });
      isMembersCategoryLoading.value = false;
      isMembersCategoryMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          nearbyCategoryMusicians.clear();
          totalMembersCategoryCount.value = 0;
          currentMembersCategoryPage.value = 1;
        }
        nearbyCategoryMusicians.addAll(
          (response.data['musicians'] as List)
              .map((e) => Members.fromJson(e))
              .toList(),
        );

        totalMembersCategoryCount.value =
            response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      isMembersCategoryLoading.value = false;
      isMembersCategoryMoreLoading.value = false;
    } finally {
      isMembersCategoryLoading.value = false;
      isMembersCategoryMoreLoading.value = false;
    }
  }
  fetchCategoryMusiciansBackground({int page = 1}) async {
    try {


      var roleParam =
      selectedCategories.contains('All') || selectedCategories.isEmpty
          ? null
          : selectedCategories.toList();

      var response = await ApiService.postData(Endpoints.musiciansCategory, {
        "page": page,
        "roles": roleParam,
        "latitude": "${latitude.value}",
        "longitude": "${longitude.value}"
      });
      isMembersCategoryLoading.value = false;
      isMembersCategoryMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          nearbyCategoryMusicians.clear();
          totalMembersCategoryCount.value = 0;
          currentMembersCategoryPage.value = 1;
        }
        nearbyCategoryMusicians.addAll(
          (response.data['musicians'] as List)
              .map((e) => Members.fromJson(e))
              .toList(),
        );

        totalMembersCategoryCount.value =
            response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      isMembersCategoryLoading.value = false;
      isMembersCategoryMoreLoading.value = false;
    } finally {
      isMembersCategoryLoading.value = false;
      isMembersCategoryMoreLoading.value = false;
    }
  }
  RxList<RxBool> rolesSelection = List.generate(6, (_) => false.obs).obs;
  RxList<RxBool> playedInstrument = List.generate(6, (_) => false.obs).obs;
  var isAvailable = true.obs;
  var isLiveLocation = true.obs;

  void toggleRole(int index) {
    rolesSelection[index].value = !rolesSelection[index].value;
  }

  void toggleInstruments(int index) {
    playedInstrument[index].value = !playedInstrument[index].value;
  }

  void toggleAvailability(bool value) {
    isAvailable.value = value;
  }

  void toggleLiveLocation(bool value) {
    isLiveLocation.value = value;
  }

  fetchReviews({int page = 1}) async {
    try {
      if (isReviewLoading.value) return;
      if (page == 1) {
        isReviewLoading.value = true;
      } else {
        isReviewMoreLoading.value = true;
      }
      var response = await ApiService.getData(
          "${Endpoints.musiciansRating}/${selectedMemberCard.value?.id}?page=$page");
      isReviewLoading.value = false;
      isReviewMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          reviews.clear();
          totalReviews.value = 0;
          currentReviewPage.value = 1;
        }

        reviews.addAll(
          (response.data['ratings'] as List)
              .map((e) => Ratings.fromJson(e))
              .toList(),
        );

        totalReviews.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      print(e);
      isReviewLoading.value = false;
      isReviewMoreLoading.value = false;
    } finally {
      isReviewLoading.value = false;
      isReviewMoreLoading.value = false;
    }
  }

  submitFeedback(var payload) async {
    try {
      Get.back();
      memberDetails.value?.ratingExists = true;
      Widgets.showLoader("Loading...");
      var response =
          await ApiService.postData("${Endpoints.addRating}", payload);
      Widgets.hideLoader();
      if (response.status == true) {
        Widgets.showSnackBar("Success", response.message ?? "");fetchMemberProfileView();
      } else {
        // Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
    } finally {
      Widgets.hideLoader();
    }
  }

  submitContactRequest(var payload) async {
    try {
      Widgets.showLoader("Submitting...");
      var response =
          await ApiService.postData("${Endpoints.submitContact}", payload);
      {
        Widgets.hideLoader();
        if (response.status == true) {
          messageController.clear();
          subjectController.clear();
          Widgets.showSnackBar("Success", response.message ?? "");
        } else {
          // Widgets.showSnackBar("Error", response.message ?? "");
        }
      }
    } catch (e) {
    } finally {
      Widgets.hideLoader();
    }
  }

  fetchNotifications({int page = 1}) async {
    try {
      if (isNotificationLoading.value) return;
      if (page == 1) {
        isNotificationLoading.value = true;
      } else {
        isNotificationMoreLoading.value = true;
      }
      var response = await ApiService.getData(
        "${Endpoints.notifications}?page=$page",
      );
      Get.find<UserController>().unreadNotificationsCount.value = 0;
      isNotificationLoading.value = false;
      isNotificationMoreLoading.value = false;
      if (response.status == true) {
        if (page == 1) {
          notifications.clear();
          totalNotifications.value = 0;
          currentNotificationPage.value = 1;
        }

        notifications.addAll(
          (response.data['notifications'] as List)
              .map((e) => Notifications.fromJson(e))
              .toList(),
        );

        totalNotifications.value = response.data['pagination']['total'] ?? 0;
      }
    } catch (e) {
      print(e);
      isNotificationLoading.value = false;
      isNotificationMoreLoading.value = false;
    } finally {
      isNotificationLoading.value = false;
      isNotificationMoreLoading.value = false;
    }
  }

  clearNotifications() async {
    try {
      notifications.clear();

      var response =
          await ApiService.postData("${Endpoints.clearNotifications}", {});
      if (response.status == true) {
      } else {}
    } catch (e) {
    } finally {}
  }
  toggleFavoriteFromDetail() async {
    try {
      memberDetails.value?.isFavorite =memberDetails.value!.isFavorite??false==true?false:true;
 memberDetails.refresh();
 nearbyCategoryMusicians.forEach((element) {
        if(element.id==selectedMemberCard.value?.id)
        {log("found");
          element.isFavorite =memberDetails.value!.isFavorite??false==true?false:true;
        }
      });
      nearbyCategoryMusicians.refresh();
      var payload = {};
      var response =
      await ApiService.postData("${Endpoints.favMusician}/${selectedMemberCard.value?.id}", payload);

      if (response.status == true) {
      } else {
        // Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
    } finally {}
  }
  toggleFavorite(String id, int index) async {
    try {
      nearbyCategoryMusicians[index].isFavorite =
          !nearbyCategoryMusicians[index].isFavorite;
      nearbyCategoryMusicians.refresh();
      var payload = {};
      var response =
          await ApiService.postData("${Endpoints.favMusician}/$id", payload);

      if (response.status == true) {
      } else {
        // Widgets.showSnackBar("Error", response.message ?? "");
      }
    } catch (e) {
    } finally {}
  }
}
