import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/utils/extensions.dart';
import 'package:music_app/core/widgets/choice_chip_widget.dart';
import 'package:music_app/core/widgets/cover_and_profile_section.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/core/widgets/image_preview.dart';
import 'package:music_app/core/widgets/location_widget.dart';
import 'package:music_app/core/widgets/rate_and_review_card.dart';
import 'package:music_app/core/widgets/review_section.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/home_controller.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/members_model.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/reviews_view.dart';
import 'package:readmore/readmore.dart';

import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/services/url_launcher_service.dart';
import '../../../../../../core/widgets/entry_field.dart';
import '../../../../../authentication/view/login_view.dart';
import '../../../../subscription/view/subscribe_view.dart';
import '../../inbox/controller/chat_controller.dart';
import '../../inbox/model/chat_model.dart';
import '../../inbox/view/chat_view.dart';
import '../model/member_detail_model.dart';

class MemberDetailView extends StatelessWidget {
  HomeController controller = Get.find();
  MemberDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: () => context.hideKeyboard(),
      child: Scaffold(
          backgroundColor: ColorConstants.whiteColor,
          appBar: AppBar(
            elevation: 0,
            toolbarHeight: 10,
            automaticallyImplyLeading: false,
            scrolledUnderElevation: 0,
            backgroundColor: ColorConstants.whiteColor,
          ),
          // floatingActionButton: Floati,
          body:  RefreshIndicator(
            backgroundColor: ColorConstants.blackColor,color: ColorConstants.whiteColor,
            onRefresh: () async {
              // await controller.fetchTopMusicians();
              await controller.fetchMemberProfileView();
            },

            child: SingleChildScrollView(
              child: Obx(
                () => Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Stack(
                      children: [
                        CoverAndProfileSection(
                            isOnline: controller.memberDetails.value == null
                                ? false
                                : controller.memberDetails.value?.availability == 1
                                    ? true
                                    : false,
                            profile:
                                controller.selectedMemberCard.value?.imageUrl ?? "",
                            cover: controller.memberDetails.value == null
                                ? ""
                                : controller.memberDetails.value?.musicianProfile
                                        ?.headerImageUrl ??
                                    ""),
                        Positioned(
                            right: 10,
                            top: 10,
                            child: Row(
                              children: [

                                IconButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    icon: Icon(
                                      Icons.clear,
                                      color: Colors.white,
                                    )),
                                SizedBox(width: 10),
                                IconButton(
                                    onPressed: () {
                                      Get.back();
                                    },
                                    icon: Icon(
                                      Icons.clear,
                                      color: Colors.white,
                                    )),
                              ],
                            ))
                      ],
                    ),
                    Widgets.heightSpaceH05,

                    Padding(
                      padding: const EdgeInsets.only(top: 0, left: 100, right: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                    child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Texts.textBold(
                                        controller.selectedMemberCard.value?.name ??
                                            "",
                                        size: 17,
                                        textAlign: TextAlign.start),
                                    Texts.textNormal(
                                        controller.memberDetails.value
                                                ?.musicianProfile?.tags
                                                ?.join(", ") ??
                                            "",
                                        size: 10,
                                        color: ColorConstants.greyTextColor,
                                        textAlign: TextAlign.start),
                                  ],
                                )),
                                SizedBox(width: 10),
                                GetBuilder<UserController>(
                                    init: UserController(),
                                    builder: (con) {
                                      return InkWell(
                                        onTap: () {
                                          if (con.token == null) {
                                            AwesomeDialog(
                                              titleTextStyle: TextStyle(
                                                  fontFamily: "PoppinsBlack",
                                                  fontSize: 16,
                                                  color: Colors.black),
                                              context: context,
                                              descTextStyle: TextStyle(
                                                  fontFamily: "PoppinsRegular",
                                                  color: Colors.black54),
                                              dialogType: DialogType.info,
                                              animType: AnimType.rightSlide,
                                              title: 'Login Required',
                                              btnOkColor:
                                                  ColorConstants.primaryColor,
                                              desc:
                                                  'To access the chats feature, you need to login to use our service.',
                                              btnOkText: "Login",
                                              btnCancelOnPress: () {},
                                              btnOkOnPress: () {
                                                Get.to(() => LoginView());
                                              },
                                            )..show();
                                          } else if (con
                                                  .userModel?.hasSubscription ==
                                              false) {
                                            Get.to(() => SubscribeView());
                                          } else {
                                            final existingChat =
                                                Get.find<ChatController>()
                                                    .chats
                                                    .firstWhereOrNull((chat) =>
                                                        chat.otherUser?.id ==
                                                        controller.memberDetails
                                                            .value?.id);
                                            controller.selectedMemberCard.value
                                                    ?.image =
                                                controller.selectedMemberCard.value
                                                    ?.imageUrl;
                                            existingChat == null
                                                ? Get.to(() => ChatView(
                                                      chatUser: controller
                                                          .selectedMemberCard.value,
                                                    ))?.then((value) {})
                                                : Get.to(() => ChatView(
                                                      chatId: existingChat.chatId
                                                          .toString(),
                                                      chatUser: controller
                                                          .selectedMemberCard.value,
                                                    ))?.then((value) {});
                                          }
                                        },
                                        child: con.userModel?.hasSubscription ==
                                            false||con.token==null
                                            ?Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 10, vertical: 5),
                                            decoration: BoxDecoration(
                                              color: ColorConstants.redColor,
                                              borderRadius: BorderRadius.circular(5),
                                            ),
                                            child: Row(
                                          children: [
                                            Icon(
                                              Icons.lock_outline,
                                              color: Colors.white,
                                              size: 12,
                                            ),
                                            Widgets.widthSpaceW1,
                                            Texts.textNormal("Message",
                                                size: 11,
                                                color: ColorConstants
                                                    .whiteColor),
                                          ],
                                        ))
                                            : con.userModel?.id!=controller.memberDetails.value?.id?Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 10, vertical: 5),
                                          decoration: BoxDecoration(
                                            color: ColorConstants.redColor,
                                            borderRadius: BorderRadius.circular(5),
                                          ),
                                          child:Texts.textNormal("Message",
                                                  size: 11,
                                                  color: ColorConstants.whiteColor),
                                        ):SizedBox.shrink(),
                                      );
                                    }),
                                Widgets.heightSpaceH1,
                              ]),
                        ],
                      ),
                    ),

                    Center(
                      child: Padding(
                        padding: const EdgeInsets.all(15.0),
                        child: ReadMoreText(
                            "${controller.memberDetails.value?.musicianProfile?.description ?? ""}",
                            trimLines: 4,
                            trimMode: TrimMode.Line,
                            trimCollapsedText: 'Show more',
                            trimExpandedText: 'Show less',
                            lessStyle: TextStyle(
                                fontSize: 12,
                                fontFamily: "PoppinsRegular",
                                color: ColorConstants.primaryColor),
                            moreStyle: TextStyle(
                                fontSize: 12,
                                color: ColorConstants.primaryColor,
                                fontFamily: "PoppinsRegular"),
                            style: TextStyle(
                                fontSize: 12,
                                color: Colors.black,
                                fontFamily: "PoppinsRegular")),
                      ),
                    ),

                    RateAndReviewCard(
                      perHour: controller
                              .memberDetails.value?.musicianProfile?.ratePerHour ??
                          "0",
                      ratingAvg: controller.memberDetails.value?.musicianProfile
                              ?.averageRating ??
                          "0",
                      ratingNo: controller
                              .memberDetails.value?.musicianProfile?.ratingsCount
                              ?.toString() ??
                          "",
                      perEvent: controller
                              .memberDetails.value?.musicianProfile?.ratePerEvent ??
                          "0",
                    ),

                    Widgets.heightSpaceH2,
                    buildExpansionTile(
                      context: context,
                      icon: Icons.info_outline,
                      title: "Contact Info",
                      children: [
                        controller.memberDetails.value != null
                            ? Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10.0, horizontal: 20),
                          child: Obx(() {
                            return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  GestureDetector(
                                      onTap: () {
                                        UrlLauncherService.launchPhoneCall(
                                            controller
                                                .memberDetails
                                                .value
                                                ?.musicianProfile
                                                ?.phoneNumber ??
                                                "");
                                      },
                                      child: Widgets.buildContactCard(
                                          icon: Icons.phone,
                                          title: "Phone No.",
                                          value:
                                          "${controller.memberDetails.value?.musicianProfile?.countryCode ?? ""} ${controller.memberDetails.value?.musicianProfile?.phoneNumber ?? ""}")),
                                  GestureDetector(
                                      onTap: () {

                                        Get.find<UserController>().launchEmail( controller.memberDetails.value
                                            ?.email ??
                                            "");

                                      },
                                      child: Widgets.buildContactCard(
                                        icon: Icons.email,
                                        title: "Email",
                                        value: controller
                                            .memberDetails.value?.email ??
                                            "",
                                      )),
                                  controller.memberDetails.value
                                      ?.musicianProfile?.website !=
                                      null
                                      ? GestureDetector(
                                      onTap: () {
                                        UrlLauncherService.launchWebsite(
                                            controller
                                                .memberDetails
                                                .value
                                                ?.musicianProfile
                                                ?.website ??
                                                "");
                                      },
                                      child: Widgets.buildContactCard(
                                          icon: Icons.web,
                                          title: "Website",
                                          value: controller
                                              .memberDetails
                                              .value
                                              ?.musicianProfile
                                              ?.website ??
                                              ""))
                                      : SizedBox.shrink(),
                                ]);
                          }),
                        )
                            : SizedBox.shrink(),
                      ],
                    ),
                    // Expandable Tiles
                    buildExpansionTile(
                      context: context,
                      icon: Icons.manage_accounts,
                      title: "Roles",
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10.0, horizontal: 20),
                          child: Obx(() {
                            // Get roles from selected member
                            final memberRoles = controller
                                    .memberDetails.value?.musicianProfile?.roles ??
                                [];

                            // If no roles available, show a message
                            if (memberRoles.isEmpty) {
                              return Center(
                                child: Texts.textNormal(
                                  "No roles specified",
                                  color: ColorConstants.greyTextColor,
                                  size: 14,
                                ),
                              );
                            }

                            // Display the roles from the member data
                            return Wrap(
                              crossAxisAlignment: WrapCrossAlignment.start,
                              alignment: WrapAlignment.start,
                              spacing: 8.w,
                              runSpacing: 8.h,
                              children: List.generate(
                                memberRoles.length,
                                (index) => ChoiceChipWidget(
                                  label: memberRoles[index].toString(),
                                  size: 14,
                                  // No action when tapped
                                ),
                              ),
                            );
                          }),
                        )
                      ],
                    ),
                    buildExpansionTile(
                      context: context,
                      icon: Icons.music_note_outlined,
                      title: "Played instruments",
                      children: [
                        controller.memberDetails.value != null
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10.0, horizontal: 20),
                                child: Obx(() {
                                  // Get instruments from member details
                                  final instruments = controller.memberDetails.value
                                          ?.musicianProfile?.instruments ??
                                      [];

                                  // If no instruments available, show a message
                                  if (instruments.isEmpty) {
                                    return Center(
                                      child: Texts.textNormal(
                                        "No instruments specified",
                                        color: ColorConstants.greyTextColor,
                                        size: 14,
                                      ),
                                    );
                                  }

                                  // Display the instruments from the member data
                                  return Wrap(
                                    crossAxisAlignment: WrapCrossAlignment.start,
                                    alignment: WrapAlignment.start,
                                    spacing: 10,
                                    runSpacing: 10,
                                    children: List.generate(
                                      instruments.length,
                                      (index) => ChoiceChipWidget(
                                        label: instruments[index].toString(),
                                        size: 14,
                                        // No action when tapped
                                      ),
                                    ),
                                  );
                                }),
                              )
                            : SizedBox.shrink(),
                      ],
                    ),
                    buildExpansionTile(
                      context: context,
                      icon: Icons.category_outlined,
                      title: "Music Categories",
                      children: [
                        controller.memberDetails.value != null
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10.0, horizontal: 20),
                                child: Obx(() {
                                  // Get instruments from member details
                                  final instruments = controller.memberDetails.value
                                          ?.musicianProfile?.musicTypes ??
                                      [];

                                  // If no instruments available, show a message
                                  if (instruments.isEmpty) {
                                    return Center(
                                      child: Texts.textNormal(
                                        "No music categories",
                                        color: ColorConstants.greyTextColor,
                                        size: 14,
                                      ),
                                    );
                                  }

                                  // Display the instruments from the member data
                                  return Wrap(
                                    crossAxisAlignment: WrapCrossAlignment.start,
                                    alignment: WrapAlignment.start,
                                    spacing: 10,
                                    runSpacing: 10,
                                    children: List.generate(
                                      instruments.length,
                                      (index) => ChoiceChipWidget(
                                        label: instruments[index].toString(),
                                        size: 14,
                                        // No action when tapped
                                      ),
                                    ),
                                  );
                                }),
                              )
                            : SizedBox.shrink(),
                      ],
                    ),


                    buildExpansionTile(
                        context: context,
                        icon: Icons.location_on_outlined,
                        title: "Location",
                        children: [
                          Obx(() =>controller.memberDetails.value == null?SizedBox.shrink():LocationWidget(
                              lat: controller.selectedMemberCard.value!.latitude
                                      ?.toDouble() ??
                                  0,
                              long: controller.selectedMemberCard.value!.longitude
                                      ?.toDouble() ??
                                  0,
                              address: controller.memberDetails.value
                                      ?.musicianProfile?.location ??
                                  "")),
                        ]),
                    buildExpansionTile(
                      context: context,
                      icon: Icons.language_outlined,
                      title: "Social Networks",
                      children: [
                        controller.memberDetails.value != null
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10.0, horizontal: 20),
                                child: Obx(() {
                                  // Get instruments from member details
                                  final instruments = controller.memberDetails.value
                                          ?.musicianProfile?.socials ??
                                      [];

                                  // If no instruments available, show a message
                                  if (instruments.isEmpty) {
                                    return Center(
                                      child: Texts.textNormal(
                                        "No social networks",
                                        color: ColorConstants.greyTextColor,
                                        size: 14,
                                      ),
                                    );
                                  }

                                  // Display the instruments from the member data
                                  return ListView.separated(
                                      shrinkWrap: true,
                                      itemBuilder: (context, index) {
                                        return GestureDetector(
                                            onTap: () {
                                              UrlLauncherService.launchWebsite(
                                                  instruments[index].value ?? "");
                                            },
                                            child: Widgets.buildSocialCard(
                                                title: instruments[index].key ?? "",
                                                value: instruments[index].value ??
                                                    ""));
                                      },
                                      separatorBuilder: (context, index) {
                                        return SizedBox(
                                          height: 2,
                                        );
                                      },
                                      itemCount: instruments.length);
                                }),
                              )
                            : SizedBox.shrink(),
                      ],
                    ),

                    buildExpansionTile(
                      context: context,
                      icon: Icons.translate,
                      title: "Spoken Languages",
                      children: [
                        controller.memberDetails.value != null
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10.0, horizontal: 20),
                                child: Obx(() {
                                  // Get instruments from member details
                                  final instruments = controller.memberDetails.value
                                          ?.musicianProfile?.spokenLanguages ??
                                      [];

                                  // If no instruments available, show a message
                                  if (instruments.isEmpty) {
                                    return Center(
                                      child: Texts.textNormal(
                                        "No languages",
                                        color: ColorConstants.greyTextColor,
                                        size: 14,
                                      ),
                                    );
                                  }

                                  // Display the instruments from the member data
                                  return Wrap(
                                    crossAxisAlignment: WrapCrossAlignment.start,
                                    alignment: WrapAlignment.start,
                                    spacing: 10,
                                    runSpacing: 10,
                                    children: List.generate(
                                      instruments.length,
                                      (index) => ChoiceChipWidget(
                                        label: instruments[index].toString(),
                                        size: 14,
                                        // No action when tapped
                                      ),
                                    ),
                                  );
                                }),
                              )
                            : SizedBox.shrink(),
                      ],
                    ),
                    buildExpansionTile(
                      context: context,
                      icon: Icons.payment_outlined,
                      title: "Payment Methods",
                      children: [
                        controller.memberDetails.value != null
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 5.0, horizontal: 20),
                                child: Obx(() {
                                  // Get instruments from member details
                                  final instruments = controller.memberDetails.value
                                          ?.musicianProfile?.paymentMethods ??
                                      [];

                                  // If no instruments available, show a message
                                  if (instruments.isEmpty) {
                                    return Center(
                                      child: Texts.textNormal(
                                        "No payment methods",
                                        color: ColorConstants.greyTextColor,
                                        size: 14,
                                      ),
                                    );
                                  }

                                  // Display the instruments from the member data
                                  return Wrap(
                                    crossAxisAlignment: WrapCrossAlignment.start,
                                    alignment: WrapAlignment.start,
                                    spacing: 10,
                                    runSpacing: 10,
                                    children: List.generate(
                                      instruments.length,
                                      (index) => ChoiceChipWidget(
                                        label: instruments[index].toString(),
                                        size: 14,
                                        // No action when tapped
                                      ),
                                    ),
                                  );
                                }),
                              )
                            : SizedBox.shrink(),
                      ],
                    ),
                    buildExpansionTile(
                      context: context,
                      icon: Icons.photo_size_select_actual_outlined,
                      title: "Images Gallery",
                      children: [
                        controller.memberDetails.value != null
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 5.0, horizontal: 20),
                                child: GridView.builder(
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true,
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 3,
                                    crossAxisSpacing: 8,
                                    mainAxisSpacing: 8,
                                    childAspectRatio: 1,
                                  ),
                                  itemCount: (controller.memberDetails.value
                                          ?.musicianProfile?.gallery?.length ??
                                      0),
                                  itemBuilder: (context, index) {
                                    return InkWell(
                                      onTap: () {
                                        Get.to(() => ImagePreview(
                                            imageUrl: controller
                                                    .memberDetails
                                                    .value
                                                    ?.musicianProfile!
                                                    .gallery![index]
                                                    .imageUrl ??
                                                "",
                                            isNetwork: true));
                                      },
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(12),
                                        child: Image.network(
                                          controller
                                                  .memberDetails
                                                  .value
                                                  ?.musicianProfile!
                                                  .gallery![index]
                                                  .imageUrl ??
                                              "",
                                          height: double.infinity,
                                          width: double.infinity,
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    );
                                  },
                                ))
                            : SizedBox.shrink(),
                      ],
                    ),
                    buildExpansionTile(
                      context: context,
                      icon: Icons.contact_page_outlined,
                      title: "Contact Form",
                      children: [
                        GetBuilder<UserController>(
                            init: UserController(),
                            builder: (userController) {
                              if (userController.token == null) {
                                return Widgets.buildLoginPrompt(
                                    title: "contact form");
                              } else if (userController
                                      .userModel?.hasSubscription ==
                                  false) {
                                return Widgets.buildSubscriptionPrompt(
                                    title: "contact form");
                              }

                              return Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 10.0, horizontal: 20),
                                child: Column(
                                  children: [
                                    EntryField(
                                      fillColor:
                                          ColorConstants.backgroundSilverColor,
                                      label: "Name",
                                      hint: "Enter your name",
                                    ),
                                    EntryField(
                                      fillColor:
                                          ColorConstants.backgroundSilverColor,
                                      label: "Email",
                                      controller: controller.emailController,
                                      hint: "Enter your email",
                                    ),
                                    EntryField(
                                      fillColor:
                                          ColorConstants.backgroundSilverColor,
                                      label: "Subject",
                                      controller: controller.subjectController,
                                      hint: "Enter here",
                                    ),
                                    EntryBigField(
                                      color: ColorConstants.whiteColor,
                                      controller: controller.messageController,
                                      label: "Message",
                                      minLines: 5,
                                      hint: "Enter your message",
                                      maxLines: null,
                                    ),
                                    CustomButton(
                                      label: "Submit",
                                      textColor: ColorConstants.whiteColor,
                                      backgroundColor: ColorConstants.primaryColor,
                                      fontSize: 12,
                                      onTap: () {
                                        if (controller
                                            .nameController.text.isEmpty) {
                                          Widgets.showSnackBar(
                                              "Alert", "Enter your name.");
                                        } else if (!GetUtils.isEmail(
                                            controller.emailController.text)) {
                                          Widgets.showSnackBar(
                                              "Alert", "Enter your valid email.");
                                        } else if (controller
                                            .subjectController.text.isEmpty) {
                                          Widgets.showSnackBar(
                                              "Alert", "Enter subject.");
                                        } else if (controller
                                            .messageController.text.isEmpty) {
                                          Widgets.showSnackBar(
                                              "Alert", "Enter message.");
                                        } else {
                                          context.hideKeyboard();
                                          controller.submitContactRequest({
                                            "musician_id": controller
                                                .selectedMemberCard.value?.id
                                                .toString(),
                                            "name": controller.nameController.text,
                                            "email":
                                                controller.emailController.text,
                                            "subject":
                                                controller.subjectController.text,
                                            "message":
                                                controller.messageController.text,
                                          });
                                        }
                                      },
                                    ),
                                  ],
                                ),
                              );
                            })
                      ],
                    ),
                    buildExpansionTile(
                      icon: Icons.star_rate_outlined,
                      title: "Reviews",
                      context: context,
                      children: [
                        GetBuilder<UserController>(
                            init: UserController(),
                            builder: (userController) {
                              if (userController.token == null) {
                                return Widgets.buildLoginPrompt(title: "reviews");
                              } else if (userController
                                      .userModel?.hasSubscription ==
                                  false) {
                                return Widgets.buildSubscriptionPrompt(
                                    title: "reviews");
                              }
                              return controller.memberDetails.value != null
                                  ? Column(
                                      children: [
                                        controller
                                                    .memberDetails
                                                    .value
                                                    ?.musicianProfile
                                                    ?.ratings
                                                    ?.isNotEmpty ==
                                                true
                                            ? ListView.builder(
                                                shrinkWrap: true,
                                                physics:
                                                    NeverScrollableScrollPhysics(),
                                                itemCount: controller
                                                            .memberDetails
                                                            .value!
                                                            .musicianProfile!
                                                            .ratings!
                                                            .length >
                                                        3
                                                    ? 3
                                                    : controller
                                                            .memberDetails
                                                            .value
                                                            ?.musicianProfile
                                                            ?.ratings
                                                            ?.length ??
                                                        0,
                                                itemBuilder: (context, index) {
                                                  Ratings? rating = controller
                                                      .memberDetails
                                                      .value
                                                      ?.musicianProfile
                                                      ?.ratings?[index];
                                                  return ReviewSection(
                                                    rating: rating,
                                                  );
                                                },
                                              )
                                            : Center(
                                                child: Texts.textNormal(
                                                  "No reviews yet",
                                                  size: 12,
                                                ),
                                              ),
                                        if (controller
                                                .memberDetails
                                                .value
                                                ?.musicianProfile
                                                ?.ratings
                                                ?.isNotEmpty ==
                                            true)
                                          controller
                                                      .memberDetails
                                                      .value!
                                                      .musicianProfile!
                                                      .ratings!
                                                      .length >
                                                  3
                                              ? Padding(
                                                  padding:
                                                      const EdgeInsets.all(15.0),
                                                  child: CustomButton(
                                                    label: "See All Reviews",
                                                    textColor:
                                                        ColorConstants.blackColor,
                                                    fontSize: 12,
                                                    backgroundColor: Colors.white,
                                                    borderColor:
                                                        ColorConstants.blackColor,
                                                    onTap: () {
                                                      controller.fetchReviews(
                                                          page: 1);
                                                      Get.to(
                                                          () => MemberReviewView());
                                                    },
                                                  ),
                                                )
                                              : SizedBox(),
                                      ],
                                    )
                                  : SizedBox.shrink();
                            }),
                      ],
                    ),
                    Widgets.heightSpaceH5,
                    Widgets.heightSpaceH4,
                  ],
                ),
              ),
            ),
          ),
          floatingActionButton: GetBuilder<UserController>(
              init: UserController(),
              builder: (con) {
                return con.token== null||con.userModel?.hasSubscription == false||con.userModel?.id==controller.memberDetails.value?.id
                    ? SizedBox()
                    : Obx(() => controller.memberDetails.value != null
                        ? controller.memberDetails.value?.ratingExists == false
                            ? FloatingActionButton(
                                backgroundColor: ColorConstants.redColor,
                                child: Icon(
                                  Icons.rate_review_outlined,
                                  color: Colors.white,
                                ),
                                onPressed: () {
                                  showReviewBottomSheet(context);
                                })
                            : SizedBox()
                        : SizedBox());
              })),
    );
  }

  Widget buildExpansionTile(
      {required context,
      required IconData icon,
      required String title,
      required List<Widget> children}) {
    return Theme(
      data: Theme.of(context).copyWith(
          dividerColor: Colors.grey.shade50, splashColor: Colors.transparent),
      child: ExpansionTile(
          initiallyExpanded: true,
          dense: true,
          expandedAlignment: Alignment.centerLeft,
          title: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
            Texts.textBold(
              title,
              size: 16,
              textAlign: TextAlign.start,
            )
          ]),
          childrenPadding: EdgeInsets.zero,
          visualDensity: VisualDensity(horizontal: -4, vertical: 0),
          children: children),
    );
  }

  void showReviewBottomSheet(BuildContext context) {
    int selectedRating = 0;
    final reviewController = TextEditingController();

    showModalBottomSheet(
      backgroundColor: Colors.white,
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return StatefulBuilder(
          builder: (contextt, setState) {
            return Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
                left: 20,
                right: 20,
                top: 20,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBold("Write a Review", size: 18),
                      IconButton(
                        icon: Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                  SizedBox(height: 15),
                  Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(5, (index) {
                        return IconButton(
                          icon: Icon(
                            index < selectedRating
                                ? Icons.star
                                : Icons.star_border,
                            color: index < selectedRating
                                ? Colors.amber
                                : Colors.grey,
                            size: 36,
                          ),
                          onPressed: () {
                            setState(() {
                              selectedRating = index + 1;
                            });
                          },
                        );
                      }),
                    ),
                  ),
                  SizedBox(height: 15),
                  EntryBigField(
                    controller: reviewController,
                    maxLines: null,
                    minLines: 8,
                    hint: "Share your experience",
                  ),
                  SizedBox(height: 20),
                  CustomButton(
                    label: "Submit Review",
                    onTap: () {
                      if (selectedRating == 0) {
                        Widgets.showSnackBar("Alert", "Please select a rating");
                        return;
                      }
context.hideKeyboard();
                      var request = {};

                      request['comment'] = reviewController.text;
                      request['rating'] = selectedRating.toString();
                      request['musician_profile_id'] =
                          controller.memberDetails.value?.musicianProfile?.id
                              .toString();

                      controller.submitFeedback(request);
                    },
                  ),
                  SizedBox(height: 20),
                ],
              ),
            );
          },
        );
      },
    );
  }

}
