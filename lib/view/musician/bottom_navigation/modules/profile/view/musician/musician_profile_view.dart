import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/widgets/choice_chip_widget.dart';
import 'package:music_app/core/widgets/cover_and_profile_section.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/core/widgets/location_widget.dart';
import 'package:music_app/core/widgets/rate_and_review_card.dart';
import 'package:music_app/core/widgets/review_section.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:readmore/readmore.dart';

import '../../../../../../../controller/user_controller.dart';
import '../../../../../../../core/services/url_launcher_service.dart';
import '../../../../../../../core/widgets/image_preview.dart';
import '../../../home/<USER>/member_detail_model.dart';
import '../../controller/controller.dart';
import 'edit_musician_profile_view.dart';

class MusicianProfileView extends StatefulWidget {
  const MusicianProfileView({super.key});

  @override
  State<MusicianProfileView> createState() => _MusicianProfileViewState();
}

class _MusicianProfileViewState extends State<MusicianProfileView> {
  late MusicianController muscianController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    muscianController = Get.put(MusicianController());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.whiteColor,
      appBar: AppBar(
        elevation: 0,
        toolbarHeight: 10,
        automaticallyImplyLeading: false,
        scrolledUnderElevation: 0,
        backgroundColor: ColorConstants.whiteColor,
      ),
      body:  RefreshIndicator(
        backgroundColor: ColorConstants.blackColor,
        onRefresh: () async {
          // await controller.fetchTopMusicians();
          await Get.find<UserController>().fetchUserDetails();
        },              color: ColorConstants.whiteColor,

        child: SingleChildScrollView(
          child: GetBuilder<UserController>(builder: (controller) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CoverAndProfileSection(isOnline: controller.userModel?.availability == 1?true:false,
                    profile: controller.userModel?.imageUrl ?? "",
                    cover: controller.userModel?.musicianProfile == null
                        ? ""
                        : controller.userModel?.musicianProfile?.headerImageUrl ??
                            ""),
                Widgets.heightSpaceH05,

                Padding(
                  padding: const EdgeInsets.only(top: 0, left: 100, right: 8),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Texts.textBold(controller.userModel?.name ?? "",
                                    size: 17, textAlign: TextAlign.start),
                                Texts.textNormal(
                                    controller.userModel?.musicianProfile?.tags
                                            ?.join(", ") ??
                                        "",
                                    size: 10,
                                    color: ColorConstants.greyTextColor,
                                    textAlign: TextAlign.start),
                              ],
                            )),
                            SizedBox(width: 10),
                            GestureDetector(onTap: () {
                              Get.to(() => EditMusicianProfileView());
                            },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 5),
                                decoration: BoxDecoration(
                                  color: ColorConstants.primaryColor,
                                  borderRadius: BorderRadius.circular(5),
                                ),
                                child: Texts.textNormal("Edit Profile",
                                    size: 11, color: ColorConstants.whiteColor),
                              ),
                            ),
                            Widgets.heightSpaceH1,
                          ]),
                    ],
                  ),
                ),

                Center(
                  child: Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: ReadMoreText(
                        "\"${controller.userModel?.musicianProfile?.description ?? ""}\"",
                        trimLines: 4,
                        trimMode: TrimMode.Line,
                        trimCollapsedText: 'Show more',
                        trimExpandedText: 'Show less',
                        lessStyle: TextStyle(
                            fontSize: 12,
                            fontFamily: "PoppinsRegular",
                            color: ColorConstants.primaryColor),
                        moreStyle: TextStyle(
                            fontSize: 12,
                            color: ColorConstants.primaryColor,
                            fontFamily: "PoppinsRegular"),
                        style: TextStyle(
                            fontSize: 12,
                            color: Colors.black,
                            fontFamily: "PoppinsRegular")),
                  ),
                ),

                RateAndReviewCard(
                  perHour:
                      controller.userModel?.musicianProfile?.ratePerHour ?? "0",
                  ratingAvg:
                      controller.userModel?.musicianProfile?.averageRating ?? "0",
                  ratingNo: controller.userModel?.musicianProfile?.ratingsCount
                          ?.toString() ??
                      "",
                  perEvent:
                      controller.userModel?.musicianProfile?.ratePerEvent ?? "0",
                ),

                Widgets.heightSpaceH2,
                Container(
                  decoration: BoxDecoration(
                    border: Border(
                        top: BorderSide(color: ColorConstants.grayBorderColor),
                      ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15.0, vertical: 5.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Texts.textBold('Availability',
                            size: 14, color: ColorConstants.blackColor),
                        Obx(
                          () => Transform.scale(
                            scale: 0.7,
                            child: Switch(
                              value: muscianController.isAvailable.value,
                              onChanged: (value) {
                                muscianController.toggleAvailability(value);
                              },
                              activeColor: Colors.white,
                              activeTrackColor: Colors.green,
                              inactiveThumbColor: Colors.white,
                              inactiveTrackColor:Colors.grey,
                            ),
                          ),
                        ),

                        Spacer(),
                        // Live Location
                        Row(
                          children: [
                            Texts.textBold('Live Location',
                                size: 14, color: ColorConstants.blackColor),
                            Obx(
                              () => Transform.scale(
                                scale: 0.7,
                                child: Switch(
                                  value: muscianController.isLiveLocation.value,
                                  onChanged: muscianController.toggleLiveLocation,
                                  activeColor: Colors.white,
                                  activeTrackColor: Colors.green,
                                  inactiveThumbColor: Colors.white,
                                  inactiveTrackColor: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),


                // Expandable Tiles
                buildExpansionTile(
                  context: context,
                  icon: Assets.rolesIcon,
                  title: "Roles",
                  children: [
                    Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10.0, horizontal: 20),
                        child: Wrap(
                            crossAxisAlignment: WrapCrossAlignment.start,
                            alignment: WrapAlignment.start,
                            spacing: 8.w,
                            runSpacing: 8.h,
                            children: List.generate(
                              controller.userModel?.musicianProfile?.roles
                                      ?.length ??
                                  0,
                              (index) => ChoiceChipWidget(
                                label: controller.userModel?.musicianProfile
                                        ?.roles?[index] ??
                                    "",
                                size: 14,
                                // No action when tapped
                              ),
                            ))),
                  ],
                ),
                buildExpansionTile(
                  context: context,
                  icon: Assets.skillsIcon,
                  title: "Played instruments",
                  children: [
                    controller.userModel?.musicianProfile != null
                        ? Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: 10.0, horizontal: 20),
                            child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.start,
                              alignment: WrapAlignment.start,
                              spacing: 10,
                              runSpacing: 10,
                              children: List.generate(
                                controller.userModel?.musicianProfile?.instruments
                                        ?.length ??
                                    0,
                                (index) => ChoiceChipWidget(
                                  label: controller.userModel?.musicianProfile
                                          ?.instruments?[index] ??
                                      "",
                                  size: 14,
                                  // No action when tapped
                                ),
                              ),
                            ))
                        : SizedBox.shrink(),
                  ],
                ), buildExpansionTile(
                  context: context,
                  icon: Assets.skillsIcon,
                  title: "Spoken Languages",
                  children: [
                    controller.userModel?.musicianProfile != null
                        ? Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10.0, horizontal: 20),
                        child: Wrap(
                          crossAxisAlignment: WrapCrossAlignment.start,
                          alignment: WrapAlignment.start,
                          spacing: 10,
                          runSpacing: 10,
                          children: List.generate(
                            controller.userModel?.musicianProfile?.spokenLanguages
                                ?.length ??
                                0,
                                (index) => ChoiceChipWidget(
                              label: controller.userModel?.musicianProfile
                                  ?.spokenLanguages?[index] ??
                                  "",
                              size: 14,
                              // No action when tapped
                            ),
                          ),
                        ))
                        : SizedBox.shrink(),
                  ],
                ), buildExpansionTile(
                  context: context,
                  icon: "",
                  title: "Offered Services",
                  children: [
                    controller.userModel?.musicianProfile != null
                        ? Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10.0, horizontal: 20),
                        child:


                        Wrap(
                          crossAxisAlignment: WrapCrossAlignment.start,
                          alignment: WrapAlignment.start,
                          spacing: 10,
                          runSpacing: 10,
                          children: List.generate(
                            controller.userModel
                                ?.musicianProfile?.offeredServices?.length ?? 0,
                                (index) => ChoiceChipWidget(
                              label: controller.userModel
                                  ?.musicianProfile?.offeredServices?[index]??"",
                              size: 14,
                              // No action when tapped
                            ),
                          ),
                        )

                    )
                        : SizedBox.shrink(),
                  ],
                ),
                buildExpansionTile(
                  context: context,
                  icon: "",
                  title: "Music Categories",
                  children: [
                    controller.userModel?.musicianProfile != null
                        ? Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10.0, horizontal: 20),
                      child:


                         Wrap(
                          crossAxisAlignment: WrapCrossAlignment.start,
                          alignment: WrapAlignment.start,
                          spacing: 10,
                          runSpacing: 10,
                          children: List.generate(
                        controller.userModel
                            ?.musicianProfile?.musicTypes?.length ?? 0,
                                (index) => ChoiceChipWidget(
                              label: controller.userModel
                                  ?.musicianProfile?.musicTypes?[index]??"",
                              size: 14,
                              // No action when tapped
                            ),
                          ),
                        )

                    )
                        : SizedBox.shrink(),
                  ],
                ), buildExpansionTile(
                  context: context,
                  icon: "",
                  title: "Social Networks",
                  children: [
                    controller.userModel?.musicianProfile!= null
                        ? Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10.0, horizontal: 20),
                      child: Obx(() {
                        // Get instruments from member details
                        final instruments =   controller.userModel?.musicianProfile?.socials ??
                            [];

                        // If no instruments available, show a message
                        if (instruments.isEmpty) {
                          return Center(
                            child: Texts.textNormal(
                              "No social networks",
                              color: ColorConstants.greyTextColor,
                              size: 14,
                            ),
                          );
                        }

                        // Display the instruments from the member data
                        return ListView.separated(
                            shrinkWrap: true,
                            itemBuilder: (context, index) {
                              return GestureDetector(
                                  onTap: () {
                                    UrlLauncherService.launchWebsite(
                                        instruments[index].value ?? "");
                                  },
                                  child: Widgets.buildSocialCard(
                                      title: instruments[index].key ?? "",
                                      value: instruments[index].value ??
                                          ""));
                            },
                            separatorBuilder: (context, index) {
                              return SizedBox(
                                height: 2,
                              );
                            },
                            itemCount: instruments.length);
                      }),
                    )
                        : SizedBox.shrink(),
                  ],
                ),
                buildExpansionTile(
                  context: context,
                icon: "",
                  title: "Contact Info",
                  children: [
                    controller.userModel?.musicianProfile!= null
                        ? Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10.0, horizontal: 20),
                      child:  Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [

                              Widgets.buildContactCard(
                                  icon: Icons.phone,
                                  title: "Phone No.",
                                  value:"${  controller.userModel?.musicianProfile?.countryCode ??
                                      ""} ${controller.userModel?.musicianProfile?.phoneNumber ??
                                      ""}"),
                              Widgets.buildContactCard(
                                icon: Icons.email,
                                title: "Email",
                                value:
                                controller.userModel?.email??
                                    "",
                              ),
                             controller.userModel?.musicianProfile?.website!=null
                                  ? Widgets.buildContactCard(
                                  icon: Icons.web,
                                  title: "Website",
                                  value:   controller.userModel?.musicianProfile?.website ??
                                      "")
                                  : SizedBox.shrink(),
                            ])

                    )
                        : SizedBox.shrink(),
                  ],
                ),       buildExpansionTile(
                    context: context,
                    icon: "",
                    title: "Location",
                    children: [
                      LocationWidget(lat: double.parse(controller.userModel?.latitude??"0"), long: double.parse(controller.userModel?.longitude??"0"),address: controller.userModel?.musicianProfile?.location??""
                      ),
                    ]),
                buildExpansionTile(
                  icon: Assets.redReviewIcon,
                  title: "Reviews",
                  context: context,
                  children: [
                    controller.userModel?.musicianProfile != null
                        ? Column(
                            children: [
                              controller.userModel?.musicianProfile?.ratings
                                          ?.isNotEmpty ==
                                      true
                                  ? ListView.builder(
                                      shrinkWrap: true,
                                      physics: NeverScrollableScrollPhysics(),
                                      itemCount: controller
                                                  .userModel!
                                                  .musicianProfile!
                                                  .ratings!
                                                  .length >
                                              3
                                          ? 3
                                          : controller.userModel!.musicianProfile!
                                                  .ratings!.length ??
                                              0,
                                      itemBuilder: (context, index) {
                                        Ratings? rating = controller.userModel
                                            ?.musicianProfile?.ratings?[index];
                                        return ReviewSection(
                                          rating: rating,
                                        );
                                      },
                                    )
                                  : Padding(
                                    padding: const EdgeInsets.only(bottom: 15.0),
                                    child: Center(
                                        child: Texts.textNormal(
                                          "No reviews yet",
                                          size: 12,
                                        ),
                                      ),
                                  ),
                              if (controller.userModel?.musicianProfile?.ratings
                                      ?.isNotEmpty ==
                                  true)
                                controller.userModel!.musicianProfile!.ratings!
                                            .length >
                                        3
                                    ? Padding(
                                        padding: const EdgeInsets.all(15.0),
                                        child: CustomButton(
                                          label: "See All Reviews",
                                          textColor: ColorConstants.blackColor,
                                          radius: 50,
                                          fontSize: 12,
                                          padding: 10,
                                          borderColor: ColorConstants.blackColor,
                                          onTap: () {
                                            // muscianController.fetchReviews(page: 1);Get.to(() => MemberReviewView());
                                          },
                                        ),
                                      )
                                    : SizedBox(),
                            ],
                          )
                        : SizedBox.shrink(),
                  ],
                ),
                buildExpansionTile(
                  context: context,
                  icon:"",
                  title: "Images Gallery",
                  children: [
                    controller.userModel?.musicianProfile != null
                        ? Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 5.0, horizontal: 20),
                        child:   GridView.builder(
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 3,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                            childAspectRatio: 1,
                          ),
                          itemCount: (    controller.userModel?.musicianProfile?.gallery
                              ?.length ??
                              0),
                          itemBuilder: (context, index) {

                            return InkWell(onTap: (){

                              Get.to(()=>ImagePreview(imageUrl: controller.userModel?.musicianProfile!
                                  .gallery![index].imageUrl ??
                                  "", isNetwork: true));
                            },
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(12),
                                child: Image.network(
                                  controller.userModel?.musicianProfile!
                                      .gallery![index].imageUrl ??
                                      "",
                                  height: double.infinity,
                                  width: double.infinity,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            );

                          },
                        )
                    )
                        : SizedBox.shrink(),
                  ],
                ),
                buildExpansionTile(
                  context: context,
                  icon: "",
                  title: "Payment Methods",
                  children: [
                    controller.userModel?.musicianProfile != null
                        ? Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10.0, horizontal: 20),
                        child:


                        Wrap(
                          crossAxisAlignment: WrapCrossAlignment.start,
                          alignment: WrapAlignment.start,
                          spacing: 10,
                          runSpacing: 10,
                          children: List.generate(
                            controller.userModel
                                ?.musicianProfile?.paymentMethods?.length ?? 0,
                                (index) => ChoiceChipWidget(
                              label: controller.userModel
                                  ?.musicianProfile?.paymentMethods?[index]??"",
                              size: 14,
                              // No action when tapped
                            ),
                          ),
                        )

                    )
                        : SizedBox.shrink(),
                  ],
                ),
                Widgets.heightSpaceH5,
                Widgets.heightSpaceH4,
              ],
            );
          }),
        ),
      ),
    );
  }

  Widget buildExpansionTile(
      {required context,
      required String icon,
      required String title,
      required List<Widget> children}) {
    return Theme(
      data: Theme.of(context).copyWith(
          dividerColor: Colors.grey.shade50, splashColor: Colors.transparent),
      child: ExpansionTile(
          initiallyExpanded: true,
          dense: true,
          expandedAlignment: Alignment.centerLeft,

          title: Row(

              crossAxisAlignment: CrossAxisAlignment.center,

              children: [

                Texts.textBold(
                  title,
                  size: 16,
                  textAlign: TextAlign.start,
                )
              ]),
          visualDensity: VisualDensity(horizontal: -4, vertical: 0),
          children: children),
    );
  }
}
