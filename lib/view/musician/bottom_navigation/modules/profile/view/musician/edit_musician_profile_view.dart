import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_advanced_avatar/flutter_advanced_avatar.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/utils/extensions.dart';
import 'package:music_app/core/widgets/custom_button.dart';
import 'package:music_app/model/gallery._model.dart';
import 'package:path_provider/path_provider.dart';

import 'package:music_app/core/widgets/widgets.dart';

import '../../../../../../../controller/user_controller.dart';
import '../../../../../../../core/constants/constants_list.dart';
import '../../../../../../../core/utils/utils.dart';
import '../../../../../../../core/widgets/custom_dropdown.dart';
import '../../../../../../../core/widgets/entry_field.dart';
import '../../../../../../../core/widgets/text_widgets.dart';
import '../../../../../create_musician_profile/about_you_step_six.dart';
import '../../../../../create_musician_profile/location_selection/location_selection_screen.dart';
import '../../../home/<USER>/member_detail_model.dart';
import '../../controller/controller.dart';
import 'package:path/path.dart';

class EditMusicianProfileView extends StatefulWidget {
  const EditMusicianProfileView({super.key});

  @override
  State<EditMusicianProfileView> createState() =>
      _EditMusicianProfileViewState();
}

class _EditMusicianProfileViewState extends State<EditMusicianProfileView> {
  late MusicianController userController;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    userController = Get.find();
    userController.nameController.text =
        userController.userController.userModel?.name ?? "";
    userController.descController.text =
        userController.userController.userModel?.musicianProfile?.description ??
            "";
    userController.phoneController.text =
        userController.userController.userModel?.musicianProfile?.phoneNumber ??
            ""; userController.selectedCountryCode.value =
        userController.userController.userModel?.musicianProfile?.countryCode ??
            "";
    userController.locationController.text =
        userController.userController.userModel?.musicianProfile?.location ??
            "";
    userController.selectedLongitude.value =
        userController.userController.userModel!.longitude.toString();

    userController.selectedLatitude.value =
        userController.userController.userModel!.latitude.toString();
    userController.raterHour.text =
        userController.userController.userModel?.musicianProfile?.ratePerHour ??
            "";
    userController.rateEvent.text = userController
            .userController.userModel?.musicianProfile?.ratePerEvent ??
        "";
    userController.websiteController.text =
        userController.userController.userModel?.musicianProfile?.website ?? "";
    if (userController.userController.userModel?.musicianProfile?.spokenLanguages?.isNotEmpty??false) {
  userController.selectedLanguages.clear();

      userController.selectedLanguages.value = userController
          .userController.userModel!.musicianProfile!.spokenLanguages!
          .cast<String>();
    }
    if (userController
        .userController.userModel?.musicianProfile?.tags?.isNotEmpty??false) {

   userController.tags.clear();
      userController.tags.value = userController
          .userController.userModel!.musicianProfile!.tags!
          .cast<String>();
    }
    if (userController
        .userController.userModel?.musicianProfile?.roles?.isNotEmpty??false) {

     userController.selectedRoles.clear();
      userController.selectedRoles.value = userController
          .userController.userModel!.musicianProfile!.roles!
          .cast<String>();
    }
    if (userController
        .userController.userModel?.musicianProfile?.instruments?.isNotEmpty??false) {

     userController.selectedInstruments.clear();
      userController.selectedInstruments.value = userController
          .userController.userModel!.musicianProfile!.instruments!
          .cast<String>();
    }
    if (userController
        .userController.userModel?.musicianProfile?.musicTypes?.isNotEmpty?? false) {
      userController.selectedMusicTypes.clear();
      userController.selectedMusicTypes.value = userController
          .userController.userModel!.musicianProfile!.musicTypes!
          .cast<String>();
    }
    if (userController.userController.userModel?.musicianProfile?.paymentMethods!.isNotEmpty?? false) {

     userController.selectedPaymentMethods.clear();
      userController.selectedPaymentMethods.value = userController
          .userController.userModel!.musicianProfile!.paymentMethods!
          .cast<String>();
    }
    if (userController.userController.userModel?.musicianProfile?.paymentMethods?.isNotEmpty?? false) {
    userController.selectedPaymentMethods.clear();

      userController.selectedPaymentMethods.value = userController
          .userController.userModel!.musicianProfile!.paymentMethods!
          .cast<String>();
    } if (userController.userController.userModel?.musicianProfile?.offeredServices?.isNotEmpty ?? false) {
    userController.selectedServices.clear();
      userController.selectedServices.value = userController
          .userController.userModel!.musicianProfile!.offeredServices!
          .cast<String>();
    }

    if (userController.userController.userModel?.musicianProfile?.socials?.length==0 ||userController.userController.userModel!.musicianProfile!
        .socials!=null ) {
userController.socialMediaList.clear();

      for (var item in userController.userController.userModel!.musicianProfile!.socials!)
      {userController.socialMediaList.clear();
        userController.socialMediaList.add(SocialMedia(type: item.key??"", username: item.value??""));
      }

    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: (){context.hideKeyboard();},
      child: Scaffold(
        backgroundColor: ColorConstants.whiteColor,
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(15.0),
          child: CustomButton(
              label: "Update Profile",
              onTap: () {
                context.hideKeyboard();
                userController.updateMusicianProfile();
              }),
        ),
        body: SingleChildScrollView(
          child: GetBuilder<UserController>(builder: (controller) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Stack(
                  alignment: Alignment.topCenter,
                  clipBehavior: Clip.none,
                  children: [
                    Container(height: .28.sh, width: double.infinity),
                    controller.userModel?.musicianProfile?.headerImageUrl != null
                        ? Widgets.networkImage(
                            controller
                                    .userModel?.musicianProfile?.headerImageUrl ??
                                "",
                            height: .2.sh,
                            width: double.infinity,
                          )
                        : Image.asset(
                            Assets.coverImage,
                            fit: BoxFit.cover, // Ensure the image covers the area
                            height:
                                .2.sh, // Set a fixed height for the cover image
                            width: double.infinity,
                          ),
                    Positioned(
                        top: 50,
                        left: 20,
                        child: GestureDetector(
                            onTap: () {
                              Get.back();
                            },
                            child: const Icon(
                              Icons.arrow_back_ios_new_outlined,
                              size: 20,
                              color: Colors.white,
                            ))),
                    Positioned(
                      bottom: .01.sh,
                      child: GestureDetector(
                        behavior: HitTestBehavior.translucent,
                        onTap: () {
                          // controller.showImageSourceDialog(context,
                          //     isProfile: true);
                        },
                        child: Stack(
                          alignment: Alignment.bottomRight,
                          clipBehavior: Clip.none,
                          children: [
                            AdvancedAvatar(
                              animated: true,
                              size: 120,
                              foregroundDecoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: Colors.grey,
                                  width: 0.0,
                                ),
                              ),
                              child: Widgets.networkImage(
                                  controller.userModel?.imageUrl ?? "",
                                  width: 200,
                                  height: 200),
                            ),
                            Positioned(
                              right: 10,
                              child: GestureDetector(
                                onTap: () {
                                  showImageSourceDialog(context, isProfile: true);
                                },
                                child: CircleAvatar(
                                  radius: 15,
                                  backgroundColor: ColorConstants.redColor,
                                  child: const Icon(
                                    Icons.camera_alt,
                                    color: Colors.white,
                                    size: 15,
                                  ),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: .06.sh,
                      right: 20,
                      child: InkWell(
                        onTap: () {
                          showImageSourceDialog(context, isCover: true);
                        },
                        child: CircleAvatar(
                          radius: 15,
                          backgroundColor: ColorConstants.redColor,
                          child: const Icon(
                            Icons.camera_alt,
                            color: Colors.white,
                            size: 15,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBold("Personal Info", size: 20),
                      Widgets.heightSpaceH2,
                      EntryField(
                        controller: userController.nameController,
                        label: "MusicianName",
                        hint: "Enter here",
                      ),
                      EntryBigField(
                        controller: userController.descController,
                        label: "Describe your talent/experience",
                        hint: "Enter here",
                        maxLength: null,
                        minLines: 7,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            flex: 2,
                            child: Obx(() => EntryField(
                                  suffixIcon: Icons.add,
                                  readOnly: true,
                                  onTap: () => userController
                                      .pickCountryCodeBottomSheet(context),
                                  controller: TextEditingController(
                                      text: userController.selectedCountryCode
                                              .value.isNotEmpty
                                          ? "${userController.selectedCountryCode.value}"
                                          : "+1"),
                                  label: "Code",
                                  hint: "+1",
                                  color: Colors.black45,
                                )),
                          ),
                          SizedBox(width: 8.w),
                          Expanded(
                            flex: 8,
                            child: EntryField(
                              controller: userController.phoneController,
                              label: "Phone Number",
                              hint: "write here",
                              textInputType: TextInputType.phone,
                            ),
                          ),
                        ],
                      ),
                      EntryField(
                          onTrailingTap: () {
                            context.hideKeyboard();
                            Get.to(() => LocationSelectionView(
                                  onLocationSelected: (location, lat, lng) {
                                    userController.locationController.text =
                                        location;
                                    userController.selectedLatitude.value =
                                        lat.toString();
                                    userController.selectedLongitude.value =
                                        lng.toString();
                                  },
                                ));
                          },
                          readOnly: false,
                          controller: userController.locationController,
                          label: "Location",

                          hint: "Enter your location",
                          suffixIcon: Icons.gps_fixed),
                    ],
                  ),
                ),    Widgets.heightSpaceH2,
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBold("Experience and Skills", size: 20),
                      Widgets.heightSpaceH2,
                      Row(
                        children: [
                          Expanded(
                            child: EntryField(
                              textInputType:
                                  TextInputType.numberWithOptions(decimal: true),
                              fillColor: ColorConstants.whiteColor,
                              label: 'Rate per hour',
                              controller: userController.raterHour,
                              hint: "0.00",
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: EntryField(
                              controller: userController.rateEvent,
                              textInputType:
                                  TextInputType.numberWithOptions(decimal: true),
                              fillColor: ColorConstants.whiteColor,
                              label: 'Rate per event',
                              hint: "0.00",
                            ),
                          ),
                        ],
                      ),
                      Obx(
                        () => CustomDropdown(
                            suffixIcon: Icon(
                              Icons.keyboard_arrow_down_outlined,
                              color: Colors.black54,
                              size: 18,
                            ),
                            onTap: () {
                              _showRolesBottomSheet(context);
                            },
                            value: userController.selectedRoles.isEmpty
                                ? "Select here"
                                : userController.selectedRoles.join(", "),
                            hint: "",
                            label: "Roles"),
                      ),
                      Obx(
                        () => CustomDropdown(
                            suffixIcon: Icon(
                              Icons.keyboard_arrow_down_outlined,
                              color: Colors.black54,
                              size: 18,
                            ),
                            onTap: () {
                              _showInstrumentsBottomSheet(context);
                            },
                            value: userController.selectedInstruments.isEmpty
                                ? "Select here"
                                : userController.selectedInstruments.join(", "),
                            hint: "",
                            label: "Instruments"),
                      ),
                      Obx(
                        () => CustomDropdown(
                            suffixIcon: Icon(
                              Icons.keyboard_arrow_down_outlined,
                              color: Colors.black54,
                              size: 18,
                            ),
                            onTap: () {
                              _showServicesBottomSheet(context);
                            },
                            value: userController.selectedServices.isEmpty
                                ? "Select here"
                                : userController.selectedServices.join(", "),
                            hint: "",
                            label: "Offered Services"),
                      ),
                      Obx(
                        () => CustomDropdown(
                            suffixIcon: Icon(
                              Icons.keyboard_arrow_down_outlined,
                              color: Colors.black54,
                              size: 18,
                            ),
                            onTap: () {
                              _showMusicTypesBottomSheet(context);
                            },
                            value: userController.selectedMusicTypes.isEmpty
                                ? "Select here"
                                : userController.selectedMusicTypes.join(", "),
                            hint: "",
                            label: "Music Types"),
                      ),
                      Obx(
                        () => CustomDropdown(
                            suffixIcon: Icon(
                              Icons.keyboard_arrow_down_outlined,
                              color: Colors.black54,
                              size: 18,
                            ),
                            onTap: () {
                              _showLanguageBottomSheet(context);
                            },
                            value: userController.selectedLanguages.isEmpty
                                ? "Select here"
                                : userController.selectedLanguages.join(", "),
                            hint: "",
                            label: "Spoken Language"),
                      ),
                      EntryField(
                        controller: userController.websiteController,
                        label: 'Website',
                        hint: "e.g www.loremipsum.com",
                        fillColor: ColorConstants.whiteColor,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Texts.textBlock(
                                "Social Networks",
                                fontWeight: FontWeight.w600,
                                size: 10,
                              ),
                              InkWell(
                                child: Icon(
                                  Icons.add,
                                  size: 20,
                                ),
                                onTap: () {
                                  userController.addSocialMedia();
                                },
                              ),
                            ],
                          ),
                          Widgets.heightSpaceH1,
                          Obx(() => Column(
                                children: [
                                  if (userController.socialMediaList.isEmpty)
                                    _buildEmptySocialMedia(),
                                  ...List.generate(
                                    userController.socialMediaList.length,
                                    (index) => _buildSocialMediaItem(index),
                                  ),
                                ],
                              )),
                        ],
                      ),
                      Widgets.heightSpaceH05,
                      EntryField(
                        onSubmitted: (v) {
                          if (userController.tagController.text.isNotEmpty) {
                            userController
                                .addTag(userController.tagController.text);
                          }
                        },
                        fillColor: Colors.white,
                        controller: userController.tagController,
                        label: "Tags/keywords",
                        hint: "Add a tag",
                        onChange: (value) {},
                        suffixIcon: Icons.add,
                        onTrailingTap: () {
                          if (userController.tagController.text.isNotEmpty) {
                            userController
                                .addTag(userController.tagController.text);
                          }
                        },
                      ),

                      Obx(() => Container(
                            width: double.infinity,

                            child: userController.tags.isEmpty
                                ? Center(
                                    child: Texts.textNormal("",
                                        size: 12))
                                : Wrap(
                                    spacing: 5.0,
                                    runSpacing: 5.0,
                                    children: userController.tags
                                        .map((tag) => Chip(
                                              label: Texts.textNormal(tag,
                                                  size: 10),
                                              deleteIcon:
                                                  Icon(Icons.close, size: 13),
                                              onDeleted: () =>
                                                  userController.removeTag(tag),
                                              backgroundColor: Colors.white,
                                            ))
                                        .toList(),
                                  ),
                          )),
                      Widgets.heightSpaceH1,
                      Obx(
                        () => CustomDropdown(
                            suffixIcon: Icon(
                              Icons.keyboard_arrow_down_outlined,
                              color: Colors.black54,
                              size: 18,
                            ),
                            onTap: () {
                              _showPaymentBottomSheet(context);
                            },
                            value: userController.selectedPaymentMethods.isEmpty
                                ? "Select here"
                                : userController.selectedPaymentMethods
                                    .join(", "),
                            hint: "",
                            label: "Payment Methods"),
                      ),
                    ],
                  ),
                ),   Widgets.heightSpaceH1,
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBold("Image Gallery", size: 17),
                      Widgets.heightSpaceH2,
                      GridView.builder(
                        padding: EdgeInsets.zero,
                        shrinkWrap: true,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 8,
                          mainAxisSpacing: 8,
                          childAspectRatio: 1,
                        ),
                        itemCount: (controller.userModel?.musicianProfile!.gallery
                                    ?.length ??
                                0) +
                            1,
                        itemBuilder: (context, index) {
                          // Show existing event images first
                          if (index <
                              (controller.userModel?.musicianProfile!.gallery
                                      ?.length ??
                                  0)) {
                            return Stack(
                              clipBehavior: Clip.none,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(12),
                                  child: Image.network(
                                    controller.userModel?.musicianProfile!
                                            .gallery![index].imageUrl ??
                                        "",
                                    height: double.infinity,
                                    width: double.infinity,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Positioned(
                                  right: -6,
                                  top: -6,
                                  child: GestureDetector(
                                    onTap: () {
                                      userController.deleteGalleryPic(
                                          controller.userModel?.musicianProfile!
                                                  .gallery![index].id ??
                                              0,
                                          index);

                                    },
                                    child: CircleAvatar(
                                      radius: 12,
                                      backgroundColor: Colors.red,
                                      child: Icon(Icons.close,
                                          color: Colors.white, size: 14),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          } else {
                            return GestureDetector(
                              onTap: () {
                                showImageSourceDialog(context, isGallery: true);
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.black),
                                ),
                                child: const Center(
                                  child: Icon(Icons.add,
                                      size: 24, color: Colors.black),
                                ),
                              ),
                            );
                          }
                        },
                      )
                    ],
                  ),
                ),
                Widgets.heightSpaceH5,
                Widgets.heightSpaceH4,
              ],
            );
          }),
        ),
      ),
    );
  }

  void _showLanguageBottomSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textBold("Select Languages", size: 18),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            Container(
              height: 300,
              child: ListView.builder(
                itemCount: Data.languages.length,
                itemBuilder: (context, index) {
                  final language = Data.languages[index];
                  return Obx(() => buildTile(language));
                },
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  void _showRolesBottomSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textBold("Select Roles", size: 18),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            Container(
              height: 300,
              child: ListView.builder(
                itemCount: Data.roles.length,
                itemBuilder: (context, index) {
                  final language = Data.roles[index];
                  return Obx(() => Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: InkWell(
                          onTap: () => userController.toggleRoleEdit(language),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: userController.selectedRoles
                                        .contains(language)
                                    ? ColorConstants.primaryColor
                                    : ColorConstants.grayBorderColor,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                    child: Texts.textNormal(
                                  textAlign: TextAlign.start,
                                  "$language",
                                  size: 14,
                                  color: userController.selectedRoles
                                          .contains(language)
                                      ? ColorConstants.primaryColor
                                      : ColorConstants.blackColor,
                                )),
                                if (userController.selectedRoles
                                    .contains(language))
                                  Icon(
                                    Icons.check_circle,
                                    color: ColorConstants.primaryColor,
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ));
                },
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  void _showMusicTypesBottomSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textBold("Select Music Types", size: 18),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            Container(
              height: 300,
              child: ListView.builder(
                itemCount: Data.musicTypes.length,
                itemBuilder: (context, index) {
                  final language = Data.musicTypes[index];
                  return Obx(() => Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: InkWell(
                          onTap: () => userController.toggleMusicType(language),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: userController.selectedMusicTypes
                                        .contains(language)
                                    ? ColorConstants.primaryColor
                                    : ColorConstants.grayBorderColor,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                    child: Texts.textNormal(
                                  textAlign: TextAlign.start,
                                  "$language",
                                  size: 14,
                                  color: userController.selectedMusicTypes
                                          .contains(language)
                                      ? ColorConstants.primaryColor
                                      : ColorConstants.blackColor,
                                )),
                                if (userController.selectedMusicTypes
                                    .contains(language))
                                  Icon(
                                    Icons.check_circle,
                                    color: ColorConstants.primaryColor,
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ));
                },
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  void _showInstrumentsBottomSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textBold("Select Instruments", size: 18),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            Container(
              height: 300,
              child: ListView.builder(
                itemCount: Data.instruments.length,
                itemBuilder: (context, index) {
                  final language = Data.instruments[index];
                  return Obx(() => Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: InkWell(
                          onTap: () =>
                              userController.toggleInstrument(language),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: userController.selectedInstruments
                                        .contains(language)
                                    ? ColorConstants.primaryColor
                                    : ColorConstants.grayBorderColor,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                    child: Texts.textNormal(
                                  textAlign: TextAlign.start,
                                  "$language",
                                  size: 14,
                                  color: userController.selectedInstruments
                                          .contains(language)
                                      ? ColorConstants.primaryColor
                                      : ColorConstants.blackColor,
                                )),
                                if (userController.selectedInstruments
                                    .contains(language))
                                  Icon(
                                    Icons.check_circle,
                                    color: ColorConstants.primaryColor,
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ));
                },
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  void _showServicesBottomSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textBold("Select Services", size: 18),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            Container(
              height: 300,
              child: ListView.builder(
                itemCount: Data.offeredServices.length,
                itemBuilder: (context, index) {
                  final language = Data.offeredServices[index];
                  return Obx(() => Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: InkWell(
                          onTap: () => userController.toggleService(language),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: userController.selectedServices
                                        .contains(language)
                                    ? ColorConstants.primaryColor
                                    : ColorConstants.grayBorderColor,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                    child: Texts.textNormal(
                                  textAlign: TextAlign.start,
                                  "$language",
                                  size: 14,
                                  color: userController.selectedServices
                                          .contains(language)
                                      ? ColorConstants.primaryColor
                                      : ColorConstants.blackColor,
                                )),
                                if (userController.selectedServices
                                    .contains(language))
                                  Icon(
                                    Icons.check_circle,
                                    color: ColorConstants.primaryColor,
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ));
                },
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  void _showPaymentBottomSheet(BuildContext context) {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textBold("Select Payment Methods", size: 18),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            Container(
              height: 300,
              child: ListView.builder(
                itemCount: Data.paymentMethods.length,
                itemBuilder: (context, index) {
                  final language = Data.paymentMethods[index];
                  return Obx(() => Padding(
                        padding: const EdgeInsets.only(bottom: 10.0),
                        child: InkWell(
                          onTap: () =>
                              userController.togglePaymentMethod(language),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(10),
                              border: Border.all(
                                color: userController.selectedPaymentMethods
                                        .contains(language)
                                    ? ColorConstants.primaryColor
                                    : ColorConstants.grayBorderColor,
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Flexible(
                                    child: Texts.textNormal(
                                  textAlign: TextAlign.start,
                                  "$language",
                                  size: 14,
                                  color: userController.selectedPaymentMethods
                                          .contains(language)
                                      ? ColorConstants.primaryColor
                                      : ColorConstants.blackColor,
                                )),
                                if (userController.selectedPaymentMethods
                                    .contains(language))
                                  Icon(
                                    Icons.check_circle,
                                    color: ColorConstants.primaryColor,
                                    size: 20,
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ));
                },
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  Widget buildTile(String language) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: InkWell(
        onTap: () => userController.toggleLanguage(language),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: userController.selectedLanguages.contains(language)
                  ? ColorConstants.primaryColor
                  : ColorConstants.grayBorderColor,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                  child: Texts.textNormal(
                textAlign: TextAlign.start,
                "$language",
                size: 14,
                color: userController.selectedLanguages.contains(language)
                    ? ColorConstants.primaryColor
                    : ColorConstants.blackColor,
              )),
              if (userController.selectedLanguages.contains(language))
                Icon(
                  Icons.check_circle,
                  color: ColorConstants.primaryColor,
                  size: 20,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptySocialMedia() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: ColorConstants.grayBorderColor,
            width: 1,
          ),
        ),
        child: Center(
          child: Text(
            "Tap + to add social media profiles",
            style: TextStyle(
              color: ColorConstants.greyTextColor,
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSocialMediaItem(int index) {
    final socialMedia = userController.socialMediaList[index];
    final socialMediaTypes = [
      'Instagram',
      'Facebook',
      'Twitter',
      'TikTok',
      'YouTube',
      'SoundCloud',
      'Spotify'
    ];
    Map<String, String> socialMediaUrls = {
      'Instagram': 'https://www.instagram.com/',
      'Facebook': 'https://www.facebook.com/',
      'Twitter': 'https://twitter.com/',
      'TikTok': 'https://www.tiktok.com/@',
      'YouTube': 'https://www.youtube.com/',
      'SoundCloud': 'https://soundcloud.com/',
      'Spotify': 'https://open.spotify.com/',
    };
    Map<String, String> socialMediaIcons = {
      'Instagram': Assets.instagramIcon,
      'Facebook': Assets.fbIcon, // Replace with actual Facebook icon
      'Twitter': Assets.xIcon, // Replace with actual Twitter icon
      'TikTok': Assets.tikIcon, // Replace with actual TikTok icon
      'YouTube': Assets.youtubeIcon, // Replace with actual YouTube icon
      'SoundCloud':
          Assets.soundcloudIcon, // Replace with actual SoundCloud icon
      'Spotify': Assets.spotifyIcon, // Replace with actual Spotify icon
    };

    return Padding(
      padding: const EdgeInsets.only(bottom: 10.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
            color: ColorConstants.grayBorderColor,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Widgets.widthSpaceW3,
            GestureDetector(
              onTap: () {
                _showSocialMediaTypeBottomSheet(
                    index, socialMediaTypes, socialMediaIcons);
              },
              child: Row(
                children: [
                  Image.asset(
                    socialMediaIcons[socialMedia.type] ?? Assets.instagramIcon,
                    height: 20,
                  ),
                  Widgets.widthSpaceW05,
                  Icon(
                    Icons.keyboard_arrow_down_outlined,
                    color: Colors.black54,
                    size: 18,
                  ),
                ],
              ),
            ),
            Widgets.widthSpaceW1,
            Expanded(
              child: TextField(controller: TextEditingController(
                  text: socialMedia.username,
                ),
                decoration: InputDecoration(
                  hintText: "username",
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                  hintStyle: TextStyle(
                      fontFamily: "PoppinsRegular",
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: ColorConstants.textHintColor),
                ),
                style: const TextStyle(
                    fontFamily: "PoppinsRegular",
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                    color: Colors.black),
                onChanged: (value) {
                  userController.updateSocialMediaUsername(index, value);
                },
              ),
            ),
            Widgets.widthSpaceW1,
            IconButton(
              icon: Icon(Icons.close, color: ColorConstants.redColor, size: 20),
              onPressed: () {
                userController.removeSocialMedia(index);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSocialMediaTypeBottomSheet(
      int index, List<String> socialMediaTypes, var socialIcons) {
    Get.bottomSheet(
      Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Texts.textBold("Select Social Media", size: 18),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
            Container(
              height: 300,
              child: ListView.separated(
                itemCount: socialMediaTypes.length,
                itemBuilder: (context, i) {
                  final type = socialMediaTypes[i];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    dense: true,
                    leading: Image.asset(
                      socialIcons[type],
                      height: 20,
                    ),
                    title: Texts.textNormal(type,
                        textAlign: TextAlign.start, size: 13),
                    horizontalTitleGap: 4,
                    onTap: () {
                      userController.updateSocialMediaType(index, type);
                      Get.back();
                    },
                  );
                },
                separatorBuilder: (BuildContext context, int index) {
                  return Divider(
                    color: Colors.grey.shade200,
                  );
                },
              ),
            ),
          ],
        ),
      ),
      isScrollControlled: true,
    );
  }

  Future<File?> compressImage(File file) async {
    final dir = await getTemporaryDirectory();
    final targetPath =
        '${dir.path}/${basename(file.path)}_compressed_${Utils.generateUniqueNumber()}.jpg';

    var result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: 50, // 50% compression
    );

    return result != null ? File(result.path) : null;
  }

  void showImageSourceDialog(BuildContext context,
      {bool isProfile = false, bool isCover = false, bool isGallery = false}) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBlock("Select an action", align: TextAlign.center),
                  GestureDetector(
                    onTap: () => Get.back(),
                    child: const Icon(Icons.clear, color: Colors.black54),
                  ),
                ],
              ),
              Widgets.heightSpaceH2,
              ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take Photo'),
                onTap: () {
                  Get.back();
                  pickImage(ImageSource.camera,
                      isProfile: isProfile,
                      isCover: isCover,
                      isGallery: isGallery);
                },
              ),
              Divider(color: ColorConstants.greyTextColor, thickness: .5),
              ListTile(
                dense: true,
                contentPadding: EdgeInsets.zero,
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Get.back();
                  pickImage(ImageSource.gallery,
                      isProfile: isProfile,
                      isCover: isCover,
                      isGallery: isGallery);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> pickImage(ImageSource source,
      {bool isProfile = false,
      bool isCover = false,
      bool isGallery = false}) async {
    try {
      final XFile? file = await ImagePicker().pickImage(source: source);
      if (file != null) {
        // Crop the image
        final croppedFile = await ImageCropper().cropImage(
          sourcePath: file.path,
          aspectRatio: isCover
              ? const CropAspectRatio(ratioX: 16, ratioY: 9)
              : isProfile
                  ? const CropAspectRatio(ratioX: 1, ratioY: 1)
                  : const CropAspectRatio(ratioX: 1, ratioY: 1),
          compressQuality: 70,
          uiSettings: [
            AndroidUiSettings(
              toolbarTitle: 'Adjust Image',
              toolbarColor: ColorConstants.primaryColor,
              toolbarWidgetColor: Colors.white,
              initAspectRatio: isGallery
                  ? CropAspectRatioPreset.ratio16x9
                  : isProfile
                      ? CropAspectRatioPreset.square
                      : CropAspectRatioPreset.ratio16x9,
              lockAspectRatio: true,
            ),
          ],
        );

        if (croppedFile != null) {
          File? compressedFile = await compressImage(File(croppedFile.path));
          if (compressedFile != null) {
            if (isProfile) {
              userController.profileImagePath?.value = compressedFile.path;

              userController.changeProfilePic();
            } else if (isCover) {
              userController.coverImagePath?.value = compressedFile.path;
              userController.changeBannerPic();
            } else if (isGallery == true) {
              userController.galleryImagePath?.value = compressedFile!.path;
              userController.addGalleryPic();
            }
          }
        }
      }
    } catch (e) {
      print('Error picking image: $e');
    }
  }
}
