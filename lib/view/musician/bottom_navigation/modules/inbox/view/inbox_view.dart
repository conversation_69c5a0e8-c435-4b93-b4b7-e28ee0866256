import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:get/get.dart';
import 'package:music_app/core/constants/assets_constants.dart';
import 'package:music_app/core/constants/color_constants.dart';
import 'package:music_app/core/constants/padding_constants.dart';
import 'package:music_app/core/routes/app_routes.dart';
import 'package:music_app/core/widgets/entry_field.dart';
import 'package:music_app/core/widgets/text_widgets.dart';
import 'package:music_app/core/widgets/widgets.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/home_controller.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/menu_screen.dart';
import 'package:music_app/view/musician/bottom_navigation/modules/home/<USER>/notification_screen.dart';
import '../../../../../../controller/user_controller.dart';
import '../../../../../../core/services/socket_service.dart';
import '../../../../../../core/widgets/custom_button.dart';
import '../../../../../../core/widgets/shimmer_effect.dart';
import '../../../../subscription/view/subscribe_view.dart';
import '../controller/chat_controller.dart';
import 'chat_view.dart';


class InboxView extends StatefulWidget {
  const InboxView({super.key});

  @override
  State<InboxView> createState() => _InboxViewState();
}

class _InboxViewState extends State<InboxView> {
  late ChatController controller;
  final ScrollController scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    controller= Get.put(ChatController());

    if(Get.find<UserController>().token!=null)
    {SocketService.initializeSocket();
      controller.fetchChatsBackground(page: 1);
    }

    scrollController.addListener(scrollListener);
  }

  @override
  void dispose() {
    scrollController.removeListener(scrollListener);
    super.dispose();
  }


  scrollListener() {
    if (scrollController.position.pixels ==
        scrollController.position.maxScrollExtent &&
        controller.totalChats.value >
            controller.chats.length) {
      controller.fetchChats(
          page:  controller.currentPage.value + 1);
      controller.currentPage.value++; // Increment the page counter
    }
  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorConstants.whiteColor,
      appBar: AppBar(
        elevation: 0,
        scrolledUnderElevation: 0,
        backgroundColor: ColorConstants.whiteColor,
        centerTitle: true,
        automaticallyImplyLeading: false,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [

            Texts.textBold(
              "Chats",
              size: 22,
            ),
            Expanded(
              child: Widgets.widthSpaceW05,
            ),
            Stack(
              alignment: Alignment.topRight,clipBehavior: Clip.none,
              children: [
                GestureDetector(
                  onTap: () {
                    Get.to(()=>NotifcationsView());
                  },
                  child: SizedBox(
                    height: 20.h,
                    width: 20.w,
                    child: Image.asset(
                      Assets.notificationIcon,
                      fit: BoxFit.fitHeight,
                    ),
                  ),
                ),
                Positioned(
                  right: -2,
                  top: -2,
                  child: Obx(() {
                    final count = Get.find<UserController>().unreadNotificationsCount.value;
                    return count > 0
                        ? CircleAvatar(
                      radius: 7,
                      backgroundColor: Colors.red,
                      child: Text(
                        count > 9 ? '9+' : count.toString(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 7,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                        : const SizedBox.shrink();
                  }),
                ),
              ],
            ),
            Widgets.widthSpaceW2,
            GetBuilder<UserController>(init: UserController(),
                builder: (controller) {
                  return controller.userModel?.role!="client"?GestureDetector(
                    onTap: () {
                      Get.to(() => MenuScreen());
                    },
                    child: SizedBox(
                      height: 15.h,
                      child: Image.asset(
                        Assets.hamburgerIcon,
                        fit: BoxFit.fitHeight,
                      ),
                    ),
                  ):SizedBox();
                }
            )
          ],
        ),
      ),
      body: SafeArea(
        child: GetBuilder<UserController>(
          builder: (userController) {
            if (userController.token == null) {
              return Widgets.buildLoginPrompt(title: 'Chats');
            } else if (userController.userModel?.hasSubscription == false) {
              return Widgets.buildSubscriptionPrompt(title: "Chats",);
            } else {
              return buildChats();
            }
          }
        ),
      ),
    );
  }



  Widget buildChats() {
    return Obx(
          () {
        return RefreshIndicator(backgroundColor: ColorConstants.blackColor,   color: ColorConstants.whiteColor,
          onRefresh: () async {
controller.fetchChats(page: 1);
          },
          child:CustomScrollView(   controller: scrollController,
              physics: AlwaysScrollableScrollPhysics(), // Enable scrolling always
              slivers: [
              SliverToBoxAdapter(


            child: Padding(
              padding: const EdgeInsets.all( 15.0),
              child: Column(
                children: [

                  controller.isLoading.value
                      ? const ShimmerListSkeleton()
                      :controller.chats.isNotEmpty
                      ? ListView.separated(
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        final chat = controller.chats[index];
                        return InkWell(

                            onTap: (){

                              controller.selectedChat.value=chat;

                              Get.to(()=> ChatView(chatId: chat.chatId.toString(),chatUser: chat.otherUser,));
                            },

                            child: Widgets.chatCard(chat: chat));

                      },
                      separatorBuilder: (context, index) {
                        return Divider(color: ColorConstants.grayBorderColor, thickness: .5);
                      },
                      itemCount:controller.chats.length ?? 0)
                      : Widgets.noRecordsFound(title: "No Chats"),
                  if (  controller.isLoadingMore.value)
                    Widgets.moreLoading(),
                ],
              ),
            ),
            )]),
        );
      },
    );
  }
}
