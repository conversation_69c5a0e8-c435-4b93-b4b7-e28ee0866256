class SubscriptionContent {
  int? id;
  String? name;
  String? slug;
  String? userType;
  String? price;
  String? billingCycle;
  num? trialDays;
  String? description;
  List<String>? features;
  bool? isActive;
  bool? isDefault;
  String? createdAt;
  String? updatedAt;

  SubscriptionContent(
      {this.id,
        this.name,
        this.slug,
        this.userType,
        this.price,
        this.billingCycle,
        this.trialDays,
        this.description,
        this.features,
        this.isActive,
        this.isDefault,
        this.createdAt,
        this.updatedAt});

  SubscriptionContent.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    slug = json['slug'];
    userType = json['user_type'];
    price = json['price'];
    billingCycle = json['billing_cycle'];
    trialDays = json['trial_days'];
    description = json['description'];
    features = json['features'].cast<String>();
    isActive = json['is_active'];
    isDefault = json['is_default'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['slug'] = this.slug;
    data['user_type'] = this.userType;
    data['price'] = this.price;
    data['billing_cycle'] = this.billingCycle;
    data['trial_days'] = this.trialDays;
    data['description'] = this.description;
    data['features'] = this.features;
    data['is_active'] = this.isActive;
    data['is_default'] = this.isDefault;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    return data;
  }
}
