import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:music_app/view/musician/subscription/view/payment_client_success_view.dart';
import 'package:music_app/view/musician/subscription/view/payment_success_view.dart';
import 'package:webview_flutter/webview_flutter.dart';

class PaymentMusicianProcessView extends StatefulWidget {
  final String url;
  final bool firstTime;
  PaymentMusicianProcessView({required this.url,required this.firstTime});

  @override
  _PaymentMusicianProcessViewState createState() => _PaymentMusicianProcessViewState();
}

class _PaymentMusicianProcessViewState extends State<PaymentMusicianProcessView> {
  late final WebViewController _controller;
  bool isLoading = true;

  @override
  void initState() {
    super.initState();

    final WebViewController controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..setNavigationDelegate(
        NavigationDelegate(
          onNavigationRequest: (NavigationRequest request) {

            if (request.url.contains('cancel')) {
              Get.back();
              return NavigationDecision.prevent;
            } else if (request.url.contains("success")) {

              Get.off(()=>PaymentMusicianSuccessView(firstTIme: widget.firstTime,));
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
          onPageFinished: (String url) {
            setState(() {
              isLoading = false;
            });
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url));

    _controller = controller;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            WebViewWidget(controller: _controller),
            if (isLoading)
              Container(
                color: Colors.white,
                height: MediaQuery.of(context).size.height,
                width: MediaQuery.of(context).size.width,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 30.0),
                    Text("Redirecting to payment"),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}
