import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../../constants/my_colors.dart';
import '../../../constants/my_fonts.dart';
import '../../../constants/my_string.dart';
import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../../authentication/controller/authentication_controller.dart';


class LanguageSelectionPartSixView extends StatefulWidget {
  const LanguageSelectionPartSixView({super.key});

  @override
  State<LanguageSelectionPartSixView> createState() => _LanguageSelectionPartSixViewState();
}

class _LanguageSelectionPartSixViewState extends State<LanguageSelectionPartSixView> {
  AuthenticationController authenticationController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textBold("Let’s Speak Your Language!",
            size: 27, maxline: 2, align: TextAlign.start, color: Colors.black),
        Widgets.heightSpaceH2,
        Texts.textMedium(
            "Pick your preferred language so you can enjoy Go Bananas in the way that feels most natural to you!",
            align: TextAlign.start,
            color: Colors.black54,
            size: 13),
        Widgets.heightSpaceH3,
        ListView.builder(
            itemCount: authenticationController.spokenLangSelect.length,
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            itemBuilder: (context, index) {
              bool isSelected =
              authenticationController.spokenLangList.contains(   authenticationController.spokenLangSelect[index]);

              return Container(
                margin: EdgeInsets.only(top: 10),
                padding: EdgeInsets.only(left: 15, right: 15),
                height: 54,
                decoration: BoxDecoration(
                    color: isSelected
                        ? ColorConstants.lightPrimaryColor
                        : ColorConstants.lightGrey,
                    borderRadius: BorderRadius.all(Radius.circular(12.0)),
                    border: Border.all(
                        color: isSelected == true
                            ? Colors.black
                            : Colors.white,
                        width: 1)),
                child: InkWell(
                  onTap: () {
                    if (isSelected) {
                      authenticationController.spokenLangList.remove(   authenticationController.spokenLangSelect[index]);
                    } else {
                      authenticationController.spokenLangList.add(   authenticationController.spokenLangSelect[index]);
                    }
setState(() {

});

                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Texts.textBlock(
                        authenticationController.spokenLangSelect[index].toString(),
                        fontWeight:  FontWeight.w500,

                        size: 14,
                      ),
                      SvgPicture.asset(
                          isSelected == true
                              ? Assets.selectedItem
                              : Assets.unselectedItem,
                          width: 20,
                          height: 20)
                    ],
                  ),
                ),
              );
            }),

      ],
    );
  }
}
