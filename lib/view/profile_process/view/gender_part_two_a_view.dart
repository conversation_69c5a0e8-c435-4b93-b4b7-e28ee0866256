import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

import '../../../constants/my_fonts.dart';
import '../../../constants/my_string.dart';
import '../../../core/constants/assets_constants.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/widgets/text_widgets.dart';
import '../../../core/widgets/widgets.dart';
import '../../authentication/controller/authentication_controller.dart';

class GenderPartTwoAView extends StatefulWidget {
  const GenderPartTwoAView({super.key});

  @override
  State<GenderPartTwoAView> createState() => _GenderPartTwoAViewState();
}

class _GenderPartTwoAViewState extends State<GenderPartTwoAView> {
  AuthenticationController authenticationController = Get.find();

  @override
  void initState() {
    super.initState();
    // Pre-select the first gender option (index 0)
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Texts.textBold("Let's Personalize Your Experience.",
            size: 27,
            maxline: 2,
            align: TextAlign.start,
            color: Colors.black),
        Widgets.heightSpaceH2,
        Texts.textMedium(
            "We’d love to know how to refer to you! This helps us tailor your Go Bananas experience and connect you with the right crowd.",
            align: TextAlign.start,
            color: Colors.black54,
            size: 13),
        Widgets.heightSpaceH3,

        Texts.textSemiBold("Which gender best describes you?",
            size: 15,
            maxline: 2,
            align: TextAlign.start,
            color: Colors.black),

        Widgets.heightSpaceH05,

        Obx(() => ListView.builder(
          itemCount: authenticationController.genderSelection.length,
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          itemBuilder: (context, index) {
            bool isSelected = authenticationController.genderSelected.value == index;

            return Container(
              margin: EdgeInsets.only(top: 10),
              padding: EdgeInsets.only(left: 15, right: 15),
              height: 54,
              decoration: BoxDecoration(
                color: isSelected?ColorConstants.lightPrimaryColor:ColorConstants.lightGrey,
                borderRadius: BorderRadius.all(Radius.circular(12.0)),
                border: Border.all(
                  color: isSelected ? ColorConstants.blackColor : Colors.white,
                  width: 1
                )
              ),
              child: InkWell(
                onTap: () {setState(() {});
                  authenticationController.genderSelected.value =index;authenticationController.genderValue.value = authenticationController.genderSelection[index];
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Texts.textBlock(
                      authenticationController.genderSelection[index],
                      fontWeight:  FontWeight.w500,
                      size: 14,
                    ),
                    SvgPicture.asset(
                      isSelected ? Assets.selectedItem : Assets.unselectedItem,
                      width: 20,
                      height: 20
                    )
                  ],
                ),
              ),
            );
          }
        )),
      ],
    );
  }
}
