import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/constants/padding_constants.dart';
import 'package:MenoPal/core/widgets/custom_button.dart';
import 'package:MenoPal/core/widgets/entry_field.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/webcentered_widget.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:flutter/material.dart';


class ChangeEmail extends StatelessWidget {
  const ChangeEmail({super.key});

  @override
  Widget build(BuildContext context) {
    return  WebCenteredWrapper(
      child: Stack(
        children: [

          Container(color: Colors.white,),
          Container(
            height: 200,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  ColorConstants.gradient1.withAlpha((0.5 * 255).toInt()),

                  ColorConstants.gradient2.withAlpha((0.5 * 255).toInt()),
                  ColorConstants.whiteColor.withAlpha((0.5 * 255).toInt()),


                ],

              ),
            ),
          ),
          Scaffold(
            backgroundColor: ColorConstants.transparentColor,
          appBar: AppBar(elevation: 0,scrolledUnderElevation: 0,backgroundColor: Colors.transparent,),
            body: Padding(
              padding:PaddingConstants.screenPaddingHalf,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Texts.textBold('Change Email',size: 18),
                  Widgets.heightSpaceH05,
                  Texts.textNormal("Change your email address",size: 12),
                  Widgets.heightSpaceH2,
                  Widgets.divider(),
                  Widgets.heightSpaceH2,
                  EntryField(label: "New Email Address",),
                  Widgets.heightSpaceH2,
                  CustomButton(
                    label: 'Update email',
                    backgroundColor: ColorConstants.darkPrimaryColor,
                  ),



                ],

              ),
            ),

          ),
        ],
      ),
    );
  }
}
