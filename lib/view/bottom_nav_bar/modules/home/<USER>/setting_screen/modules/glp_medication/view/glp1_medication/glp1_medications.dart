import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/webcentered_widget.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/home/<USER>/setting_screen/modules/glp_medication/view/glp1_medication/controller.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/home/<USER>/setting_screen/modules/glp_medication/view/medication_database/medications_database.dart';

import 'package:flutter/material.dart';



import 'package:get/get.dart';

import '../manage_medications.dart';






class GlpMedications extends StatelessWidget {
  GlpMedications({super.key});

  final GlpMedicationsController controller =
  Get.put(GlpMedicationsController());

  @override
  Widget build(BuildContext context) {
    return WebCenteredWrapper(
      child: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  ColorConstants.gradient1.withAlpha((0.5 * 255).toInt()),

                  ColorConstants.gradient2.withAlpha((0.5 * 255).toInt()),
                  ColorConstants.whiteColor.withAlpha((0.5 * 255).toInt()),


                ],

              ),
            ),
          ),
          GestureDetector(
            onTap: (){
            FocusScope.of(context).unfocus();
            },
            child: Scaffold(
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                leading: const BackButton(),
              ),
              body: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Texts.textBold("GLP-1 Medications", size: 18),
                        Widgets.heightSpaceH05,
                        Texts.textNormal(
                          "Explore GLP-1 medications designed to support weight management and metabolic health.",
                          textAlign: TextAlign.start,
                          size: 12,
                          maxLines: 4,
                        ),
                      ],
                    ),
                  ),
                  Widgets.heightSpaceH2,

                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: TabBar(

                      controller: controller.tabController,
                      labelColor: Colors.black,
                      unselectedLabelColor: Colors.grey,
                      indicatorColor: ColorConstants.darkPrimaryColor,
                      indicatorSize: TabBarIndicatorSize.tab,
                      tabs: controller.myTabs,
                      labelStyle: TextStyle(                     // Style for selected tab
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                          fontFamily: "Montserrat"
                      ),
                    ),
                  ),
                  Widgets.heightSpaceH1,
                  Expanded(
                    child: TabBarView(
                      controller: controller.tabController,
                      children: [
                        ManageMedications(),
                        GlpMedicationDatabase(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}






