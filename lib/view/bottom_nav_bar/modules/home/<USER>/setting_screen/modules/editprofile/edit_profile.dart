import 'package:MenoPal/controller/date_time_controller.dart';
import 'package:MenoPal/core/constants/assets_constants.dart';
import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/constants/padding_constants.dart';
import 'package:MenoPal/core/widgets/bottom_sheet.dart';
import 'package:MenoPal/core/widgets/custom_button.dart';
import 'package:MenoPal/core/widgets/custom_dropdown.dart';
import 'package:MenoPal/core/widgets/entry_field.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/webcentered_widget.dart';
import 'package:MenoPal/core/widgets/widgets.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';


import 'controller.dart';

class EditProfile extends StatelessWidget {
  EditProfile({super.key});
  TextEditingController dobController = TextEditingController();

  final ProfileController controller = Get.put(ProfileController());

  String _getDescription(int index) {
    switch (index) {
      case 0:
        return "We need to process your personal data to provide our core health tracking services. This is required to use the application.";
      case 1:
        return "Receive updates about new features, health tips, and promotions via email.";
      case 2:
        return "Allow us to share anonymized data with trusted third parties for service improvement.";
      case 3:
        return "Contribute your anonymized health data to research studies to advance women’s health science.";
      default:
        return "";
    }
  }



  @override
  Widget build(BuildContext context) {
    return WebCenteredWrapper(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Stack(
          children: [
            Container(color: Colors.white,),
            Container(
              height: 200,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    ColorConstants.gradient1.withAlpha((0.5 * 255).toInt()),

                    ColorConstants.gradient2.withAlpha((0.5 * 255).toInt()),
                    ColorConstants.whiteColor.withAlpha((0.5 * 255).toInt()),


                  ],

                ),
              ),
            ),
            Scaffold(
              backgroundColor: ColorConstants.transparentColor,
              appBar: AppBar(
                elevation: 0,
                scrolledUnderElevation: 0,
                backgroundColor: ColorConstants.transparentColor,
              ),
              body: Padding(
                padding: PaddingConstants.screenPaddingHalf.copyWith(top: 0),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBold("Edit Profile", size: 18),
                      Widgets.heightSpaceH05,
                      Texts.textNormal(
                          "Update your personal information and keep your profile up to date.",
                          textAlign: TextAlign.start,
                          size: 12),
                      Widgets.heightSpaceH2,
                      Center(
                        child: Stack(
                          clipBehavior: Clip.none,
                          alignment: Alignment.bottomRight,
                          children: [
                            CircleAvatar(
                              radius: 40,
                              backgroundImage: AssetImage(Assets.profileImg),
                            ),
                            Container(
                              padding: const EdgeInsets.all(
                                  1), // Thickness of the white border
                              decoration: BoxDecoration(
                                color: Colors.white, // Border color
                                shape: BoxShape.circle,
                              ),
                              child: CircleAvatar(
                                radius: 12,
                                backgroundColor: ColorConstants.darkPrimaryColor,
                                child: Icon(
                                  Icons.camera_alt_outlined,
                                  color: ColorConstants.whiteColor,
                                  size: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Widgets.heightSpaceH1,
                      EntryField(
                        label: "Name",
                        hint: "Freddy",
                      ),
                      Widgets.heightSpaceH05,
                      EntryField(
                        label: "Last Name",
                        hint: "Mercury",
                      ),
                      Widgets.heightSpaceH05,
                      EntryField(
                        label: "Email Address",
                        hint: "<EMAIL>",
                      ),
                      Widgets.heightSpaceH05,
                      EntryField(
                        label: "DOB",
                        prefixIcon: Assets.calendarIcon,
                        controller: dobController,
                        hint: "07/04/2024",
                        readOnly: true,
                        onTap: () => pickDateAndSetController(
                            context: context, controller: dobController),
                      ),
                      Widgets.heightSpaceH05,
                      Obx(() => CustomDropdown(
                            iconColor: ColorConstants.blackColor,
                            color: Colors.transparent,
                            onTap: () {
                              controller.pickCountry(context);
                            },
                            value: controller.selectedCountry.value == ""
                                ? null
                                : controller.selectedCountry.value,
                            hint: "--Select--",
                            label: "Country",
                          )),
                      Widgets.heightSpaceH05,
                      Obx(() => CustomDropdown(
                            onTap: () => controller.pickCity(context),
                            value: controller.selectedCity.value == ""
                                ? null
                                : controller.selectedCity.value,
                            hint: 'e.g. New York',
                            label: 'City',
                            color: ColorConstants.transparentColor,
                            iconColor: ColorConstants.blackColor,
                          )),
                      Widgets.heightSpaceH05,
                      Row(
                        children: [
                          Expanded(
                              flex: 2,
                              child: EntryField(
                                onTap: () {},
                                hint: '0',
                                textInputType: TextInputType.number,
                                label: 'Height',
                                color: ColorConstants.transparentColor,
                              )),
                          Widgets.widthSpaceW2,
                          Expanded(
                            child: Obx(
                              () => CustomDropdown(
                                onTap: () {
                                  showBottomSheetPicker(
                                    title: "Select Height",
                                    items: ['cm', 'ft', "inches"],
                                    onItemSelected: (selected) {
                                      controller.selectedUnit.value = selected;
                                    },
                                  );
                                },
                                value: controller.selectedUnit.value,
                                hint: 'cm',
                                label: '',
                                color: ColorConstants.transparentColor,
                                iconColor: ColorConstants.blackColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Widgets.heightSpaceH05,
                      Row(
                        children: [
                          Expanded(
                              flex: 2,
                              child: EntryField(
                                onTap: () {},
                                textInputType: TextInputType.number,
                                hint: '0',
                                label: 'Weight',
                                color: ColorConstants.transparentColor,
                              )),
                          Widgets.widthSpaceW2,
                          Expanded(
                            child: Obx(
                              () => CustomDropdown(
                                onTap: () {
                                  showBottomSheetPicker(
                                    title: "Select Weight",
                                    items: ['kg', 'lb', "st"],
                                    onItemSelected: (selected) {
                                      controller.selectedWeightUnit.value =
                                          selected;
                                    },
                                  );
                                },
                                value: controller.selectedWeightUnit.value,
                                hint: 'kg',
                                label: '',
                                color: ColorConstants.transparentColor,
                                iconColor: ColorConstants.blackColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Widgets.heightSpaceH05,
                      Obx(() => CustomDropdown(
                            onTap: () {
                              showBottomSheetPicker(
                                title: "Select Ethnicity",
                                items: controller.ethnicityOptions,
                                onItemSelected: (selected) {
                                  controller.selectedEthnicity.value = selected;
                                },
                              );
                            },
                            value: controller.selectedEthnicity.value,
                            hint: 'Select your identity',
                            label: 'Ethnicity',
                            color: ColorConstants.transparentColor,
                            iconColor: ColorConstants.blackColor,
                          )),
                      Texts.textNormal(
                          'This information helps us provide more personalized insights about your health.',
                          size: 12,
                          textAlign: TextAlign.start),
                      Widgets.heightSpaceH2,
                      Obx(() => CustomDropdown(
                            onTap: () {
                              showBottomSheetPicker(
                                title: "Select Dietary Preference",
                                items: controller.dietaryOptions,
                                onItemSelected: (selected) {
                                  controller.selectedDietaryPreference.value =
                                      selected;
                                },
                              );
                            },
                            value: controller.selectedDietaryPreference.value,
                            hint: 'Select your dietary preference',
                            label: 'Dietary Preference',
                            color: ColorConstants.transparentColor,
                            iconColor: ColorConstants.blackColor,
                          )),
                      Widgets.heightSpaceH2,
                      Obx(() => CustomDropdown(
                            onTap: () {
                              showBottomSheetPicker(
                                title: "Select Smoking Status",
                                items: [
                                  "Non-smoker",
                                  "Current-smoker",
                                  'Former smoker'
                                ],
                                onItemSelected: (selected) {
                                  controller.smokingStatus.value = selected;
                                },
                              );
                            },
                            value: controller.smokingStatus.value,
                            hint: 'smoking status',
                            label: 'Smoking status',
                            color: ColorConstants.transparentColor,
                            iconColor: ColorConstants.blackColor,
                          )),
                      Widgets.heightSpaceH2,
                      Obx(() => CustomDropdown(
                            onTap: () {
                              showBottomSheetPicker(
                                title: "Select Menopause Status",
                                items: [
                                  "Pre-menopause (irregular periods)",
                                  "Perimenopause (irregular periods,beginning of symptoms)",
                                  'Menopause(12 consecutive months without a period',
                                  'Post-menopause(years after menopause)',
                                  "I'm not sure"
                                ],
                                onItemSelected: (selected) {
                                  controller.menopauseStatus.value = selected;
                                },
                              );
                            },
                            value: controller.menopauseStatus.value,
                            hint: 'Select your menopause status',
                            label: 'Menopause Status',
                            color: ColorConstants.transparentColor,
                            iconColor: ColorConstants.blackColor,
                          )),
                      Widgets.heightSpaceH2,
                      Obx(() => CustomDropdown(
                            onTap: () {
                              showBottomSheetPicker(
                                title: "Select Smoking Status",
                                items: [
                                  "Never",
                                  "Occasional",
                                  '1-2 per week',
                                  '3-4 per week',
                                  'Daily'
                                ],
                                onItemSelected: (selected) {
                                  controller.alcoholConsumption.value = selected;
                                },
                              );
                            },
                            value: controller.alcoholConsumption.value,
                            hint: 'Alcohol Consumption',
                            label: 'Alcohol Consumption',
                            color: ColorConstants.transparentColor,
                            iconColor: ColorConstants.blackColor,
                          )),
                      Texts.textNormal(
                        size: 12,
                        textAlign: TextAlign.start,
                        "This helps us suggest appropriate food options for managing menopause symptoms",
                      ),
                      Widgets.heightSpaceH2,
                      CustomButton(
                        label: "Save changes",
                        textColor: ColorConstants.whiteColor,
                        backgroundColor: ColorConstants.darkPrimaryColor,
                      ),
                      Widgets.heightSpaceH2,
                      Texts.textBold('Privacy Setting', size: 18),
                      Widgets.heightSpaceH2,
                      ...List.generate(controller.options.length, (index) {
                        return Obx(() {
                          bool isSelected =
                              controller.selectedOption.value == index;
                          return GestureDetector(
                            onTap: () => controller.selectOption(index),
                            child: Container(
                              margin: const EdgeInsets.only(bottom: 12),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: isSelected
                                      ? ColorConstants.darkPrimaryColor
                                      : Colors.grey.shade300,
                                  width: isSelected ? 1 : 0.5,
                                ),
                              ),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Baseline(
                                    baseline: 22,
                                    baselineType: TextBaseline.alphabetic,
                                    child: Container(
                                      height: 25,
                                      width: 25,
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? ColorConstants.primaryColor
                                            : Colors.transparent,
                                        shape: BoxShape.circle,
                                        border: isSelected
                                            ? null
                                            : Border.all(
                                                color:
                                                    ColorConstants.greyBorderColor),
                                      ),
                                      child: isSelected
                                          ? Icon(Icons.check,
                                              color: Colors.white, size: 16)
                                          : null,
                                    ),
                                  ),
                                  Widgets.widthSpaceW2,
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Baseline(
                                          baseline: 16,
                                          baselineType: TextBaseline.alphabetic,
                                          child: Texts.textBold(controller.options[index],
                                              size: 16),
                                        ),
                                        Widgets.heightSpaceH05,
                                        Texts.textNormal(
                                          _getDescription(index),
                                          size: 12,
                                          textAlign: TextAlign.start,
                                          maxLines: 6,
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        });
                      }),
                      Widgets.heightSpaceH1,
                      CustomButton(
                        label: "Update privacy settings",
                        backgroundColor: ColorConstants.darkPrimaryColor,
                        textColor: Colors.white,
                      ),
                      Widgets.heightSpaceH2,
                      Texts.textBold("Data Management", size: 18),
                      Widgets.heightSpaceH1,
                      Texts.textNormal(
                        size: 12,
                        textAlign: TextAlign.start,
                        "Under GDPR, you have the right to access, export, and delete your personal data.",
                      ),
                      Widgets.heightSpaceH2,
                      CustomButton(
                        icon: Image.asset(
                          Assets.exportIcon,
                          height: 20,
                          width: 20,
                        ),
                        label: "Export my data",
                        backgroundColor: Colors.white,
                        borderColor: Colors.black,
                        textColor: Colors.black,
                      ),
                      Widgets.heightSpaceH2,
                      Widgets.divider(),
                      Widgets.heightSpaceH2,
                      Texts.textNormal(
                          "Delete your account and all associated data. This action cannot be undone.",
                          size: 12,
                          textAlign: TextAlign.start),
                      Widgets.heightSpaceH2,
                      CustomButton(
                        label: "Delete my account",
                        backgroundColor: ColorConstants.redTextColor,
                        icon: Image.asset(
                          Assets.deleteAccount,
                          color: ColorConstants.whiteColor,
                          height: 20,
                          width: 20,
                        ),
                        textColor: Colors.white,
                      ),
                    ],
                  ),
                ),
              ),
            ),

          ],
        ),
      ),
    );
  }
}
