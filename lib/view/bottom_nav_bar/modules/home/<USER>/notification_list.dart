import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/webcentered_widget.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:flutter/material.dart';

class NotificationList extends StatelessWidget {
  const NotificationList({super.key});

  @override
  Widget build(BuildContext context) {
    return  WebCenteredWrapper(
      child: Scaffold(
          appBar: AppBar(
            titleSpacing: 2,
            title: Texts.textBold("Notification Screen",size: 18),elevation: 0,scrolledUnderElevation: 0,backgroundColor: Colors.white,),
          body: ListView.builder(
              itemCount: 5,
              itemBuilder: (BuildContext context,int index){
            return SettingsTile(title: "Notification");
          })),
    );
  }
}

class SettingsTile extends StatelessWidget {
  final String title;
  const SettingsTile({super.key, required this.title,this.onTap});
  final VoidCallback? onTap;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Container(
        width: double.infinity,
        color: Colors.white,
        child: GestureDetector(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0,vertical: 18),
            child: Column(

              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Texts.textBold(title,size: 18,textAlign: TextAlign.start),
                 Widgets.heightSpaceH1,
                Texts.textNormal("Receive personalized health reminders, symptom updates, and motivation to help you feel your best—every single day",size: 12,textAlign: TextAlign.start,maxLines: 3,),

              ],),
          ),


        ),
      ),
    );
  }
}