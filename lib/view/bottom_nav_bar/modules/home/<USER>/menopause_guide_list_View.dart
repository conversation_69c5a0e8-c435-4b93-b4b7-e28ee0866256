import 'package:MenoPal/core/routes/app_routes.dart';
import 'package:MenoPal/core/widgets/list_tile_widget.dart';
import 'package:MenoPal/core/widgets/webcentered_widget.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/home/<USER>/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MenopauseGuideListView extends StatelessWidget {
   MenopauseGuideListView({super.key});
  final HomeController controller = Get.put(HomeController());
  @override
  Widget build(BuildContext context) {
    return  WebCenteredWrapper(
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(elevation: 0,scrolledUnderElevation: 0,backgroundColor: Colors.transparent,),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: ListView(
            children: List.generate(10, (index) {
              return GestureDetector(
                  onTap: ()=>Get.toNamed(AppRoutes.detailView),
                  child: ListTile_Widget());
            }),
          ),
        ),
      ),
    );
  }
}