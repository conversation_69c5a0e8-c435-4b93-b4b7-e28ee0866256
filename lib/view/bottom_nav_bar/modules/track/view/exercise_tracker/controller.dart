import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ExerciseController extends GetxController with GetSingleTickerProviderStateMixin {
  late TabController tabController;

  final selectedActivityType = ''.obs;
  final selectedActivity = ''.obs;

  final tabs = <Tab>[
    const Tab(text: 'Exercise'),
    const Tab(text: 'Past Entries'),
  ];

  void selectActivity(String activity) {
    selectedActivity.value = activity;
  }

  void selectActivityType(String value) {
    selectedActivityType.value = value;
  }

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: tabs.length, vsync: this);
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }
}
