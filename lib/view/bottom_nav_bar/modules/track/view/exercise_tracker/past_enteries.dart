import 'package:MenoPal/controller/date_time_controller.dart';
import 'package:MenoPal/core/constants/assets_constants.dart';
import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/constants/padding_constants.dart';
import 'package:MenoPal/core/widgets/entry_field.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:flutter/material.dart';


class ExercisePastEnteries extends StatelessWidget {
   ExercisePastEnteries({super.key});
  final TextEditingController dobController=TextEditingController();
  @override
  Widget build(BuildContext context) {
    return  GestureDetector(
      onTap: (){
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: ColorConstants.whiteColor,
        body: Padding(
        padding: PaddingConstants.screenPaddingHalf,
        child: Column(children: [

          EntryField(
            onTap: () => pickDateAndSetController(context: context, controller: dobController),
            readOnly: true,
            controller: dobController,
            prefixIcon: Assets.calendarIcon,
            hint: "07/04/2025",
          ),
      Widgets.heightSpaceH1,
          Container(
            decoration: BoxDecoration(
              color: ColorConstants.whiteColor,
              borderRadius: BorderRadius.circular(14),
              border: Border.all(color: ColorConstants.grayColor),

            ),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,

                  children: [
                  Texts.textBold("Walking",size: 18),
                    Image.asset(Assets.deleteIcon,height: 15,width: 15,),

                ],),
                Widgets.heightSpaceH2,
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                      Texts.textMedium('Time',size: 14),
                      Widgets.heightSpaceH05,
                      Texts.textNormal('1 hour 10 mins',size: 14),

                    ],),
                  ),
                  Widgets.widthSpaceW1,
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                      Texts.textMedium('Intensity',size: 14),
                      Widgets.heightSpaceH05,
                      Texts.textNormal('7/10',size: 14),

                    ],),
                  )
                ],)



              ],),
            ),
          )

        ],),
      ),),
    );
  }
}
