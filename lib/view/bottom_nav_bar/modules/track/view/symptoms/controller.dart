import 'package:get/get.dart';

class SymptomsController extends GetxController {
  final RxMap<String, bool> expandedMap = <String, bool>{}.obs;
  RxBool dropDown=false.obs;
  final Map<String, RxSet<String>> _selectedTriggers = {};

  void toggleExpanded(String title) {
    expandedMap[title] = !(isExpanded(title));
  }
  void toggleDropDown(){
    dropDown.value=!dropDown.value;



  }

  bool isExpanded(String title) => expandedMap[title] ?? false;

  // Trigger selection logic
  bool isSelected(String sectionKey, String label) {
    _selectedTriggers.putIfAbsent(sectionKey, () => <String>{}.obs);
    return _selectedTriggers[sectionKey]!.contains(label);
  }

  void toggleTrigger(String sectionKey, String label) {
    _selectedTriggers.putIfAbsent(sectionKey, () => <String>{}.obs);
    final set = _selectedTriggers[sectionKey]!;
    if (set.contains(label)) {
      set.remove(label);
    } else {
      set.add(label);
    }
  }
}
