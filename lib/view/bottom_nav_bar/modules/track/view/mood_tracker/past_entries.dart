import 'package:MenoPal/controller/date_time_controller.dart';
import 'package:MenoPal/core/constants/assets_constants.dart';
import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/widgets/entry_field.dart';
import 'package:MenoPal/core/widgets/radiobuttonwithtext.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/track/view/mood_tracker/controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class MoodPastEntries extends StatelessWidget {
   MoodPastEntries({super.key});

  final MoodController controller = Get.put(MoodController());



  final TextEditingController dobController=TextEditingController();

  @override
  Widget build(BuildContext context) {
    return  GestureDetector(
      onTap: (){
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: ColorConstants.whiteColor,
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: ListView(children: [
            Widgets.heightSpaceH1,
            EntryField(
              readOnly: true,
              controller: dobController,
              prefixIcon: Assets.calendarIcon,
              hint: "07/04/2025",
              onTap: () => pickDateAndSetController(context: context, controller: dobController),

            ),
            Widgets.heightSpaceH1,
            PastEntryCards(triggers: controller.triggers,title: "Happy",icon:Assets.unfilledHappy,controller:controller,),
            // PastEntryCards(triggers: trigger2,title: 'Sleep Issues',label:"Very Severe" ,labelColor: ColorConstants.redColor,),

          ],),
        ),
      ),
    );
  }
}

class PastEntryCards extends StatelessWidget {
  const PastEntryCards({
    super.key,
    required this.triggers,
    required this.title,
    required this.icon,
    this.labelColor,
    this.controller
  });

  final List<String> triggers;
  final String title;
  final String icon;
  final Color? labelColor;
  final controller;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: ColorConstants.greyBorderColor),
          borderRadius: BorderRadius.circular(20),

        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Texts.textBold(title,size: 18,textAlign: TextAlign.start),
                  Widgets.widthSpaceW1,
                  Image.asset(icon,height: 20,width: 20,),
                  Expanded(child:  Widgets.widthSpaceW1,),
                  Image.asset(Assets.deleteIcon,height: 16,),

                ],),
              Widgets.heightSpaceH1,

              Widgets.heightSpaceH1,
              Widgets.divider(),
              Widgets.heightSpaceH2,
              Texts.textBold("Triggers",size: 16),
              Widgets.heightSpaceH1,

              Wrap(
                children: List.generate(triggers.length, (index) {
                  final label = triggers[index];
                  final sectionKey = "${label}_LifeStylee";

                  return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Obx(() => RadioBtnWithTextChip(
                      label: label,
                      isSelected: controller.isSelected(sectionKey, label),
                      onTap: () => controller.toggleTrigger(sectionKey, label),
                    )),
                  );
                }),
              ),



            ],
          ),
        ),
      ),
    );
  }
}
