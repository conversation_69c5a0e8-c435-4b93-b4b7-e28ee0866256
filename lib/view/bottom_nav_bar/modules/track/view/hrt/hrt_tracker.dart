import 'package:MenoPal/controller/date_time_controller.dart';
import 'package:MenoPal/core/constants/assets_constants.dart';
import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/constants/padding_constants.dart';
import 'package:MenoPal/core/widgets/entry_field.dart';
import 'package:MenoPal/core/widgets/medication_card.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/webcentered_widget.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../../../core/routes/app_routes.dart';



class HrtTracker extends StatelessWidget {
   HrtTracker({super.key});
  final TextEditingController dobController=TextEditingController();

  @override
  Widget build(BuildContext context) {
    return WebCenteredWrapper(
      child: GestureDetector(
        onTap: (){
          FocusScope.of(context).unfocus();
        },
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                color: ColorConstants.whiteColor,
              ),
            ),
            Container(
              height: 600,
              decoration: BoxDecoration(

                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [
                    ColorConstants.gradient1.withAlpha((0.5 * 255).toInt()),
                    ColorConstants.gradient2.withAlpha((0.5 * 255).toInt()),
                    Colors.transparent,
                  ],

                ),
              ),
            ),
            Scaffold(
              backgroundColor: Colors.transparent,
              floatingActionButton: Padding(
                padding: const EdgeInsets.only(left: 16.0, bottom: 10.0),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Texts.textNormal("Add Medication", size: 14),
                   Widgets.widthSpaceW2,
                    FloatingActionButton.small(

                      elevation: 0,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(50)),
                      backgroundColor: ColorConstants.darkPrimaryColor,
                      onPressed: () {
                        Get.toNamed(AppRoutes.addMedicationMedication);
                      },
                      child: Icon(
                        Icons.add,
                        color: ColorConstants.whiteColor,
                      ),
                    ),
                  ],
                ),
              ),
              appBar: AppBar(
                elevation: 0,
                scrolledUnderElevation: 0,
                backgroundColor: Colors.transparent,
                leading: BackButton(),
              ),
              body: Padding(
                padding: PaddingConstants.screenPaddingHalf,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Texts.textBold("HRT Tracker", size: 18),
                      Widgets.heightSpaceH05,
                      Texts.textNormal(
                          "Track your HRT to spot patterns and support better health decisions",
                          textAlign: TextAlign.start,
                          size: 12,
                          maxLines: 5),
                      Widgets.heightSpaceH2,
                      EntryField(
                        readOnly: true,
                        controller: dobController,
                        prefixIcon: Assets.calendarIcon,
                        hint: "07/04/2025",
                        onTap: () => pickDateAndSetController(context: context, controller: dobController),

                      ),
                      Widgets.heightSpaceH1,
                      Texts.textBold("Current Medications(1)", size: 18),
                      Widgets.heightSpaceH1,
                      MedicationCard(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
