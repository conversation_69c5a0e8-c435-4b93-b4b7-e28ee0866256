import 'package:get/get.dart';

class MedicationController extends GetxController{

  var customMethodOfAdministration=''.obs;
  var customFrequency=''.obs;

  var selectedOption = (-1).obs;
  var selectedCategory = ''.obs;
  var selectedMedication= ''.obs;
  var selectedDosage= ''.obs;
  var selectedMethod= ''.obs;
  var selectedFrequency= ''.obs;
  void selectOption(int index) {
    selectedOption.value = index;
  }



}