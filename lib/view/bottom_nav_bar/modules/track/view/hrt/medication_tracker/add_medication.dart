import 'package:MenoPal/controller/date_time_controller.dart';
import 'package:MenoPal/core/constants/assets_constants.dart';
import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/constants/padding_constants.dart';
import 'package:MenoPal/core/routes/app_routes.dart';
import 'package:MenoPal/core/widgets/bottom_sheet.dart';
import 'package:MenoPal/core/widgets/custom_button.dart';
import 'package:MenoPal/core/widgets/custom_dropdown.dart';
import 'package:MenoPal/core/widgets/entry_field.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/webcentered_widget.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/track/view/hrt/medication_tracker/controller.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

class AddMedicationMedication extends StatelessWidget {
   AddMedicationMedication({super.key});
  final TextEditingController dobController=TextEditingController();
   MedicationController controller=Get.put(MedicationController());
  @override
  Widget build(BuildContext context) {
    return  WebCenteredWrapper(
      child: GestureDetector(
        onTap: (){
          FocusScope.of(context).unfocus();
        },
        child: Stack(
          children: [
            Container(
              decoration: BoxDecoration(
                color: ColorConstants.whiteColor,
              ),
            ),
            Container(
              height: 600,
              decoration: BoxDecoration(

                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [
                    ColorConstants.gradient1.withAlpha((0.5 * 255).toInt()),
                    ColorConstants.gradient2.withAlpha((0.5 * 255).toInt()),
                    Colors.transparent,
                  ],

                ),
              ),
            ),

            Scaffold(
              backgroundColor: ColorConstants.transparentColor,
              appBar: AppBar(

                elevation: 0,scrolledUnderElevation: 0,backgroundColor: Colors.transparent,),
              body: Padding(
                padding: PaddingConstants.screenPaddingHalf,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                    Texts.textBold("Add Medication",size: 18),
                    Widgets.heightSpaceH1,
                    Texts.textNormal("Add a medication that’s not in our database. You can  also include a photo so we can add it for future users.",textAlign: TextAlign.start,size: 12),
                      Widgets.heightSpaceH1,
                      Widgets.divider(),
                      Widgets.heightSpaceH1,
                      Obx(()=>CustomDropdown(
                        onTap: () {
                          showBottomSheetPicker(

                            title: "Select Category",
                            items: ["Estrogen medications1",'Estrogen medications2',"Estrogen medications3"], onItemSelected: (selected) {
                            controller.selectedCategory.value = selected;
                          },);
                        },
                        value: controller.selectedCategory.value,
                        hint: 'select category',

                        label: "Category",
                        color: ColorConstants.whiteColor,
                        iconColor: ColorConstants.blackColor,
                      ),),
                      Widgets.heightSpaceH05,
                      Obx(()=>CustomDropdown(
                        onTap: () {
                          showBottomSheetPicker(

                            title: "Select Medication",
                            items: ["Ovestin Cream",'Ovestin Cream2',"Ovestin Cream3"], onItemSelected: (selected) {
                            controller.selectedMedication.value = selected;
                          },);
                        },
                        value: controller.selectedMedication.value,
                        hint: 'select medication',

                        label: "Medication",
                        color: ColorConstants.whiteColor,
                        iconColor: ColorConstants.blackColor,
                      ),),
                      Widgets.heightSpaceH1,
                      Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(colors: [
                            ColorConstants.primaryColor.withAlpha((0.5*255).toInt()),
                            ColorConstants.primaryColor.withAlpha((0.3*255).toInt()),
                            ColorConstants.whiteColor

                          ]),
                          borderRadius:BorderRadius.circular(16),
                          border: Border.all(color: ColorConstants.darkPrimaryColor),
                        ),
                        child: Padding(
                          padding: EdgeInsets.symmetric(vertical: 12,horizontal: 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                          Image.asset(Assets.infoIcon,width: 20,height: 20,),
                            Widgets.widthSpaceW1
                              ,
                            Texts.textBold('Dosage Information',size: 18,color: ColorConstants.darkPrimaryColor),
                          ],),
                              Widgets.heightSpaceH1,
                              RichText(text: TextSpan(text: 'Initial dose: ',style: TextStyle(color: Colors.black,fontWeight: FontWeight.bold,fontFamily: "Montserrat",fontSize: 14),children: [
                                TextSpan(text: 'Apply 1 applicatorful (0.5 mg estriol) daily for the first 2 to 3 weeks. Maintenance dose: After initial treatment, reduce to 1 applicatorful twice a week.',style: TextStyle(fontWeight: FontWeight.normal,fontFamily: 'Montserrat',fontSize: 12)),

                              ])),

                            ],
                          ),
                        ),
                      ),
                      Widgets.heightSpaceH2,


                      Obx(()=>CustomDropdown(
                        onTap: () {
                          showBottomSheetPicker(

                            title: "Select Dosage",
                            items: ["Initial: 1 applicatorful (0.5 mg estriol) daily",'Initial: 2 applicatorful (0.5 mg estriol) daily',"Initial: 3 applicatorful (0.5 mg estriol) daily"], onItemSelected: (selected) {
                            controller.selectedDosage.value = selected;
                          },);
                        },
                        value: controller.selectedDosage.value,
                        hint: 'select dosage',

                        label: "Dosage",
                        color: ColorConstants.whiteColor,
                        iconColor: ColorConstants.blackColor,
                      ),),



                      Widgets.heightSpaceH05,

                      Obx(()=>CustomDropdown(
                        onTap: () {
                          showBottomSheetPicker(

                            title: "Select Method",
                            items: ["Cream","Gel","Patch","Spray","Tablet","Capsule","Vaginal","Pessary","Ring","Lozenge","Injection","IUS","Supplement","Other"],







                            onItemSelected: (selected) {
                            controller.selectedMethod.value = selected;
                          },);
                        },
                        value: controller.selectedMethod.value,
                        hint: 'select method',

                        label: "Method",
                        color: ColorConstants.whiteColor,
                        iconColor: ColorConstants.blackColor,
                      ),),


                      Widgets.heightSpaceH05,

                      Obx(()=>CustomDropdown(
                        onTap: () {
                          showBottomSheetPicker(

                            title: "Select Frequency",
                            items: ["daily",'weekly',"monthly"], onItemSelected: (selected) {
                            controller.selectedFrequency.value = selected;
                          },);
                        },
                        value: controller.selectedFrequency.value,
                        hint: 'select frequency',

                        label: "frequency",
                        color: ColorConstants.whiteColor,
                        iconColor: ColorConstants.blackColor,
                      ),),
                      EntryField(
                        readOnly: true,
                    controller: dobController,
                        onTap: () => pickDateAndSetController(context: context, controller: dobController),

                  label: 'Start Date',
                        prefixIcon: Assets.calendarIcon,
                        hint: "07/04/2025",
                      ),
                      Widgets.heightSpaceH05,
                  EntryBigField(label: 'Additional Notes',hint: "Addition notes  about this medication",),
                      Widgets.heightSpaceH1,
                  CustomButton(
                    backgroundColor: ColorConstants.darkPrimaryColor,
                    label: "Add medication",
                  ),
                      Widgets.heightSpaceH2,
                      CustomButton(label: "Add custom medication",borderColor: ColorConstants.blackColor,textColor: ColorConstants.blackColor,onTap: (){
                        Get.toNamed(AppRoutes.addcustomMedication);
                      },),
                    ],),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
