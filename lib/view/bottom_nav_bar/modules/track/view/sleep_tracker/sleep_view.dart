import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/track/view/sleep_tracker/sleep_past_enteries.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/track/view/sleep_tracker/sleep_tracker.dart';
import 'package:flutter/material.dart';

import '../../../../../../core/widgets/webcentered_widget.dart';

class SleepView extends StatefulWidget {
  const SleepView({super.key});

  @override
  SleepViewState createState() => SleepViewState();
}

class SleepViewState extends State<SleepView> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<Tab> myTabs = <Tab>[
    Tab(text: 'Sleep'),
    Tab(text: 'Past Entries'),

  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: myTabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return WebCenteredWrapper(
      child: Stack(

        children: [
          Container(
            height: 200,
            decoration: BoxDecoration(

              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  ColorConstants.gradient1.withAlpha((0.9 * 255).toInt()),

                  ColorConstants.gradient2.withAlpha((0.9 * 255).toInt()),


                ],

              ),
            ),
          ),
          Scaffold(
            backgroundColor: ColorConstants.transparentColor,
            appBar: AppBar(
              backgroundColor: ColorConstants.transparentColor,
              leading: BackButton(),
            ),
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(padding: EdgeInsets.symmetric(horizontal: 16),child: Texts.textBold("Sleep Tracker",size: 20),),
                Widgets.heightSpaceH1,
                Padding(padding: EdgeInsets.symmetric(horizontal: 16),child: Texts.textNormal("Track your sleep to spot what's helping or disrupting your rest.",textAlign: TextAlign.start,size: 14),
                ),
                Widgets.heightSpaceH1,
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: TabBar(
                    labelStyle: TextStyle(                     // Style for selected tab
                        fontSize: 14,
                        fontWeight: FontWeight.normal,
                        fontFamily: "Montserrat"
                    ),
                    padding: EdgeInsets.zero,
                    controller: _tabController,
                    labelColor: Colors.white,
                    unselectedLabelColor: Colors.black,
                    indicatorColor: ColorConstants.primaryColor,
                    indicatorSize: TabBarIndicatorSize.tab,
                    tabs: myTabs,
                  ),
                ),
                Widgets.heightSpaceH1,
                Expanded(
                  child: TabBarView(

                    controller: _tabController,
                    children: [
                      SleepTracker(),
                      SleepPastEnteries(),

                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}


