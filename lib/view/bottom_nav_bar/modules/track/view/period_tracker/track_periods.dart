import 'package:MenoPal/core/constants/assets_constants.dart';
import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/constants/padding_constants.dart';
import 'package:MenoPal/core/widgets/custom_button.dart';
import 'package:MenoPal/core/widgets/custom_slider.dart';
import 'package:MenoPal/core/widgets/entry_field.dart';
import 'package:MenoPal/core/widgets/radiobuttonwithtext.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:MenoPal/view/bottom_nav_bar/modules/track/view/period_tracker/controller.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';

import '../../../../../../controller/date_time_controller.dart';

class TrackPeriods extends StatelessWidget {
  TrackPeriods({super.key});
  TextEditingController dobController = TextEditingController();
  TextEditingController customSymptomsController = TextEditingController();
  PeriodTrackerController controller = Get.put(PeriodTrackerController());
//List<String> symptomsList=["Cramps","Bloating","Headache",'Fatigue','Mood Swings',"Backache","Breast Tenderness"];

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: ColorConstants.whiteColor,
        body: Padding(
          padding: PaddingConstants.screenPaddingHalf,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                EntryField(
                  readOnly: true,
                  controller: dobController,
                  prefixIcon: Assets.calendarIcon,
                  hint: "07/04/2025",
                  onTap: () => pickDateAndSetController(
                      context: context, controller: dobController),
                ),
                Texts.textMedium("Flow Intensity", size: 14),
                SeveritySlider(
                  labels: [
                    "Spotting",
                    "Light",
                    "Medium",
                    'Heavy',
                    "Very Heavy"
                  ],
                ),
                Widgets.heightSpaceH2,
                Texts.textMedium("Symptoms", size: 14),
                Widgets.heightSpaceH05,
                Obx(() {
                  return Wrap(
                    children:
                        List.generate(controller.symptomsList.length, (index) {
                      final label = controller.symptomsList[index];
                      final sectionKey = "${label}Symptoms";

                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Obx(() => RadioBtnWithTextChip(
                              label: label,
                              isSelected:
                                  controller.isSelected(sectionKey, label),
                              onTap: () =>
                                  controller.toggleTrigger(sectionKey, label),
                            )),
                      );
                    }),
                  );
                }),
                Widgets.heightSpaceH2,
                Texts.textMedium("Custom Symptoms", size: 14),
                Widgets.heightSpaceH05,
                Row(
                  children: [
                    Expanded(
                        flex: 3,
                        child: EntryField(
                          controller: customSymptomsController,
                          hint: "Enter custom symptom",
                        )),
                    Widgets.widthSpaceW2,
                    Expanded(
                        child: CustomButton(
                      onTap: () {
                        controller.symptomsList
                            .add(customSymptomsController.text.toString());
                        customSymptomsController.text = '';
                        FocusScope.of(context).unfocus();
                      },
                      height: 40,
                      backgroundColor: ColorConstants.darkPrimaryColor,
                      label: "Add",
                      textColor: ColorConstants.whiteColor,
                    )),
                  ],
                ),
                Widgets.heightSpaceH05,
                EntryField(
                  label: 'Notes',
                  hint: 'Add any personal observation or additional details',
                ),
                Widgets.heightSpaceH2,
                CustomButton(
                  backgroundColor: ColorConstants.darkPrimaryColor,
                  label: "Mark period start",
                  color: ColorConstants.whiteColor,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
