import 'package:MenoPal/core/constants/color_constants.dart';
import 'package:MenoPal/core/constants/padding_constants.dart';
import 'package:MenoPal/core/widgets/text_widgets.dart';
import 'package:MenoPal/core/widgets/widgets.dart';
import 'package:flutter/material.dart';

import '../../../../core/constants/assets_constants.dart';
import '../../../../core/widgets/doctor_card.dart';
import '../../../../core/widgets/entry_field.dart';


class ConsultView extends StatelessWidget {
  ConsultView({super.key});
  final specialists = [
    {
      "name": "Dr. <PERSON>",
      "specialty": "Menopause Specialist & Gynecologist",
      "location": "London, UK",
      "experience": "15 years experience",
      "rating": "4.9",
      "available": ["In Person", "Online", "Corporate"]
    },

    {
      "name": "Dr. <PERSON>",
      "specialty": "Women’s Health Specialist",
      "location": "London, UK",
      "experience": "12 years experience",
      "rating": "4.7",
      "available": ["In Person", "Online"]
    },

    {
      "name": "Dr. <PERSON>",
      "specialty": "Endocrinologist",
      "location": "London, UK",
      "experience": "7 years experience",
      "rating": "4.4",
      "available": ["In Person", "Corporate"]
    },

    {
      "name": "Dr.Emma Collins",
      "specialty": "Menopause Specialist",
      "location": "London, UK",
      "experience": "8 years experience",
      "rating": "4.5",
      "available": ["Online"]
    },

  ];
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: ColorConstants.whiteColor,
        body: SafeArea(
          child: Padding(
            padding: PaddingConstants.screenPaddingHalf,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Widgets.heightSpaceH1,
                Texts.textBold('Find a Speacialist',
                    textAlign: TextAlign.start, size: 18),
                Widgets.heightSpaceH05,
                Texts.textNormal('Find a Speacialist',
                    textAlign: TextAlign.start, size: 12),
                Widgets.heightSpaceH1,
                EntrySearchField(
                  prefixIcon: Assets.searchbarIcon,

                  color: ColorConstants.blackColor,
                  hint: "Search By Name,Specialty,Location",
                ),
                Widgets.heightSpaceH1,
                Expanded(
                  child: ListView.builder(
                    itemCount: specialists.length,
                    itemBuilder: (context, index) {
                      final doc = specialists[index];
                      return DoctorCard(
                        doc: doc,
                        icon: Assets.corporateIcon,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
