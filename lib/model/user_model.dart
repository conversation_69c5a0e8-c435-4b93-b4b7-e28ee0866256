import '../view/musician/bottom_navigation/modules/home/<USER>/member_detail_model.dart';

class UserModel {
  int? id;
  String? name;
  String? email;
  String? emailVerifiedAt;
  String? role;
  int? isVerified;
  int? isActive;
  int? isApproved;
  int? availability;
  int? liveLocation;
  String? longitude;
  String? latitude;
  String? fcmToken;
  String? createdAt;
  String? updatedAt;
  String? imageUrl;
  Subscription? subscription;
  MusicianProfile? musicianProfile;
bool? hasSubscription;
  UserModel(
      {this.id,
        this.name,
        this.email,
        this.emailVerifiedAt,
        this.role,
        this.isVerified,
        this.isActive,
        this.isApproved,
        this.availability,
        this.liveLocation,
        this.longitude,this.musicianProfile,
        this.latitude,
        this.fcmToken,
        this.createdAt,
        this.updatedAt,
        this.imageUrl,
        this.subscription});

  UserModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    emailVerifiedAt = json['email_verified_at'];
    role = json['role'];musicianProfile = json['musician_profile'] != null
        ? new MusicianProfile.fromJson(json['musician_profile'])
        : null;
    isVerified = json['is_verified'];
   hasSubscription = json['has_active_subscription']??false;
    isActive = json['is_active']??0;
    isApproved = json['is_approved']??0;
    availability = json['availability']??0;
    liveLocation = json['live_location'];
    longitude = json['longitude']!=null?json['longitude'].toString():"0";
    latitude = json['latitude']!=null?json['latitude'].toString():"0";
    fcmToken = json['fcm_token'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    imageUrl = json['image_url'];
    subscription = json['subscription'] != null
        ? new Subscription.fromJson(json['subscription'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;data['has_active_subscription'] = this.hasSubscription;
    data['email'] = this.email;
    data['email_verified_at'] = this.emailVerifiedAt;
    data['role'] = this.role;data['musician_profile'] = this.musicianProfile;
    data['is_verified'] = this.isVerified;
    data['is_active'] = this.isActive;
    data['is_approved'] = this.isApproved;
    data['availability'] = this.availability;
    data['live_location'] = this.liveLocation;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    data['fcm_token'] = this.fcmToken;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['image_url'] = this.imageUrl;
    data['subscription'] = this.subscription;
    return data;
  }
}
class MuscianProfile {
  int? id;
  int? userId;
  String? phoneNumber;
  String? website;
  String? description;
  String? headerImage;
  List<String>? roles;
  List<String>? offeredServices;
  List<String>? instruments;
  List<String>? musicTypes;
  List<String>? spokenLanguages;
  List<String>? paymentMethods;
  String? ratePerEvent;
  String? ratePerHour;
  List<String>? socialLinks;
  List<String>? tags;
  String? location;
  String? createdAt;
  String? updatedAt;
  String? headerImageUrl;

  MuscianProfile(
      {this.id,
        this.userId,
        this.phoneNumber,
        this.website,
        this.description,
        this.headerImage,
        this.roles,
        this.offeredServices,
        this.instruments,
        this.musicTypes,
        this.spokenLanguages,
        this.paymentMethods,
        this.ratePerEvent,
        this.ratePerHour,
        this.socialLinks,
        this.tags,
        this.location,
        this.createdAt,
        this.updatedAt,
        this.headerImageUrl});

  MuscianProfile.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    phoneNumber = json['phone_number'];
    website = json['website'];
    description = json['description'];
    headerImage = json['header_image'];
    roles = json['roles'] != null ? json['roles'].cast<String>() : [];
    offeredServices = json['offered_services'] != null ? json['offered_services'].cast<String>() : [];
    instruments = json['instruments'] != null ? json['instruments'].cast<String>() : [];
    musicTypes = json['music_types'] != null ? json['music_types'].cast<String>() : [];
    spokenLanguages = json['spoken_languages'] != null ? json['spoken_languages'].cast<String>() : [];
    paymentMethods = json['payment_methods'] != null ? json['payment_methods'].cast<String>() : [];
    ratePerEvent = json['rate_per_event'];
    ratePerHour = json['rate_per_hour'];
    socialLinks = json['social_links'] != null ? json['social_links'].cast<String>() : [];
    tags = json['tags'] != null ? json['tags'].cast<String>() : [];
    location = json['location'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    headerImageUrl = json['header_image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['phone_number'] = this.phoneNumber;
    data['website'] = this.website;
    data['description'] = this.description;
    data['header_image'] = this.headerImage;
    data['roles'] = this.roles;
    data['offered_services'] = this.offeredServices;
    data['instruments'] = this.instruments;
    data['music_types'] = this.musicTypes;
    data['spoken_languages'] = this.spokenLanguages;
    data['payment_methods'] = this.paymentMethods;
    data['rate_per_event'] = this.ratePerEvent;
    data['rate_per_hour'] = this.ratePerHour;
    data['social_links'] = this.socialLinks;
    data['tags'] = this.tags;
    data['location'] = this.location;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['header_image_url'] = this.headerImageUrl;
    return data;
  }
}
class Subscription {
  int? id;
  int? userId;
  String? status;
  String? trialEndsAt;
  String? endsAt;
  String? nextBillingDate;
  bool? cancelAtPeriodEnd;
  String? formattedNextBillingDate;
  String? formattedTrialEndsAt;
  String? formattedEndsAt;

  Subscription(
      {this.id,
        this.userId,
        this.status,
        this.trialEndsAt,
        this.endsAt,
        this.nextBillingDate,
        this.cancelAtPeriodEnd,
        this.formattedNextBillingDate,
        this.formattedTrialEndsAt,
        this.formattedEndsAt});

  Subscription.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    status = json['status'];
    trialEndsAt = json['trial_ends_at'];
    endsAt = json['ends_at'];
    nextBillingDate = json['next_billing_date'];
    cancelAtPeriodEnd = json['cancel_at_period_end'];
    formattedNextBillingDate = json['formatted_next_billing_date'];
    formattedTrialEndsAt = json['formatted_trial_ends_at'];
    formattedEndsAt = json['formatted_ends_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['user_id'] = this.userId;
    data['status'] = this.status;
    data['trial_ends_at'] = this.trialEndsAt;
    data['ends_at'] = this.endsAt;
    data['next_billing_date'] = this.nextBillingDate;
    data['cancel_at_period_end'] = this.cancelAtPeriodEnd;
    data['formatted_next_billing_date'] = this.formattedNextBillingDate;
    data['formatted_trial_ends_at'] = this.formattedTrialEndsAt;
    data['formatted_ends_at'] = this.formattedEndsAt;
    return data;
  }
}
