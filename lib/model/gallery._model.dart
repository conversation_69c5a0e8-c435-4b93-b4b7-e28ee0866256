class Gallery {
  int? id;
  int? musicianProfileId;
  String? image;
  String? createdAt;
  String? updatedAt;
  String? imageUrl;

  Gallery(
      {this.id,
      this.musicianProfileId,
      this.image,
      this.createdAt,
      this.updatedAt,
      this.imageUrl});

  Gallery.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    musicianProfileId = json['musician_profile_id'];
    image = json['image'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    imageUrl = json['image_url'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['musician_profile_id'] = this.musicianProfileId;
    data['image'] = this.image;
    data['created_at'] = this.createdAt;
    data['updated_at'] = this.updatedAt;
    data['image_url'] = this.imageUrl;
    return data;
  }
}
