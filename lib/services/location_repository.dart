import 'dart:async';
import 'dart:developer';
import 'package:flutter_background_geolocation/flutter_background_geolocation.dart' as bg;
import 'package:guardtracker/services/background_location_service.dart';

class LocationRepository {
  static final LocationRepository _instance = LocationRepository._internal();
  factory LocationRepository() => _instance;
  LocationRepository._internal();

  final BackgroundLocationService _locationService = BackgroundLocationService();
  
  StreamSubscription<LocationServiceStatus>? _statusSubscription;
  StreamSubscription<bg.Location>? _locationSubscription;
  
  final StreamController<LocationData> _locationController = 
      StreamController<LocationData>.broadcast();
  final StreamController<LocationServiceStatus> _statusController = 
      StreamController<LocationServiceStatus>.broadcast();

  // Public streams
  Stream<LocationData> get locationStream => _locationController.stream;
  Stream<LocationServiceStatus> get statusStream => _statusController.stream;

  bool get isInitialized => _locationService.isInitialized;
  bool get isTracking => _locationService.isTracking;

  Future<LocationServiceResult> initialize() async {
    try {
      final result = await _locationService.initialize();
      
      if (result.isSuccess) {
        _setupLocationListeners();
      }
      
      return result;
    } catch (e) {
      log('LocationRepository initialization failed: $e');
      return LocationServiceResult.failure('Repository initialization failed: $e');
    }
  }

  void _setupLocationListeners() {
    // Listen to location updates
    _locationSubscription = _locationService.locationStream.listen(
      (bg.Location bgLocation) {
        final locationData = LocationData.fromBackgroundLocation(bgLocation);
        _locationController.add(locationData);
        log('LocationRepository: Location update forwarded');
      },
      onError: (error) {
        log('LocationRepository: Location stream error: $error');
      },
    );

    // Listen to status updates
    _statusSubscription = _locationService.statusStream.listen(
      (LocationServiceStatus status) {
        _statusController.add(status);
        log('LocationRepository: Status update forwarded: ${status.state}');
      },
      onError: (error) {
        log('LocationRepository: Status stream error: $error');
      },
    );
  }

  Future<LocationServiceResult> startTracking() async {
    if (!isInitialized) {
      final initResult = await initialize();
      if (!initResult.isSuccess) {
        return initResult;
      }
    }

    return await _locationService.startTracking();
  }

  Future<LocationServiceResult> stopTracking() async {
    return await _locationService.stopTracking();
  }

  Future<LocationData?> getCurrentLocation() async {
    try {
      final bgLocation = await _locationService.getCurrentLocation();
      if (bgLocation != null) {
        return LocationData.fromBackgroundLocation(bgLocation);
      }
      return null;
    } catch (e) {
      log('Failed to get current location: $e');
      return null;
    }
  }

  Future<void> syncPendingLocations() async {
    await _locationService.sync();
  }

  Future<int> getPendingLocationCount() async {
    return await _locationService.getCount();
  }

  Future<void> clearStoredLocations() async {
    await _locationService.destroyLocations();
  }

  void dispose() {
    _locationSubscription?.cancel();
    _statusSubscription?.cancel();
    _locationController.close();
    _statusController.close();
    _locationService.dispose();
  }
}

// Location data model compatible with existing code
class LocationData {
  final double latitude;
  final double longitude;
  final DateTime timestamp;
  final double? accuracy;
  final double? altitude;
  final double? speed;
  final double? heading;
  final String? activity;
  final double? batteryLevel;
  final bool? isCharging;

  LocationData({
    required this.latitude,
    required this.longitude,
    required this.timestamp,
    this.accuracy,
    this.altitude,
    this.speed,
    this.heading,
    this.activity,
    this.batteryLevel,
    this.isCharging,
  });

  factory LocationData.fromBackgroundLocation(bg.Location bgLocation) {
    return LocationData(
      latitude: bgLocation.coords.latitude,
      longitude: bgLocation.coords.longitude,
      timestamp: DateTime.parse(bgLocation.timestamp),
      accuracy: bgLocation.coords.accuracy,
      altitude: bgLocation.coords.altitude,
      speed: bgLocation.coords.speed,
      heading: bgLocation.coords.heading,
      activity: bgLocation.activity.type,
      batteryLevel: bgLocation.battery.level,
      isCharging: bgLocation.battery.isCharging,
    );
  }

  Map<String, dynamic> toJson() => {
        'latitude': latitude,
        'longitude': longitude,
        'timestamp': timestamp.toIso8601String(),
        'accuracy': accuracy,
        'altitude': altitude,
        'speed': speed,
        'heading': heading,
        'activity': activity,
        'battery_level': batteryLevel,
        'is_charging': isCharging,
      };

  bool isValid() {
    return latitude.abs() <= 90 &&
        longitude.abs() <= 180 &&
        (accuracy == null || accuracy! < 1000);
  }

  @override
  String toString() =>
      'LocationData(${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)})';
}